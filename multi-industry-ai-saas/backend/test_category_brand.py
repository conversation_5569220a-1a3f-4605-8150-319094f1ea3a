#!/usr/bin/env python3
"""
测试分类和品牌字段传递
"""

import asyncio
import uuid
import logging
from typing import Dict, Any

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 测试配置
TEST_PROJECT_ID = "66e74880-0f4c-4b56-bcae-12691bdbb59e"
TEST_USER_ID = "01932a18-8a66-7f20-8e37-f39b29bef0a1"
TEST_FILE_ID = "d09595ab-5c4b-4231-bfd0-dc30e697d1b3"

async def test_category_brand_fields():
    """测试分类和品牌字段传递"""
    
    print("=" * 60)
    print("🧪 测试分类和品牌字段传递")
    print("=" * 60)
    
    try:
        # 导入必要的模块
        from tasks.enhanced_purchase_order_task_executor import EnhancedPurchaseOrderTaskExecutor
        from models.task import AsyncTask
        from uuid import UUID
        
        # 创建模拟任务
        mock_task = AsyncTask(
            id=str(uuid.uuid4()),
            project_id=UUID(TEST_PROJECT_ID),
            tenant_id=UUID(TEST_PROJECT_ID),
            user_id=UUID(TEST_USER_ID),
            task_type="purchase_order_ai_preview",
            task_name="分类品牌测试任务",
            input_data={
                "file_id": TEST_FILE_ID,
                "upload_type": "both",
                "processing_mode": "auto"
            }
        )
        
        # 执行任务
        print("🔄 执行增强版任务执行器...")
        executor = EnhancedPurchaseOrderTaskExecutor()
        task_result = await executor.execute_task(mock_task, test_mode=True)
        
        if task_result.get('success'):
            frontend_data = task_result['data']
            purchase_items = frontend_data.get('preview', {}).get('purchase_items', [])
            
            print(f"✅ 任务执行成功，获得 {len(purchase_items)} 个采购商品")
            
            # 检查分类和品牌字段
            category_count = 0
            brand_count = 0
            
            print("\n📦 采购商品分类和品牌信息:")
            for i, item in enumerate(purchase_items[:5]):  # 只显示前5个
                product_name = item.get('product_name', '')
                category = item.get('suggested_category', '')
                brand = item.get('suggested_brand', '')
                
                print(f"  {i+1}. {product_name}")
                print(f"     分类: {category if category else '未识别'}")
                print(f"     品牌: {brand if brand else '未识别'}")
                print()
                
                if category:
                    category_count += 1
                if brand:
                    brand_count += 1
            
            # 统计结果
            total_items = len(purchase_items)
            category_rate = (category_count / total_items * 100) if total_items > 0 else 0
            brand_rate = (brand_count / total_items * 100) if total_items > 0 else 0
            
            print(f"📊 分类和品牌识别统计:")
            print(f"   - 总商品数: {total_items}")
            print(f"   - 有分类的商品: {category_count} ({category_rate:.1f}%)")
            print(f"   - 有品牌的商品: {brand_count} ({brand_rate:.1f}%)")
            
            # 验证字段是否存在
            if total_items > 0:
                sample_item = purchase_items[0]
                has_category_field = 'suggested_category' in sample_item
                has_brand_field = 'suggested_brand' in sample_item
                
                print(f"\n🔍 字段存在性检查:")
                print(f"   - suggested_category 字段: {'✅ 存在' if has_category_field else '❌ 缺失'}")
                print(f"   - suggested_brand 字段: {'✅ 存在' if has_brand_field else '❌ 缺失'}")
                
                if has_category_field and has_brand_field:
                    print("\n🎉 分类和品牌字段传递成功！")
                    return True
                else:
                    print("\n❌ 分类和品牌字段传递失败！")
                    return False
            else:
                print("\n⚠️  没有采购商品数据")
                return False
        else:
            print(f"❌ 任务执行失败: {task_result.get('error')}")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    asyncio.run(test_category_brand_fields()) 