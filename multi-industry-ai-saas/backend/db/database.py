#!/usr/bin/env python
# -*- coding: utf-8 -*-

import logging
from typing import AsyncGenerator, Optional
from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker, configure_mappers
from sqlalchemy import text

from core.config import settings
from core.tenant_context import get_tenant_context
from db.base_class import Base

# 配置日志
logger = logging.getLogger(__name__)

# 创建异步引擎
engine = create_async_engine(
    settings.DATABASE_URL,
    echo=settings.SQL_ECHO,
    pool_size=settings.DB_POOL_SIZE,
    max_overflow=settings.DB_MAX_OVERFLOW,
    pool_pre_ping=True,
)

# 创建异步会话
AsyncSessionLocal = sessionmaker(
    engine, class_=AsyncSession, expire_on_commit=False
)


async def init_db():
    """
    初始化数据库
    """
    try:
        # 导入所有模型，确保它们都被注册
        import models

        # 配置映射器
        configure_mappers()

        # 创建公共Schema的表
        async with engine.begin() as conn:
            # 确保public schema存在
            await conn.execute(text("CREATE SCHEMA IF NOT EXISTS public"))

            # 创建所有表
            await conn.run_sync(Base.metadata.create_all)

        logger.info("数据库初始化成功")
    except Exception as e:
        logger.error(f"数据库初始化失败: {e}")
        raise


async def get_db() -> AsyncGenerator[AsyncSession, None]:
    """
    获取数据库会话
    注意：此函数已被 api/deps.py 中的 get_db 替代，使用 asynccontextmanager 实现
    保留此函数是为了向后兼容
    """
    # 导入所有模型，确保它们都被注册
    import models

    # 配置映射器
    configure_mappers()

    session = AsyncSessionLocal()
    try:
        yield session
    finally:
        await session.close()


async def get_tenant_db(tenant_schema: Optional[str] = None) -> AsyncGenerator[AsyncSession, None]:
    """
    获取租户数据库会话
    """
    # 如果没有指定租户Schema，则从上下文中获取
    if tenant_schema is None:
        tenant_context = get_tenant_context()
        if tenant_context and tenant_context.tenant_id:
            tenant_schema = f"tenant_{tenant_context.tenant_id.replace('-', '')}"
        else:
            # 如果没有租户上下文，则使用公共Schema
            async with AsyncSessionLocal() as session:
                yield session
            return

    # 创建租户会话
    async with AsyncSessionLocal() as session:
        try:
            # 设置当前会话的Schema
            await session.execute(text(f"SET search_path TO {tenant_schema}, public"))
            yield session
        finally:
            # 重置Schema为public
            await session.execute(text("SET search_path TO public"))


async def create_tenant_schema(tenant_id: str) -> str:
    """
    创建租户Schema
    """
    # 生成Schema名称
    schema_name = f"tenant_{tenant_id.replace('-', '')}"

    async with AsyncSessionLocal() as session:
        try:
            # 创建Schema
            await session.execute(text(f"CREATE SCHEMA IF NOT EXISTS {schema_name}"))
            await session.commit()

            logger.info(f"租户Schema创建成功: {schema_name}")
            return schema_name
        except Exception as e:
            await session.rollback()
            logger.error(f"租户Schema创建失败: {e}")
            raise


async def drop_tenant_schema(tenant_id: str) -> bool:
    """
    删除租户Schema
    """
    # 生成Schema名称
    schema_name = f"tenant_{tenant_id.replace('-', '')}"

    async with AsyncSessionLocal() as session:
        try:
            # 删除Schema
            await session.execute(text(f"DROP SCHEMA IF EXISTS {schema_name} CASCADE"))
            await session.commit()

            logger.info(f"租户Schema删除成功: {schema_name}")
            return True
        except Exception as e:
            await session.rollback()
            logger.error(f"租户Schema删除失败: {e}")
            raise
