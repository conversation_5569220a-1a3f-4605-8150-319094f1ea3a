#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
内容审核插件

这个插件提供内容审核功能，可以检测文本中的有害内容。
"""

import uuid
from typing import Dict, List, Any, Optional
from fastapi import APIRouter, Depends, HTTPException, Body, Path, Query
from fastapi.responses import JSONResponse
from pydantic import BaseModel

from plugins.base import BasePlugin


class ContentModerationRequest(BaseModel):
    """内容审核请求模型"""
    content: str
    context: Optional[Dict[str, Any]] = None


class ContentModerationResponse(BaseModel):
    """内容审核响应模型"""
    is_safe: bool
    categories: Dict[str, float]
    filtered_content: str


class ContentModerationPlugin(BasePlugin):
    """内容审核插件"""
    
    def get_id(self) -> str:
        return "ai-content-moderation"
    
    def get_name(self) -> str:
        return "内容审核"
    
    def get_description(self) -> str:
        return "提供AI内容审核功能，检测有害内容并进行过滤"
    
    def get_version(self) -> str:
        return "1.0.0"
    
    def get_author(self) -> str:
        return "Retail AI Team"
    
    def get_type(self) -> str:
        return "ai"
    
    def get_permissions(self) -> List[str]:
        return ["content_moderation"]
    
    def get_config_schema(self) -> Dict[str, Any]:
        return {
            "type": "object",
            "properties": {
                "sensitivity": {
                    "type": "string",
                    "enum": ["low", "medium", "high"],
                    "default": "medium",
                    "description": "审核敏感度"
                },
                "filter_mode": {
                    "type": "string",
                    "enum": ["replace", "remove", "tag"],
                    "default": "replace",
                    "description": "过滤模式"
                },
                "replacement_char": {
                    "type": "string",
                    "default": "*",
                    "description": "替换字符"
                }
            }
        }
    
    def get_default_config(self) -> Dict[str, Any]:
        return {
            "sensitivity": "medium",
            "filter_mode": "replace",
            "replacement_char": "*"
        }
    
    def _initialize_routes(self) -> None:
        """初始化插件路由"""
        
        @self.router.post("/moderate", response_model=ContentModerationResponse)
        async def moderate_content(
            request: ContentModerationRequest = Body(...),
            project_id: uuid.UUID = Path(..., description="项目ID")
        ):
            """
            审核内容
            
            检测文本中的有害内容并进行过滤
            """
            try:
                result = self.moderate_content(request.content, request.context)
                return result
            except Exception as e:
                raise HTTPException(status_code=500, detail=f"内容审核失败: {str(e)}")
        
        @self.router.get("/config", response_model=Dict[str, Any])
        async def get_config(
            project_id: uuid.UUID = Path(..., description="项目ID")
        ):
            """
            获取插件配置
            """
            return self.config or self.get_default_config()
    
    def moderate_content(self, content: str, context: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        内容审核功能
        
        Args:
            content: 要审核的内容
            context: 上下文信息
        
        Returns:
            Dict[str, Any]: 审核结果
        """
        # 这里是一个简单的实现，实际应用中应该使用更复杂的算法或调用外部API
        sensitive_words = ["敏感词1", "敏感词2", "敏感词3"]
        is_safe = True
        categories = {}
        filtered_content = content
        
        # 检查敏感词
        for word in sensitive_words:
            if word in content:
                is_safe = False
                categories[word] = 0.9  # 置信度
                
                # 根据过滤模式处理内容
                if self.config.get("filter_mode") == "replace":
                    replacement = self.config.get("replacement_char", "*") * len(word)
                    filtered_content = filtered_content.replace(word, replacement)
                elif self.config.get("filter_mode") == "remove":
                    filtered_content = filtered_content.replace(word, "")
                elif self.config.get("filter_mode") == "tag":
                    filtered_content = filtered_content.replace(word, f"[敏感内容:{word}]")
        
        return {
            "is_safe": is_safe,
            "categories": categories,
            "filtered_content": filtered_content
        }


# 创建插件实例
plugin = ContentModerationPlugin()
