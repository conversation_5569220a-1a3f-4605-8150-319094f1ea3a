#!/usr/bin/env python
# -*- coding: utf-8 -*-

import logging
import asyncio
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from sqlalchemy import and_, delete, text
import uuid
from datetime import datetime

from db.database import get_db, async_engine
from models.tenant import Tenant
from models.project import Project
from models.plugin import Plugin, TenantPlugin, PluginVersion

# 导入模型以确保表结构
from .models.models import MarketingGame, GamePrize, GameParticipant, GameRecord

# 初始化日志
logger = logging.getLogger(__name__)

# 插件信息
PLUGIN_INFO = {
    "code": "marketing_game",
    "name": "营销游戏插件",
    "description": "提供多种营销游戏活动，帮助客户线下互动，增强用户粘性。支持随机抽奖、幸运大转盘、刮刮卡、闯关游戏等多种游戏类型，支持奖品设置、限制抽奖次数、内定中奖人、自动识别小票等功能。",
    "version": "1.2.0",
    "type": "marketplace",
    "category": "营销",
    "icon_url": "/static/plugins/marketing_game/icon.png",
    "price": 0,  # 免费插件
    "author": "Retail AI Team",
    "homepage": None,
    "features": [
        "🎲 随机抽奖 - 支持概率设置和奖品配置",
        "🎡 幸运大转盘 - 炫酷转盘动画效果",
        "🃏 刮刮卡游戏 - 刮开涂层揭晓奖品",
        "🗺️ 闯关游戏 - 多关卡挑战模式",
        "🎁 奖品管理 - 灵活的奖品配置系统",
        "📊 中奖率控制 - 精确的概率算法",
        "👥 参与者管理 - 用户游戏记录统计",
        "🎯 内定中奖 - 支持指定用户中奖",
        "📱 小票识别 - AI识别小票信息",
        "📈 数据统计 - 详细的游戏数据分析"
    ]
}

async def create_marketing_game_tables():
    """创建营销游戏相关数据表"""
    try:
        logger.info("开始创建营销游戏数据表...")
        
        # 使用SQLAlchemy的元数据创建表
        from .models.models import Base
        async with async_engine.begin() as conn:
            # 只创建营销游戏相关的表
            tables_to_create = [
                MarketingGame.__table__,
                GamePrize.__table__,
                GameParticipant.__table__,
                GameRecord.__table__
            ]
            
            for table in tables_to_create:
                try:
                    await conn.run_sync(table.create, checkfirst=True)
                    logger.info(f"表 {table.name} 创建成功")
                except Exception as e:
                    logger.warning(f"表 {table.name} 创建失败或已存在: {str(e)}")
        
        logger.info("营销游戏数据表创建完成")
        return True
    except Exception as e:
        logger.error(f"创建营销游戏数据表失败: {str(e)}")
        return False

async def initialize_plugin(tenant_id=None, project_id=None):
    """初始化营销游戏插件数据库"""
    logger.info("初始化营销游戏插件数据库")

    # 首先创建数据表
    if not await create_marketing_game_tables():
        logger.error("数据表创建失败，插件初始化终止")
        return False

    async for db in get_db():
        try:
            # 检查插件是否已注册
            plugin_query = select(Plugin).where(Plugin.code == PLUGIN_INFO["code"])
            result = await db.execute(plugin_query)
            plugin = result.scalar_one_or_none()

            if not plugin:
                # 注册插件
                plugin = Plugin(
                    id=uuid.uuid4(),
                    code=PLUGIN_INFO["code"],
                    name=PLUGIN_INFO["name"],
                    description=PLUGIN_INFO["description"],
                    version=PLUGIN_INFO["version"],
                    type=PLUGIN_INFO["type"],
                    category=PLUGIN_INFO["category"],
                    icon_url=PLUGIN_INFO["icon_url"],
                    price=PLUGIN_INFO["price"],
                    is_system=False,
                    is_active=True,
                    author=PLUGIN_INFO["author"],
                    homepage=PLUGIN_INFO["homepage"],
                    installation_path="plugins.marketing_game",
                    entry_point="initialize",
                    created_at=datetime.now(),
                    settings_schema={
                        "properties": {
                            "enable_marketing_game": {
                                "type": "boolean",
                                "title": "启用营销游戏",
                                "default": True
                            },
                            "max_games_per_project": {
                                "type": "integer",
                                "title": "每个项目最大游戏数量",
                                "minimum": 1,
                                "maximum": 100,
                                "default": 10
                            },
                            "enable_prize_distribution": {
                                "type": "boolean",
                                "title": "启用奖品发放",
                                "default": True
                            },
                            "enable_ocr_recognition": {
                                "type": "boolean",
                                "title": "启用小票OCR识别",
                                "default": False
                            },
                            "default_win_rate": {
                                "type": "number",
                                "title": "默认中奖率（%）",
                                "minimum": 0,
                                "maximum": 100,
                                "default": 10
                            }
                        }
                    },
                    features=PLUGIN_INFO["features"]
                )
                db.add(plugin)
                await db.flush()

                # 创建插件版本
                version = PluginVersion(
                    id=uuid.uuid4(),
                    plugin_id=plugin.id,
                    version=PLUGIN_INFO["version"],
                    release_notes="升级版本，增强游戏体验和奖品管理功能",
                    is_latest=True,
                    released_at=datetime.now()
                )
                db.add(version)
                await db.commit()
                logger.info(f"营销游戏插件注册成功: {plugin.id}")
            else:
                # 更新插件信息
                plugin.name = PLUGIN_INFO["name"]
                plugin.description = PLUGIN_INFO["description"]
                plugin.version = PLUGIN_INFO["version"]
                plugin.type = PLUGIN_INFO["type"]
                plugin.category = PLUGIN_INFO["category"]
                plugin.icon_url = PLUGIN_INFO["icon_url"]
                plugin.price = PLUGIN_INFO["price"]
                plugin.author = PLUGIN_INFO["author"]
                plugin.homepage = PLUGIN_INFO["homepage"]
                plugin.installation_path = "plugins.marketing_game"
                plugin.entry_point = "initialize"
                plugin.is_active = True
                plugin.features = PLUGIN_INFO["features"]
                plugin.last_updated_at = datetime.now()

                await db.commit()
                logger.info(f"更新了营销游戏插件: {plugin.id}")

            # 如果指定了租户和项目，则为其初始化插件设置
            if tenant_id and project_id:
                await initialize_tenant_plugin(db, tenant_id, project_id, plugin.id)

            return True

        except Exception as e:
            logger.error(f"初始化营销游戏插件失败: {str(e)}")
            await db.rollback()
            raise
        finally:
            await db.close()

async def initialize_tenant_plugin(db: AsyncSession, tenant_id: str, project_id: str, plugin_id: str):
    """为租户初始化插件设置"""
    try:
        # 检查租户插件是否已存在
        tenant_plugin_query = select(TenantPlugin).where(
            and_(
                TenantPlugin.tenant_id == tenant_id,
                TenantPlugin.plugin_id == plugin_id
            )
        )
        result = await db.execute(tenant_plugin_query)
        tenant_plugin = result.scalar_one_or_none()

        if not tenant_plugin:
            # 创建租户插件关联
            tenant_plugin = TenantPlugin(
                id=uuid.uuid4(),
                tenant_id=tenant_id,
                plugin_id=plugin_id,
                is_active=True,
                settings={
                    "enable_marketing_game": True,
                    "max_games_per_project": 10,
                    "enable_prize_distribution": True,
                    "enable_ocr_recognition": False,
                    "default_win_rate": 10
                },
                installed_at=datetime.now()
            )
            db.add(tenant_plugin)
            await db.commit()
            logger.info(f"为租户 {tenant_id} 初始化营销游戏插件设置成功")
    except Exception as e:
        logger.error(f"为租户初始化营销游戏插件设置失败: {str(e)}")
        raise

async def clean_marketing_game_tables(db: AsyncSession):
    """清理营销游戏相关数据表（谨慎使用）"""
    try:
        logger.warning("开始清理营销游戏数据表...")
        
        # 按照外键依赖关系的逆序删除数据
        table_names = [
            "game_records",
            "game_participants", 
            "game_prizes",
            "marketing_games"
        ]
        
        for table_name in table_names:
            try:
                await db.execute(text(f"DELETE FROM {table_name}"))
                logger.info(f"清理表 {table_name} 数据成功")
            except Exception as e:
                logger.warning(f"清理表 {table_name} 数据失败: {str(e)}")
        
        await db.commit()
        logger.warning("营销游戏数据表清理完成")
        
    except Exception as e:
        logger.error(f"清理营销游戏数据表失败: {str(e)}")
        await db.rollback()
        raise

async def uninstall_plugin(tenant_id: str, project_id: str = None):
    """卸载营销游戏插件"""
    logger.info(f"开始卸载营销游戏插件，租户ID: {tenant_id}")

    async for db in get_db():
        try:
            # 获取插件信息
            plugin_query = select(Plugin).where(Plugin.code == PLUGIN_INFO["code"])
            result = await db.execute(plugin_query)
            plugin = result.scalar_one_or_none()

            if not plugin:
                logger.warning("营销游戏插件未找到")
                return False

            # 删除租户插件关联
            tenant_plugin_query = delete(TenantPlugin).where(
                and_(
                    TenantPlugin.tenant_id == tenant_id,
                    TenantPlugin.plugin_id == plugin.id
                )
            )
            await db.execute(tenant_plugin_query)

            # 可选：清理插件相关数据表（谨慎操作）
            # 注意：通常建议保留数据以便重新安装时恢复
            # 如果需要完全清理数据，请取消下面的注释
            
            # await clean_marketing_game_tables(db)

            await db.commit()
            logger.info(f"营销游戏插件卸载成功，租户ID: {tenant_id}")
            return True

        except Exception as e:
            logger.error(f"卸载营销游戏插件失败: {str(e)}")
            await db.rollback()
            raise
        finally:
            await db.close()

async def get_plugin_status(tenant_id: str):
    """获取插件安装状态"""
    async for db in get_db():
        try:
            # 获取插件信息
            plugin_query = select(Plugin).where(Plugin.code == PLUGIN_INFO["code"])
            result = await db.execute(plugin_query)
            plugin = result.scalar_one_or_none()

            if not plugin:
                return {"installed": False, "plugin": None, "tenant_plugin": None}

            # 检查租户是否安装了该插件
            tenant_plugin_query = select(TenantPlugin).where(
                and_(
                    TenantPlugin.tenant_id == tenant_id,
                    TenantPlugin.plugin_id == plugin.id
                )
            )
            result = await db.execute(tenant_plugin_query)
            tenant_plugin = result.scalar_one_or_none()

            return {
                "installed": tenant_plugin is not None,
                "plugin": plugin,
                "tenant_plugin": tenant_plugin
            }

        except Exception as e:
            logger.error(f"获取插件状态失败: {str(e)}")
            return {"installed": False, "plugin": None, "tenant_plugin": None}
        finally:
            await db.close() 