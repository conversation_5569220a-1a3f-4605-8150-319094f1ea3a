#!/usr/bin/env python
# -*- coding: utf-8 -*-

import logging
import uuid
from typing import Dict, Any, List, Optional
from fastapi import APIRouter, Depends, HTTPException, Body, status
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from sqlalchemy import and_, or_
from datetime import datetime

from db.database import get_db
from models.user import User
from models.tenant import Tenant
from models.project import Project
from models.plugin import Plugin, TenantPlugin

from ..models.models import DingTalkWebhook, DingTalkSettings, DingTalkUserMapping, DingTalkGroup, DingTalkNotificationLog
from ..utils.notification import send_notification_to_dingtalk
from ..utils.sync_users import sync_dingtalk_data
from core.auth import get_current_user
from api.deps import get_current_project, get_current_tenant

# 初始化日志
logger = logging.getLogger(__name__)

# 创建路由器
router = APIRouter(
    tags=["钉钉机器人插件"]
)

@router.get("/settings")
async def get_settings(
    current_user: User = Depends(get_current_user),
    current_project: Project = Depends(get_current_project),
    current_tenant: Tenant = Depends(get_current_tenant),
    db: AsyncSession = Depends(get_db)
):
    """获取钉钉机器人插件设置"""
    try:
        # 检查插件是否已安装
        plugin_query = select(Plugin).where(Plugin.code == "dingtalk_robot")
        result = await db.execute(plugin_query)
        plugin = result.scalar_one_or_none()

        if not plugin:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="钉钉机器人插件未安装"
            )

        # 检查租户是否已安装插件
        tenant_plugin_query = select(TenantPlugin).where(
            and_(
                TenantPlugin.tenant_id == current_tenant.id,
                TenantPlugin.plugin_id == plugin.id
            )
        )
        result = await db.execute(tenant_plugin_query)
        tenant_plugin = result.scalar_one_or_none()

        if not tenant_plugin or tenant_plugin.status != "active":
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="钉钉机器人插件未激活"
            )

        # 获取插件设置
        settings_query = select(DingTalkSettings).where(
            and_(
                DingTalkSettings.tenant_id == current_tenant.id,
                DingTalkSettings.project_id == current_project.id
            )
        )
        result = await db.execute(settings_query)
        settings = result.scalar_one_or_none()

        if not settings:
            # 创建默认设置
            from ..db_init import initialize_tenant_plugin
            await initialize_tenant_plugin(db, current_tenant.id, current_project.id, plugin.id)

            # 重新查询
            result = await db.execute(settings_query)
            settings = result.scalar_one_or_none()

        # 获取Webhook列表
        webhooks_query = select(DingTalkWebhook).where(
            and_(
                DingTalkWebhook.tenant_id == current_tenant.id,
                DingTalkWebhook.project_id == current_project.id
            )
        )
        result = await db.execute(webhooks_query)
        webhooks = result.scalars().all()

        # 构建响应
        response = {
            "settings": {
                "id": str(settings.id),
                "enable_dingtalk": settings.enable_dingtalk,
                "notification_level": settings.notification_level,
                "retry_count": settings.retry_count,
                "retry_interval": settings.retry_interval,
                "default_template": settings.default_template
            },
            "webhooks": [
                {
                    "id": str(webhook.id),
                    "name": webhook.name,
                    "webhook_url": webhook.webhook_url,
                    "secret": webhook.secret,
                    "enabled": webhook.enabled,
                    "message_template": webhook.message_template,
                    "notification_types": webhook.notification_types,
                    "target_users": webhook.target_users,
                    "target_groups": webhook.target_groups,
                    "created_at": webhook.created_at.isoformat() if webhook.created_at else None,
                    "updated_at": webhook.updated_at.isoformat() if webhook.updated_at else None
                }
                for webhook in webhooks
            ]
        }

        return response

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取钉钉机器人插件设置失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取钉钉机器人插件设置失败: {str(e)}"
        )

@router.post("/settings")
async def update_settings(
    settings: Dict[str, Any] = Body(...),
    current_user: User = Depends(get_current_user),
    current_project: Project = Depends(get_current_project),
    current_tenant: Tenant = Depends(get_current_tenant),
    db: AsyncSession = Depends(get_db)
):
    """更新钉钉机器人插件设置"""
    try:
        # 获取插件设置
        settings_query = select(DingTalkSettings).where(
            and_(
                DingTalkSettings.tenant_id == current_tenant.id,
                DingTalkSettings.project_id == current_project.id
            )
        )
        result = await db.execute(settings_query)
        db_settings = result.scalar_one_or_none()

        if not db_settings:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="钉钉机器人插件设置不存在"
            )

        # 更新设置
        db_settings.enable_dingtalk = settings.get("enable_dingtalk", db_settings.enable_dingtalk)
        db_settings.notification_level = settings.get("notification_level", db_settings.notification_level)
        db_settings.retry_count = settings.get("retry_count", db_settings.retry_count)
        db_settings.retry_interval = settings.get("retry_interval", db_settings.retry_interval)
        db_settings.default_template = settings.get("default_template", db_settings.default_template)

        await db.commit()

        return {
            "id": str(db_settings.id),
            "enable_dingtalk": db_settings.enable_dingtalk,
            "notification_level": db_settings.notification_level,
            "retry_count": db_settings.retry_count,
            "retry_interval": db_settings.retry_interval,
            "default_template": db_settings.default_template
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"更新钉钉机器人插件设置失败: {str(e)}")
        await db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"更新钉钉机器人插件设置失败: {str(e)}"
        )

@router.get("/webhooks")
async def get_webhooks(
    current_user: User = Depends(get_current_user),
    current_project: Project = Depends(get_current_project),
    current_tenant: Tenant = Depends(get_current_tenant),
    db: AsyncSession = Depends(get_db)
):
    """获取钉钉机器人Webhook列表"""
    try:
        # 获取Webhook列表
        webhooks_query = select(DingTalkWebhook).where(
            and_(
                DingTalkWebhook.tenant_id == current_tenant.id,
                DingTalkWebhook.project_id == current_project.id
            )
        )
        result = await db.execute(webhooks_query)
        webhooks = result.scalars().all()

        return [
            {
                "id": str(webhook.id),
                "name": webhook.name,
                "webhook_url": webhook.webhook_url,
                "secret": webhook.secret,
                "enabled": webhook.enabled,
                "message_template": webhook.message_template,
                "notification_types": webhook.notification_types,
                "target_users": webhook.target_users,
                "target_groups": webhook.target_groups,
                "created_at": webhook.created_at.isoformat() if webhook.created_at else None,
                "updated_at": webhook.updated_at.isoformat() if webhook.updated_at else None
            }
            for webhook in webhooks
        ]

    except Exception as e:
        logger.error(f"获取钉钉机器人Webhook列表失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取钉钉机器人Webhook列表失败: {str(e)}"
        )

@router.post("/webhooks")
async def create_webhook(
    webhook_data: Dict[str, Any] = Body(...),
    current_user: User = Depends(get_current_user),
    current_project: Project = Depends(get_current_project),
    current_tenant: Tenant = Depends(get_current_tenant),
    db: AsyncSession = Depends(get_db)
):
    """创建钉钉机器人Webhook"""
    try:
        # 创建Webhook
        webhook = DingTalkWebhook(
            id=uuid.uuid4(),
            tenant_id=current_tenant.id,
            project_id=current_project.id,
            name=webhook_data["name"],
            webhook_url=webhook_data["webhook_url"],
            secret=webhook_data.get("secret"),
            enabled=webhook_data.get("enabled", True),
            message_template=webhook_data.get("message_template"),
            notification_types=webhook_data.get("notification_types", ["all"]),
            target_users=webhook_data.get("target_users", []),
            target_groups=webhook_data.get("target_groups", []),
            created_by=current_user.id
        )

        db.add(webhook)
        await db.commit()

        return {
            "id": str(webhook.id),
            "name": webhook.name,
            "webhook_url": webhook.webhook_url,
            "secret": webhook.secret,
            "enabled": webhook.enabled,
            "message_template": webhook.message_template,
            "notification_types": webhook.notification_types,
            "target_users": webhook.target_users,
            "target_groups": webhook.target_groups,
            "created_at": webhook.created_at.isoformat() if webhook.created_at else None,
            "updated_at": webhook.updated_at.isoformat() if webhook.updated_at else None
        }

    except Exception as e:
        logger.error(f"创建钉钉机器人Webhook失败: {str(e)}")
        await db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"创建钉钉机器人Webhook失败: {str(e)}"
        )

@router.put("/webhooks/{webhook_id}")
async def update_webhook(
    webhook_id: uuid.UUID,
    webhook_data: Dict[str, Any] = Body(...),
    current_user: User = Depends(get_current_user),
    current_project: Project = Depends(get_current_project),
    current_tenant: Tenant = Depends(get_current_tenant),
    db: AsyncSession = Depends(get_db)
):
    """更新钉钉机器人Webhook"""
    try:
        # 查询Webhook
        webhook_query = select(DingTalkWebhook).where(
            and_(
                DingTalkWebhook.id == webhook_id,
                DingTalkWebhook.tenant_id == current_tenant.id,
                DingTalkWebhook.project_id == current_project.id
            )
        )
        result = await db.execute(webhook_query)
        webhook = result.scalar_one_or_none()

        if not webhook:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="钉钉机器人Webhook不存在"
            )

        # 更新Webhook
        webhook.name = webhook_data.get("name", webhook.name)
        webhook.webhook_url = webhook_data.get("webhook_url", webhook.webhook_url)
        webhook.secret = webhook_data.get("secret", webhook.secret)
        webhook.enabled = webhook_data.get("enabled", webhook.enabled)
        webhook.message_template = webhook_data.get("message_template", webhook.message_template)
        webhook.notification_types = webhook_data.get("notification_types", webhook.notification_types)
        webhook.target_users = webhook_data.get("target_users", webhook.target_users)
        webhook.target_groups = webhook_data.get("target_groups", webhook.target_groups)

        await db.commit()

        return {
            "id": str(webhook.id),
            "name": webhook.name,
            "webhook_url": webhook.webhook_url,
            "secret": webhook.secret,
            "enabled": webhook.enabled,
            "message_template": webhook.message_template,
            "notification_types": webhook.notification_types,
            "target_users": webhook.target_users,
            "target_groups": webhook.target_groups,
            "created_at": webhook.created_at.isoformat() if webhook.created_at else None,
            "updated_at": webhook.updated_at.isoformat() if webhook.updated_at else None
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"更新钉钉机器人Webhook失败: {str(e)}")
        await db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"更新钉钉机器人Webhook失败: {str(e)}"
        )

@router.delete("/webhooks/{webhook_id}")
async def delete_webhook(
    webhook_id: uuid.UUID,
    current_user: User = Depends(get_current_user),
    current_project: Project = Depends(get_current_project),
    current_tenant: Tenant = Depends(get_current_tenant),
    db: AsyncSession = Depends(get_db)
):
    """删除钉钉机器人Webhook"""
    try:
        # 查询Webhook
        webhook_query = select(DingTalkWebhook).where(
            and_(
                DingTalkWebhook.id == webhook_id,
                DingTalkWebhook.tenant_id == current_tenant.id,
                DingTalkWebhook.project_id == current_project.id
            )
        )
        result = await db.execute(webhook_query)
        webhook = result.scalar_one_or_none()

        if not webhook:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="钉钉机器人Webhook不存在"
            )

        # 删除Webhook
        await db.delete(webhook)
        await db.commit()

        return {"message": "钉钉机器人Webhook删除成功"}

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"删除钉钉机器人Webhook失败: {str(e)}")
        await db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"删除钉钉机器人Webhook失败: {str(e)}"
        )

@router.post("/test")
async def test_webhook(
    test_data: Dict[str, Any] = Body(...),
    webhook_id: Optional[uuid.UUID] = None,
    current_user: User = Depends(get_current_user),
    current_project: Project = Depends(get_current_project),
    current_tenant: Tenant = Depends(get_current_tenant),
    db: AsyncSession = Depends(get_db)
):
    """测试钉钉机器人Webhook"""
    try:
        if webhook_id:
            # 查询特定Webhook
            webhook_query = select(DingTalkWebhook).where(
                and_(
                    DingTalkWebhook.id == webhook_id,
                    DingTalkWebhook.tenant_id == current_tenant.id,
                    DingTalkWebhook.project_id == current_project.id
                )
            )
            result = await db.execute(webhook_query)
            webhook = result.scalar_one_or_none()

            if not webhook:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="钉钉机器人Webhook不存在"
                )

            # 发送测试消息
            result = await send_notification_to_dingtalk(
                tenant_id=current_tenant.id,
                project_id=current_project.id,
                notification_type="test",
                title="测试通知",
                content=test_data.get("content", "这是一条测试通知，如果您收到这条消息，说明钉钉机器人配置正确。"),
                target_users=[str(current_user.id)],
                extra_data={"test": True},
                webhook_ids=[str(webhook_id)]
            )
        else:
            # 发送测试消息到所有Webhook
            result = await send_notification_to_dingtalk(
                tenant_id=current_tenant.id,
                project_id=current_project.id,
                notification_type="test",
                title="测试通知",
                content=test_data.get("content", "这是一条测试通知，如果您收到这条消息，说明钉钉机器人配置正确。"),
                target_users=[str(current_user.id)],
                extra_data={"test": True}
            )

        return result

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"测试钉钉机器人Webhook失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"测试钉钉机器人Webhook失败: {str(e)}"
        )

@router.post("/sync")
async def sync_dingtalk(
    sync_data: Dict[str, Any] = Body(...),
    current_user: User = Depends(get_current_user),
    current_project: Project = Depends(get_current_project),
    current_tenant: Tenant = Depends(get_current_tenant),
    db: AsyncSession = Depends(get_db)
):
    """同步钉钉数据"""
    try:
        # 使用令牌管理器自动获取访问凭证
        from ..utils.token_manager import token_manager
        
        access_token = await token_manager.get_access_token(db, current_project, str(current_user.id))
        
        if not access_token:
            # 如果无法自动获取，检查是否手动提供了访问令牌
            access_token = sync_data.get("access_token")
            
            if not access_token:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="无法获取钉钉访问令牌。请确保：1) 已正确配置钉钉应用凭证，或 2) 用户已绑定钉钉账号，或 3) 手动提供访问令牌"
                )

        # 同步数据
        result = await sync_dingtalk_data(
            tenant_id=current_tenant.id,
            project_id=current_project.id,
            access_token=access_token
        )

        return {
            "success": True,
            "message": "钉钉数据同步完成",
            "data": result,
            "token_source": "auto" if sync_data.get("access_token") is None else "manual"
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"同步钉钉数据失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"同步钉钉数据失败: {str(e)}"
        )

@router.get("/logs")
async def get_notification_logs(
    skip: int = 0,
    limit: int = 100,
    current_user: User = Depends(get_current_user),
    current_project: Project = Depends(get_current_project),
    current_tenant: Tenant = Depends(get_current_tenant),
    db: AsyncSession = Depends(get_db)
):
    """获取钉钉通知日志"""
    try:
        # 获取通知日志
        logs_query = select(DingTalkNotificationLog).where(
            and_(
                DingTalkNotificationLog.tenant_id == current_tenant.id,
                DingTalkNotificationLog.project_id == current_project.id
            )
        ).order_by(DingTalkNotificationLog.created_at.desc()).offset(skip).limit(limit)

        result = await db.execute(logs_query)
        logs = result.scalars().all()

        # 获取总数
        count_query = select(DingTalkNotificationLog).where(
            and_(
                DingTalkNotificationLog.tenant_id == current_tenant.id,
                DingTalkNotificationLog.project_id == current_project.id
            )
        )
        count_result = await db.execute(count_query)
        total_count = len(count_result.scalars().all())

        # 格式化日志数据
        logs_data = [
            {
                "id": str(log.id),
                "webhook_id": str(log.webhook_id) if log.webhook_id else None,
                "notification_type": log.notification_type,
                "title": log.title,
                "content": log.content,
                "target_users": log.target_users,
                "target_groups": log.target_groups,
                "status": log.status,
                "error_message": log.error_message,
                "retry_count": log.retry_count,
                "created_at": log.created_at.isoformat() if log.created_at else None
            }
            for log in logs
        ]

        # 返回标准格式
        return {
            "success": True,
            "data": {
                "logs": logs_data,
                "pagination": {
                    "current": (skip // limit) + 1,
                    "pageSize": limit,
                    "total": total_count
                }
            }
        }

    except Exception as e:
        logger.error(f"获取钉钉通知日志失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取钉钉通知日志失败: {str(e)}"
        )

# 基础功能API路由

@router.get("/users")
async def get_dingtalk_users(
    current_user: User = Depends(get_current_user),
    current_project: Project = Depends(get_current_project),
    current_tenant: Tenant = Depends(get_current_tenant),
    db: AsyncSession = Depends(get_db)
):
    """获取钉钉用户列表（基础功能）"""
    try:
        # 检查权限
        from ..utils.permission_manager import DingTalkPermissionManager, DingTalkPermission
        DingTalkPermissionManager.check_permission(
            current_user, current_project, DingTalkPermission.VIEW_USER_INFO
        )
        
        # 获取钉钉用户映射数据
        users_query = select(DingTalkUserMapping).where(
            and_(
                DingTalkUserMapping.tenant_id == current_tenant.id,
                DingTalkUserMapping.project_id == current_project.id
            )
        )
        result = await db.execute(users_query)
        user_mappings = result.scalars().all()
        
        # 构建用户列表
        users = []
        for mapping in user_mappings:
            user_data = {
                "id": mapping.dingtalk_user_id,
                "name": mapping.dingtalk_name or "未知用户",
                "mobile": mapping.dingtalk_mobile or "",
                "email": mapping.dingtalk_email or "",
                "department": mapping.dingtalk_department or "未知部门",
                "position": mapping.dingtalk_position or "未知职位",
                "avatar": mapping.dingtalk_avatar or "",
                "status": "active" if mapping.is_active else "inactive"
            }
            users.append(user_data)
        
        # 如果没有映射数据，返回空列表
        if not users:
            users = []
        
        return {
            "success": True,
            "data": {
                "users": users,
                "total": len(users)
            }
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取钉钉用户列表失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取钉钉用户列表失败: {str(e)}"
        )

# AI对话功能引用ai_chat.py中的实现
from .ai_chat import router as ai_chat_router

@router.post("/send-message")
async def send_dingtalk_message(
    message_data: Dict[str, Any] = Body(...),
    current_user: User = Depends(get_current_user),
    current_project: Project = Depends(get_current_project),
    current_tenant: Tenant = Depends(get_current_tenant),
    db: AsyncSession = Depends(get_db)
):
    """发送钉钉消息（基础功能）"""
    try:
        # 检查权限
        from ..utils.permission_manager import DingTalkPermissionManager, DingTalkPermission
        DingTalkPermissionManager.check_permission(
            current_user, current_project, DingTalkPermission.SEND_MESSAGE
        )
        
        # 发送消息到钉钉
        result = await send_notification_to_dingtalk(
            tenant_id=current_tenant.id,
            project_id=current_project.id,
            notification_type=message_data.get("type", "message"),
            title=message_data.get("title", "系统消息"),
            content=message_data.get("content", ""),
            target_users=message_data.get("target_users", []),
            target_groups=message_data.get("target_groups", []),
            extra_data=message_data.get("extra_data", {})
        )
        
        return result
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"发送钉钉消息失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"发送钉钉消息失败: {str(e)}"
        )
