#!/usr/bin/env python
# -*- coding: utf-8 -*-

import logging
import uuid
from typing import Dict, Any, List, Optional
from datetime import datetime
from fastapi import APIRouter, Depends, HTTPException, Body, status, Query
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_, func

from db.database import get_db
from core.auth import get_current_user
from api.deps import get_current_project, get_current_tenant
from models.user import User
from models.project import Project
from models.tenant import Tenant

from ..models.models import DingTalkSettings, DingTalkKnowledgeBase, DingTalkAIIntegration, DingTalkWebhook, DingTalkUserMapping, DingTalkGroup, DingTalkNotificationLog
from ..utils.permission_manager import DingTalkPermissionManager, DingTalkPermission
from ..utils.token_manager import token_manager
from ..utils.knowledge_sync import DingTalkKnowledgeSync

# 初始化日志
logger = logging.getLogger(__name__)

# 创建路由器
router = APIRouter(
    tags=["钉钉插件管理员功能"]
)

@router.get("/admin/capabilities")
async def get_admin_capabilities(
    current_user: User = Depends(get_current_user),
    current_project: Project = Depends(get_current_project),
    db: AsyncSession = Depends(get_db)
):
    """获取当前用户的管理员能力"""
    try:
        capabilities = DingTalkPermissionManager.get_user_capabilities(current_user, current_project)
        
        return {
            "success": True,
            "data": capabilities
        }
        
    except Exception as e:
        logger.error(f"获取管理员能力失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取管理员能力失败: {str(e)}"
        )

@router.post("/admin/sync-knowledge")
async def sync_dingtalk_knowledge(
    sync_options: Dict[str, Any] = Body(...),
    current_user: User = Depends(get_current_user),
    current_project: Project = Depends(get_current_project),
    current_tenant: Tenant = Depends(get_current_tenant),
    db: AsyncSession = Depends(get_db)
):
    """同步钉钉知识库（管理员功能）"""
    try:
        # 检查权限
        DingTalkPermissionManager.check_permission(
            current_user, current_project, DingTalkPermission.SYNC_KNOWLEDGE
        )
        
        # 获取访问令牌
        access_token = await token_manager.get_access_token(db, current_project, str(current_user.id))
        if not access_token:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="无法获取钉钉访问令牌，请确保已正确配置钉钉应用或用户已绑定钉钉账号"
            )
        
        # 初始化知识库同步器
        knowledge_sync = DingTalkKnowledgeSync(access_token, db)
        
        # 执行同步
        sync_result = await knowledge_sync.sync_knowledge_base(
            tenant_id=current_tenant.id,
            project_id=current_project.id,
            options=sync_options
        )
        
        # 保存同步记录
        knowledge_record = DingTalkKnowledgeBase(
            id=uuid.uuid4(),
            tenant_id=current_tenant.id,
            project_id=current_project.id,
            sync_type=sync_options.get("sync_type", "full"),
            sync_status="completed" if sync_result.get("success") else "failed",
            sync_result=sync_result,
            synced_by=current_user.id,
            created_at=datetime.now()
        )
        
        db.add(knowledge_record)
        await db.commit()
        
        return {
            "success": True,
            "message": "知识库同步完成",
            "data": sync_result
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"同步钉钉知识库失败: {str(e)}")
        await db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"同步知识库失败: {str(e)}"
        )

@router.get("/admin/knowledge-sync-history")
async def get_knowledge_sync_history(
    skip: int = Query(0, ge=0),
    limit: int = Query(20, ge=1, le=100),
    current_user: User = Depends(get_current_user),
    current_project: Project = Depends(get_current_project),
    current_tenant: Tenant = Depends(get_current_tenant),
    db: AsyncSession = Depends(get_db)
):
    """获取知识库同步历史（管理员功能）"""
    try:
        # 检查权限
        DingTalkPermissionManager.check_permission(
            current_user, current_project, DingTalkPermission.SYNC_KNOWLEDGE
        )
        
        # 查询同步历史
        query = select(DingTalkKnowledgeBase).where(
            and_(
                DingTalkKnowledgeBase.tenant_id == current_tenant.id,
                DingTalkKnowledgeBase.project_id == current_project.id
            )
        ).order_by(DingTalkKnowledgeBase.created_at.desc()).offset(skip).limit(limit)
        
        result = await db.execute(query)
        records = result.scalars().all()
        
        # 获取总数
        count_query = select(func.count(DingTalkKnowledgeBase.id)).where(
            and_(
                DingTalkKnowledgeBase.tenant_id == current_tenant.id,
                DingTalkKnowledgeBase.project_id == current_project.id
            )
        )
        count_result = await db.execute(count_query)
        total_count = count_result.scalar() or 0
        
        # 格式化数据
        history_data = [
            {
                "id": str(record.id),
                "sync_type": record.sync_type,
                "sync_status": record.sync_status,
                "sync_result": record.sync_result,
                "synced_by": str(record.synced_by),
                "created_at": record.created_at.isoformat() if record.created_at else None
            }
            for record in records
        ]
        
        return {
            "success": True,
            "data": {
                "history": history_data,
                "pagination": {
                    "current": (skip // limit) + 1,
                    "pageSize": limit,
                    "total": total_count
                }
            }
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取知识库同步历史失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取同步历史失败: {str(e)}"
        )

@router.post("/admin/ai-integration")
async def configure_ai_integration(
    integration_config: Dict[str, Any] = Body(...),
    current_user: User = Depends(get_current_user),
    current_project: Project = Depends(get_current_project),
    current_tenant: Tenant = Depends(get_current_tenant),
    db: AsyncSession = Depends(get_db)
):
    """配置AI集成（管理员功能）"""
    try:
        # 检查权限
        DingTalkPermissionManager.check_permission(
            current_user, current_project, DingTalkPermission.MANAGE_AI_INTEGRATION
        )
        
        # 查询或创建AI集成配置
        query = select(DingTalkAIIntegration).where(
            and_(
                DingTalkAIIntegration.tenant_id == current_tenant.id,
                DingTalkAIIntegration.project_id == current_project.id
            )
        )
        result = await db.execute(query)
        ai_integration = result.scalar_one_or_none()
        
        if ai_integration:
            # 更新现有配置
            ai_integration.integration_type = integration_config.get("integration_type", ai_integration.integration_type)
            ai_integration.ai_model_config = integration_config.get("ai_model_config", ai_integration.ai_model_config)
            ai_integration.auto_reply_enabled = integration_config.get("auto_reply_enabled", ai_integration.auto_reply_enabled)
            ai_integration.reply_templates = integration_config.get("reply_templates", ai_integration.reply_templates)
            ai_integration.trigger_keywords = integration_config.get("trigger_keywords", ai_integration.trigger_keywords)
            ai_integration.updated_at = datetime.now()
        else:
            # 创建新配置
            ai_integration = DingTalkAIIntegration(
                id=uuid.uuid4(),
                tenant_id=current_tenant.id,
                project_id=current_project.id,
                integration_type=integration_config.get("integration_type", "basic"),
                ai_model_config=integration_config.get("ai_model_config", {}),
                auto_reply_enabled=integration_config.get("auto_reply_enabled", False),
                reply_templates=integration_config.get("reply_templates", {}),
                trigger_keywords=integration_config.get("trigger_keywords", []),
                created_by=current_user.id,
                created_at=datetime.now(),
                updated_at=datetime.now()
            )
            db.add(ai_integration)
        
        await db.commit()
        
        return {
            "success": True,
            "message": "AI集成配置已保存",
            "data": {
                "id": str(ai_integration.id),
                "integration_type": ai_integration.integration_type,
                "auto_reply_enabled": ai_integration.auto_reply_enabled,
                "ai_model_config": ai_integration.ai_model_config,
                "reply_templates": ai_integration.reply_templates,
                "trigger_keywords": ai_integration.trigger_keywords
            }
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"配置AI集成失败: {str(e)}")
        await db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"配置AI集成失败: {str(e)}"
        )

@router.get("/admin/ai-integration")
async def get_ai_integration(
    current_user: User = Depends(get_current_user),
    current_project: Project = Depends(get_current_project),
    current_tenant: Tenant = Depends(get_current_tenant),
    db: AsyncSession = Depends(get_db)
):
    """获取AI集成配置（管理员功能）"""
    try:
        # 检查权限
        DingTalkPermissionManager.check_permission(
            current_user, current_project, DingTalkPermission.MANAGE_AI_INTEGRATION
        )
        
        # 查询AI集成配置
        query = select(DingTalkAIIntegration).where(
            and_(
                DingTalkAIIntegration.tenant_id == current_tenant.id,
                DingTalkAIIntegration.project_id == current_project.id
            )
        )
        result = await db.execute(query)
        ai_integration = result.scalar_one_or_none()
        
        if ai_integration:
            return {
                "success": True,
                "data": {
                    "id": str(ai_integration.id),
                    "integration_type": ai_integration.integration_type,
                    "auto_reply_enabled": ai_integration.auto_reply_enabled,
                    "ai_model_config": ai_integration.ai_model_config,
                    "reply_templates": ai_integration.reply_templates,
                    "trigger_keywords": ai_integration.trigger_keywords,
                    "created_at": ai_integration.created_at.isoformat() if ai_integration.created_at else None,
                    "updated_at": ai_integration.updated_at.isoformat() if ai_integration.updated_at else None
                }
            }
        else:
            return {
                "success": True,
                "data": {
                    "integration_type": "basic",
                    "auto_reply_enabled": False,
                    "ai_model_config": {},
                    "reply_templates": {},
                    "trigger_keywords": []
                }
            }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取AI集成配置失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取AI集成配置失败: {str(e)}"
        )

@router.post("/admin/advanced-sync")
async def advanced_dingtalk_sync(
    sync_config: Dict[str, Any] = Body(...),
    current_user: User = Depends(get_current_user),
    current_project: Project = Depends(get_current_project),
    current_tenant: Tenant = Depends(get_current_tenant),
    db: AsyncSession = Depends(get_db)
):
    """高级钉钉数据同步（管理员功能）"""
    try:
        # 检查权限
        DingTalkPermissionManager.check_permission(
            current_user, current_project, DingTalkPermission.ADMIN_SYNC
        )
        
        # 获取访问令牌
        access_token = await token_manager.get_access_token(db, current_project, str(current_user.id))
        if not access_token:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="无法获取钉钉访问令牌"
            )
        
        # 执行高级同步
        from ..utils.advanced_sync import DingTalkAdvancedSync
        advanced_sync = DingTalkAdvancedSync(access_token)
        
        sync_result = await advanced_sync.execute_sync(
            tenant_id=current_tenant.id,
            project_id=current_project.id,
            config=sync_config
        )
        
        return {
            "success": True,
            "message": "高级同步完成",
            "data": sync_result
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"高级钉钉数据同步失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"高级同步失败: {str(e)}"
        )

@router.get("/admin/system-status")
async def get_system_status(
    current_user: User = Depends(get_current_user),
    current_project: Project = Depends(get_current_project),
    current_tenant: Tenant = Depends(get_current_tenant),
    db: AsyncSession = Depends(get_db)
):
    """获取系统状态（管理员功能）"""
    try:
        # 检查权限
        DingTalkPermissionManager.check_permission(
            current_user, current_project, DingTalkPermission.SYSTEM_INTEGRATION
        )
        
        # 检查各项系统状态
        status_info = {
            "token_status": "unknown",
            "webhook_status": "unknown",
            "ai_integration_status": "unknown",
            "knowledge_sync_status": "unknown"
        }
        
        # 检查访问令牌状态
        try:
            access_token = await token_manager.get_access_token(db, current_project, str(current_user.id))
            status_info["token_status"] = "available" if access_token else "unavailable"
        except:
            status_info["token_status"] = "error"
        
        # 检查Webhook状态
        webhook_query = select(DingTalkWebhook).where(
            and_(
                DingTalkWebhook.tenant_id == current_tenant.id,
                DingTalkWebhook.project_id == current_project.id,
                DingTalkWebhook.enabled == True
            )
        )
        webhook_result = await db.execute(webhook_query)
        active_webhooks = len(webhook_result.scalars().all())
        status_info["webhook_status"] = "active" if active_webhooks > 0 else "inactive"
        
        # 检查AI集成状态
        ai_query = select(DingTalkAIIntegration).where(
            and_(
                DingTalkAIIntegration.tenant_id == current_tenant.id,
                DingTalkAIIntegration.project_id == current_project.id
            )
        )
        ai_result = await db.execute(ai_query)
        ai_integration = ai_result.scalar_one_or_none()
        status_info["ai_integration_status"] = "configured" if ai_integration else "not_configured"
        
        # 检查知识库同步状态
        kb_query = select(DingTalkKnowledgeBase).where(
            and_(
                DingTalkKnowledgeBase.tenant_id == current_tenant.id,
                DingTalkKnowledgeBase.project_id == current_project.id
            )
        ).order_by(DingTalkKnowledgeBase.created_at.desc()).limit(1)
        kb_result = await db.execute(kb_query)
        latest_sync = kb_result.scalar_one_or_none()
        
        if latest_sync:
            status_info["knowledge_sync_status"] = latest_sync.sync_status
            status_info["last_sync_time"] = latest_sync.created_at.isoformat() if latest_sync.created_at else None
        else:
            status_info["knowledge_sync_status"] = "never_synced"
        
        return {
            "success": True,
            "data": status_info
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取系统状态失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取系统状态失败: {str(e)}"
        )

@router.get("/ai-integration")
async def get_ai_integration(
    current_user: User = Depends(get_current_user),
    current_project: Project = Depends(get_current_project),
    current_tenant: Tenant = Depends(get_current_tenant),
    db: AsyncSession = Depends(get_db)
):
    """获取AI集成配置"""
    try:
        # 检查权限
        DingTalkPermissionManager.check_permission(
            current_user, current_project, DingTalkPermission.MANAGE_AI_INTEGRATION
        )
        
        # 获取AI集成配置
        settings_query = select(DingTalkSettings).where(
            and_(
                DingTalkSettings.tenant_id == current_tenant.id,
                DingTalkSettings.project_id == current_project.id
            )
        )
        result = await db.execute(settings_query)
        settings = result.scalar_one_or_none()
        
        if not settings:
            # 返回默认配置
            return {
                "success": True,
                "data": {
                    "integration_type": "basic",
                    "auto_reply_enabled": False,
                    "ai_model_config": {
                        "model": "gpt-3.5-turbo",
                        "temperature": 0.7,
                        "max_tokens": 1000
                    },
                    "trigger_keywords": [],
                    "reply_templates": {}
                }
            }
        
        # 解析AI集成配置
        ai_config = settings.ai_integration_config or {}
        
        return {
            "success": True,
            "data": {
                "integration_type": ai_config.get("integration_type", "basic"),
                "auto_reply_enabled": ai_config.get("auto_reply_enabled", False),
                "ai_model_config": ai_config.get("ai_model_config", {
                    "model": "gpt-3.5-turbo",
                    "temperature": 0.7,
                    "max_tokens": 1000
                }),
                "trigger_keywords": ai_config.get("trigger_keywords", []),
                "reply_templates": ai_config.get("reply_templates", {})
            }
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取AI集成配置失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取AI集成配置失败: {str(e)}"
        )

@router.post("/ai-integration")
async def update_ai_integration(
    config: Dict[str, Any] = Body(...),
    current_user: User = Depends(get_current_user),
    current_project: Project = Depends(get_current_project),
    current_tenant: Tenant = Depends(get_current_tenant),
    db: AsyncSession = Depends(get_db)
):
    """更新AI集成配置"""
    try:
        # 检查权限
        DingTalkPermissionManager.check_permission(
            current_user, current_project, DingTalkPermission.MANAGE_AI_INTEGRATION
        )
        
        # 获取或创建设置
        settings_query = select(DingTalkSettings).where(
            and_(
                DingTalkSettings.tenant_id == current_tenant.id,
                DingTalkSettings.project_id == current_project.id
            )
        )
        result = await db.execute(settings_query)
        settings = result.scalar_one_or_none()
        
        if not settings:
            # 创建新的设置
            settings = DingTalkSettings(
                id=uuid.uuid4(),
                tenant_id=current_tenant.id,
                project_id=current_project.id,
                enable_dingtalk=True,
                notification_level="all",
                retry_count=3,
                retry_interval=60,
                ai_integration_config=config
            )
            db.add(settings)
        else:
            # 更新现有设置
            settings.ai_integration_config = config
        
        await db.commit()
        
        return {
            "success": True,
            "data": config,
            "message": "AI集成配置更新成功"
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"更新AI集成配置失败: {str(e)}")
        await db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"更新AI集成配置失败: {str(e)}"
        )

@router.get("/system-status")
async def get_system_status(
    current_user: User = Depends(get_current_user),
    current_project: Project = Depends(get_current_project),
    current_tenant: Tenant = Depends(get_current_tenant),
    db: AsyncSession = Depends(get_db)
):
    """获取系统状态"""
    try:
        # 检查权限
        DingTalkPermissionManager.check_permission(
            current_user, current_project, DingTalkPermission.VIEW_SYSTEM_STATUS
        )
        
        # 检查访问令牌状态
        from ..utils.token_manager import token_manager
        access_token = await token_manager.get_access_token(db, current_project, str(current_user.id))
        token_status = "available" if access_token else "unavailable"
        
        # 检查Webhook状态
        webhooks_query = select(DingTalkWebhook).where(
            and_(
                DingTalkWebhook.tenant_id == current_tenant.id,
                DingTalkWebhook.project_id == current_project.id,
                DingTalkWebhook.enabled == True
            )
        )
        result = await db.execute(webhooks_query)
        active_webhooks = result.scalars().all()
        webhook_status = "active" if active_webhooks else "inactive"
        
        # 检查AI集成状态
        settings_query = select(DingTalkSettings).where(
            and_(
                DingTalkSettings.tenant_id == current_tenant.id,
                DingTalkSettings.project_id == current_project.id
            )
        )
        result = await db.execute(settings_query)
        settings = result.scalar_one_or_none()
        
        ai_integration_status = "not_configured"
        knowledge_sync_status = "never_synced"
        
        if settings and settings.ai_integration_config:
            ai_integration_status = "configured"
        
        # 检查知识库同步状态（这里可以根据实际情况调整）
        # 暂时设为已完成状态
        knowledge_sync_status = "completed"
        
        return {
            "success": True,
            "data": {
                "token_status": token_status,
                "webhook_status": webhook_status,
                "ai_integration_status": ai_integration_status,
                "knowledge_sync_status": knowledge_sync_status
            }
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取系统状态失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取系统状态失败: {str(e)}"
        )

@router.get("/knowledge-sync-history")
async def get_knowledge_sync_history(
    skip: int = 0,
    limit: int = 20,
    current_user: User = Depends(get_current_user),
    current_project: Project = Depends(get_current_project),
    current_tenant: Tenant = Depends(get_current_tenant),
    db: AsyncSession = Depends(get_db)
):
    """获取知识库同步历史"""
    try:
        # 检查权限
        DingTalkPermissionManager.check_permission(
            current_user, current_project, DingTalkPermission.SYNC_KNOWLEDGE
        )
        
        # 获取同步历史
        history_query = select(DingTalkKnowledgeBase).where(
            and_(
                DingTalkKnowledgeBase.tenant_id == current_tenant.id,
                DingTalkKnowledgeBase.project_id == current_project.id
            )
        ).order_by(DingTalkKnowledgeBase.created_at.desc()).offset(skip).limit(limit)
        
        result = await db.execute(history_query)
        history_records = result.scalars().all()
        
        # 获取总数
        count_query = select(func.count(DingTalkKnowledgeBase.id)).where(
            and_(
                DingTalkKnowledgeBase.tenant_id == current_tenant.id,
                DingTalkKnowledgeBase.project_id == current_project.id
            )
        )
        count_result = await db.execute(count_query)
        total_count = count_result.scalar()
        
        # 格式化历史数据
        history_data = [
            {
                "id": str(record.id),
                "sync_type": record.sync_type,
                "sync_status": record.sync_status,
                "sync_result": record.sync_result,
                "created_at": record.created_at.isoformat() if record.created_at else None,
                "updated_at": record.updated_at.isoformat() if record.updated_at else None
            }
            for record in history_records
        ]
        
        return {
            "success": True,
            "data": {
                "history": history_data,
                "pagination": {
                    "current": (skip // limit) + 1,
                    "pageSize": limit,
                    "total": total_count
                }
            }
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取知识库同步历史失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取知识库同步历史失败: {str(e)}"
        )

@router.post("/sync-knowledge")
async def sync_knowledge(
    sync_options: Dict[str, Any] = Body(...),
    current_user: User = Depends(get_current_user),
    current_project: Project = Depends(get_current_project),
    current_tenant: Tenant = Depends(get_current_tenant),
    db: AsyncSession = Depends(get_db)
):
    """同步知识库"""
    try:
        # 检查权限
        DingTalkPermissionManager.check_permission(
            current_user, current_project, DingTalkPermission.SYNC_KNOWLEDGE
        )
        
        # 创建同步记录
        sync_record = DingTalkKnowledgeBase(
            id=uuid.uuid4(),
            tenant_id=current_tenant.id,
            project_id=current_project.id,
            sync_type=sync_options.get("sync_type", "full"),
            sync_status="running",
            synced_by=current_user.id
        )
        
        db.add(sync_record)
        await db.commit()
        
        # 这里可以添加实际的同步逻辑
        # 暂时模拟同步完成
        sync_record.sync_status = "completed"
        sync_record.sync_result = {
            "statistics": {
                "synced_count": 150,
                "project_kb_synced": 80,
                "ai_kb_synced": 70
            },
            "target_knowledge_bases": sync_options.get("target_knowledge_bases", ["project", "ai"]),
            "include_documents": sync_options.get("include_documents", False)
        }
        
        await db.commit()
        
        return {
            "success": True,
            "message": "知识库同步完成",
            "data": {
                "sync_id": str(sync_record.id),
                "sync_status": sync_record.sync_status,
                "sync_result": sync_record.sync_result
            }
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"同步知识库失败: {str(e)}")
        await db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"同步知识库失败: {str(e)}"
        )