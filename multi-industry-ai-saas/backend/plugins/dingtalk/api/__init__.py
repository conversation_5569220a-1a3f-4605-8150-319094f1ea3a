#!/usr/bin/env python
# -*- coding: utf-8 -*-

from fastapi import APIRouter

# 导入各个路由模块
from .router import router as main_router
from .auth import router as auth_router
from .ai_chat import router as ai_chat_router
from .admin import router as admin_router

# 创建主路由器
router = APIRouter()

# 注册子路由
router.include_router(main_router, tags=["钉钉机器人插件"])
router.include_router(auth_router, tags=["钉钉插件用户管理"])
router.include_router(ai_chat_router, tags=["钉钉AI聊天"])
router.include_router(admin_router, tags=["钉钉插件管理员功能"])

__all__ = ["router"]
