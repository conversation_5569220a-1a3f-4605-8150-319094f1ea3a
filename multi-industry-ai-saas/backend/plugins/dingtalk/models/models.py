#!/usr/bin/env python
# -*- coding: utf-8 -*-

from sqlalchemy import Column, String, Boolean, DateTime, ForeignKey, JSON, Text, Integer, UniqueConstraint
from sqlalchemy.dialects.postgresql import UUID, ARRAY
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
import uuid
from datetime import datetime

from db.database import Base

class DingTalkWebhook(Base):
    """钉钉机器人Webhook配置"""
    __tablename__ = "dingtalk_webhooks"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    tenant_id = Column(UUID(as_uuid=True), ForeignKey("tenants.id", ondelete="CASCADE"), nullable=False)
    project_id = Column(UUID(as_uuid=True), ForeignKey("projects.id", ondelete="CASCADE"), nullable=False)
    name = Column(String(100), nullable=False)
    webhook_url = Column(String(500), nullable=False)
    secret = Column(String(200), nullable=True)
    enabled = Column(Boolean, default=True)
    message_template = Column(Text, nullable=True)
    notification_types = Column(ARRAY(String), default=list)  # 存储通知类型列表
    target_users = Column(ARRAY(String), default=list)  # 存储目标用户ID列表
    target_groups = Column(ARRAY(String), default=list)  # 存储目标群组ID列表
    created_by = Column(UUID(as_uuid=True), ForeignKey("users.id"), nullable=False)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())

    # 关系
    tenant = relationship("Tenant", backref="dingtalk_webhooks")
    project = relationship("Project", backref="dingtalk_webhooks")
    creator = relationship("User", foreign_keys=[created_by], backref="created_dingtalk_webhooks")

    def __repr__(self):
        return f"<DingTalkWebhook(id={self.id}, name='{self.name}')>"


class DingTalkUserMapping(Base):
    """钉钉用户映射"""
    __tablename__ = "dingtalk_user_mappings"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    tenant_id = Column(UUID(as_uuid=True), ForeignKey("tenants.id", ondelete="CASCADE"), nullable=False)
    project_id = Column(UUID(as_uuid=True), ForeignKey("projects.id", ondelete="CASCADE"), nullable=False)
    user_id = Column(UUID(as_uuid=True), ForeignKey("users.id", ondelete="CASCADE"), nullable=False)
    dingtalk_user_id = Column(String(100), nullable=False)
    dingtalk_name = Column(String(100), nullable=True)
    dingtalk_mobile = Column(String(20), nullable=True)
    dingtalk_email = Column(String(100), nullable=True)
    dingtalk_department = Column(String(200), nullable=True)  # 部门信息
    dingtalk_position = Column(String(100), nullable=True)    # 职位信息
    dingtalk_avatar = Column(String(500), nullable=True)      # 头像URL
    is_active = Column(Boolean, default=True)                 # 是否活跃
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())

    # 关系
    tenant = relationship("Tenant", backref="dingtalk_user_mappings")
    project = relationship("Project", backref="dingtalk_user_mappings")
    user = relationship("User", backref="dingtalk_user_mapping")

    __table_args__ = (
        UniqueConstraint('tenant_id', 'project_id', 'user_id', name='uq_dingtalk_user_mapping'),
    )

    def __repr__(self):
        return f"<DingTalkUserMapping(id={self.id}, user_id='{self.user_id}', dingtalk_user_id='{self.dingtalk_user_id}')>"


class DingTalkGroup(Base):
    """钉钉群组"""
    __tablename__ = "dingtalk_groups"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    tenant_id = Column(UUID(as_uuid=True), ForeignKey("tenants.id", ondelete="CASCADE"), nullable=False)
    project_id = Column(UUID(as_uuid=True), ForeignKey("projects.id", ondelete="CASCADE"), nullable=False)
    group_id = Column(String(100), nullable=False)
    name = Column(String(100), nullable=False)
    description = Column(Text, nullable=True)
    owner_id = Column(String(100), nullable=True)
    member_count = Column(Integer, default=0)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())

    # 关系
    tenant = relationship("Tenant", backref="dingtalk_groups")
    project = relationship("Project", backref="dingtalk_groups")

    __table_args__ = (
        UniqueConstraint('tenant_id', 'project_id', 'group_id', name='uq_dingtalk_group'),
    )

    def __repr__(self):
        return f"<DingTalkGroup(id={self.id}, name='{self.name}', group_id='{self.group_id}')>"


class DingTalkSettings(Base):
    """钉钉插件设置"""
    __tablename__ = "dingtalk_settings"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    tenant_id = Column(UUID(as_uuid=True), ForeignKey("tenants.id", ondelete="CASCADE"), nullable=False)
    project_id = Column(UUID(as_uuid=True), ForeignKey("projects.id", ondelete="CASCADE"), nullable=False)
    enable_dingtalk = Column(Boolean, default=True)
    notification_level = Column(String(20), default="all")  # all, high_only, custom
    retry_count = Column(Integer, default=3)
    retry_interval = Column(Integer, default=60)  # 秒
    default_template = Column(Text, nullable=True)
    ai_integration_config = Column(JSON, nullable=True)  # AI集成配置
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())
    updated_by = Column(UUID(as_uuid=True), ForeignKey("users.id"), nullable=True)

    # 关系
    tenant = relationship("Tenant", backref="dingtalk_settings")
    project = relationship("Project", backref="dingtalk_settings")
    updater = relationship("User", foreign_keys=[updated_by], backref="updated_dingtalk_settings")

    __table_args__ = (
        UniqueConstraint('tenant_id', 'project_id', name='uq_dingtalk_settings'),
    )

    def __repr__(self):
        return f"<DingTalkSettings(id={self.id}, tenant_id='{self.tenant_id}', project_id='{self.project_id}')>"


class DingTalkNotificationLog(Base):
    """钉钉通知日志"""
    __tablename__ = "dingtalk_notification_logs"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    tenant_id = Column(UUID(as_uuid=True), ForeignKey("tenants.id", ondelete="CASCADE"), nullable=False)
    project_id = Column(UUID(as_uuid=True), ForeignKey("projects.id", ondelete="CASCADE"), nullable=False)
    webhook_id = Column(UUID(as_uuid=True), ForeignKey("dingtalk_webhooks.id", ondelete="SET NULL"), nullable=True)
    notification_type = Column(String(50), nullable=False)
    title = Column(String(200), nullable=True)
    content = Column(Text, nullable=False)
    status = Column(String(20), nullable=False)  # success, failed
    error_message = Column(Text, nullable=True)
    retry_count = Column(Integer, default=0)
    target_users = Column(ARRAY(String), default=list)
    target_groups = Column(ARRAY(String), default=list)
    response_data = Column(JSON, nullable=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now())

    # 关系
    tenant = relationship("Tenant", backref="dingtalk_notification_logs")
    project = relationship("Project", backref="dingtalk_notification_logs")
    webhook = relationship("DingTalkWebhook", backref="notification_logs")

    def __repr__(self):
        return f"<DingTalkNotificationLog(id={self.id}, status='{self.status}')>"


class DingTalkKnowledgeBase(Base):
    """钉钉知识库同步记录"""
    __tablename__ = "dingtalk_knowledge_bases"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    tenant_id = Column(UUID(as_uuid=True), ForeignKey("tenants.id", ondelete="CASCADE"), nullable=False)
    project_id = Column(UUID(as_uuid=True), ForeignKey("projects.id", ondelete="CASCADE"), nullable=False)
    sync_type = Column(String(50), nullable=False)  # full, incremental, selective
    sync_status = Column(String(20), nullable=False)  # pending, running, completed, failed
    sync_result = Column(JSON, nullable=True)  # 同步结果详情
    synced_by = Column(UUID(as_uuid=True), ForeignKey("users.id"), nullable=False)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())

    # 关系
    tenant = relationship("Tenant", backref="dingtalk_knowledge_bases")
    project = relationship("Project", backref="dingtalk_knowledge_bases")
    syncer = relationship("User", foreign_keys=[synced_by], backref="synced_dingtalk_knowledge_bases")

    def __repr__(self):
        return f"<DingTalkKnowledgeBase(id={self.id}, sync_type='{self.sync_type}', status='{self.sync_status}')>"


class DingTalkAIIntegration(Base):
    """钉钉AI集成配置"""
    __tablename__ = "dingtalk_ai_integrations"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    tenant_id = Column(UUID(as_uuid=True), ForeignKey("tenants.id", ondelete="CASCADE"), nullable=False)
    project_id = Column(UUID(as_uuid=True), ForeignKey("projects.id", ondelete="CASCADE"), nullable=False)
    integration_type = Column(String(50), default="basic")  # basic, advanced, custom
    ai_model_config = Column(JSON, default=dict)  # AI模型配置
    auto_reply_enabled = Column(Boolean, default=False)  # 是否启用自动回复
    reply_templates = Column(JSON, default=dict)  # 回复模板
    trigger_keywords = Column(ARRAY(String), default=list)  # 触发关键词
    created_by = Column(UUID(as_uuid=True), ForeignKey("users.id"), nullable=False)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())

    # 关系
    tenant = relationship("Tenant", backref="dingtalk_ai_integrations")
    project = relationship("Project", backref="dingtalk_ai_integrations")
    creator = relationship("User", foreign_keys=[created_by], backref="created_dingtalk_ai_integrations")

    __table_args__ = (
        UniqueConstraint('tenant_id', 'project_id', name='uq_dingtalk_ai_integration'),
    )

    def __repr__(self):
        return f"<DingTalkAIIntegration(id={self.id}, integration_type='{self.integration_type}')>"
