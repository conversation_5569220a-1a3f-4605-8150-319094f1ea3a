#!/usr/bin/env python
# -*- coding: utf-8 -*-

import logging
import aiohttp
import json
from typing import Dict, Any, Optional, List
import time
import hashlib
import hmac
import base64
import urllib.parse

# 初始化日志
logger = logging.getLogger(__name__)

class DingTalkAPI:
    """钉钉API工具类"""
    
    def __init__(self, app_key: str, app_secret: str):
        """
        初始化钉钉API
        
        Args:
            app_key: 钉钉应用的AppKey
            app_secret: 钉钉应用的AppSecret
        """
        self.app_key = app_key
        self.app_secret = app_secret
        self.base_url = "https://oapi.dingtalk.com"
        self.access_token = None
        self.token_expires_at = 0
    
    async def get_access_token(self) -> Optional[str]:
        """
        获取访问令牌
        
        Returns:
            str: 访问令牌
        """
        try:
            # 检查token是否过期
            if self.access_token and time.time() < self.token_expires_at:
                return self.access_token
            
            url = f"{self.base_url}/gettoken"
            params = {
                "appkey": self.app_key,
                "appsecret": self.app_secret
            }
            
            async with aiohttp.ClientSession() as session:
                async with session.get(url, params=params) as response:
                    if response.status == 200:
                        data = await response.json()
                        if data.get("errcode") == 0:
                            self.access_token = data.get("access_token")
                            # 设置过期时间（提前5分钟过期）
                            self.token_expires_at = time.time() + data.get("expires_in", 7200) - 300
                            return self.access_token
                        else:
                            logger.error(f"获取钉钉访问令牌失败: {data}")
                            return None
                    else:
                        logger.error(f"获取钉钉访问令牌请求失败: {response.status}")
                        return None
        except Exception as e:
            logger.error(f"获取钉钉访问令牌异常: {str(e)}")
            return None
    
    async def get_user_info_by_code(self, code: str) -> Optional[Dict[str, Any]]:
        """
        通过授权码获取用户信息（使用标准OAuth 2.0流程）
        
        Args:
            code: 授权码（authCode）
            
        Returns:
            Dict[str, Any]: 用户信息
        """
        try:
            logger.info(f"开始通过授权码获取用户信息，code: {code[:10]}...")
            
            # 第一步：通过authCode获取用户token
            user_token = await self._get_user_token_by_code(code)
            if not user_token:
                logger.error("获取用户token失败")
                return None
            
            logger.info(f"成功获取用户token: {user_token[:10]}...")
            
            # 第二步：通过用户token获取用户信息
            user_info = await self._get_user_info_by_token(user_token)
            if not user_info:
                logger.error("获取用户信息失败")
                return None
            
            logger.info(f"成功获取用户信息: {user_info}")
            return user_info
            
        except Exception as e:
            logger.error(f"通过授权码获取用户信息异常: {str(e)}", exc_info=True)
            return None
    
    async def _get_user_token_by_code(self, code: str) -> Optional[str]:
        """
        通过authCode获取用户token
        
        Args:
            code: 授权码
            
        Returns:
            str: 用户token
        """
        try:
            url = "https://api.dingtalk.com/v1.0/oauth2/userAccessToken"
            headers = {
                "Content-Type": "application/json"
            }
            data = {
                "clientId": self.app_key,
                "clientSecret": self.app_secret,
                "code": code,
                "grantType": "authorization_code"
            }
            
            logger.info(f"调用获取用户token API: {url}")
            logger.info(f"请求参数: clientId={self.app_key}, code={code[:10]}...")
            
            async with aiohttp.ClientSession() as session:
                async with session.post(url, headers=headers, json=data) as response:
                    response_text = await response.text()
                    logger.info(f"获取用户token响应状态: {response.status}")
                    logger.info(f"获取用户token响应内容: {response_text}")
                    
                    if response.status == 200:
                        result = await response.json() if response.content_type == 'application/json' else json.loads(response_text)
                        access_token = result.get("accessToken")
                        if access_token:
                            logger.info(f"成功获取用户token: {access_token[:10]}...")
                            return access_token
                        else:
                            logger.error(f"响应中没有accessToken: {result}")
                            return None
                    else:
                        logger.error(f"获取用户token请求失败: status={response.status}, response={response_text}")
                        return None
        except Exception as e:
            logger.error(f"获取用户token异常: {str(e)}", exc_info=True)
            return None
    
    async def _get_user_info_by_token(self, user_token: str) -> Optional[Dict[str, Any]]:
        """
        通过用户token获取用户信息
        
        Args:
            user_token: 用户token
            
        Returns:
            Dict[str, Any]: 用户信息
        """
        try:
            # 根据钉钉官方文档，使用正确的API端点和参数格式
            url = "https://api.dingtalk.com/v1.0/contact/users/me"
            headers = {
                "x-acs-dingtalk-access-token": user_token,
                "Content-Type": "application/json"
            }
            
            logger.info(f"调用获取用户信息API: {url}")
            logger.info(f"使用用户token: {user_token[:10]}...")
            logger.info(f"请求头: {headers}")
            
            async with aiohttp.ClientSession() as session:
                async with session.get(url, headers=headers) as response:
                    response_text = await response.text()
                    logger.info(f"获取用户信息响应状态: {response.status}")
                    logger.info(f"获取用户信息响应内容: {response_text}")
                    
                    if response.status == 200:
                        try:
                            result = await response.json() if response.content_type == 'application/json' else json.loads(response_text)
                            
                            # 验证响应是否包含用户信息
                            if "unionId" in result or "openId" in result:
                                # 构造返回的用户信息
                                user_info = {
                                    "openid": result.get("openId"),
                                    "unionid": result.get("unionId"), 
                                    "nick": result.get("nick"),
                                    "avatarUrl": result.get("avatarUrl"),
                                    "mobile": result.get("mobile"),
                                    "email": result.get("email"),
                                    "name": result.get("name"),
                                    "stateCode": result.get("stateCode")
                                }
                                
                                logger.info(f"成功解析用户信息: {user_info}")
                                return user_info
                            else:
                                logger.error(f"响应中缺少用户标识信息: {result}")
                                return None
                        except json.JSONDecodeError as e:
                            logger.error(f"解析响应JSON失败: {e}, 响应内容: {response_text}")
                            return None
                    else:
                        logger.error(f"获取用户信息请求失败: status={response.status}, response={response_text}")
                        
                        # 如果是权限问题，尝试使用基本的用户信息结构
                        if response.status == 403:
                            logger.warning("权限被拒绝，可能需要在钉钉开发者后台添加'通讯录个人信息读权限'")
                            try:
                                # 尝试从token中解析基本信息（某些情况下token包含用户信息）
                                logger.info("尝试使用基本用户信息结构")
                                return {
                                    "openid": f"unknown_{user_token[:8]}",
                                    "unionid": f"unknown_{user_token[:8]}",
                                    "nick": "钉钉用户",
                                    "avatarUrl": None,
                                    "mobile": None,
                                    "email": None,
                                    "name": "钉钉用户",
                                    "stateCode": None
                                }
                            except Exception:
                                pass
                        
                        return None
        except Exception as e:
            logger.error(f"获取用户信息异常: {str(e)}", exc_info=True)
            return None
    
    async def get_user_detail(self, userid: str) -> Optional[Dict[str, Any]]:
        """
        获取用户详细信息
        
        Args:
            userid: 用户ID
            
        Returns:
            Dict[str, Any]: 用户详细信息
        """
        try:
            access_token = await self.get_access_token()
            if not access_token:
                return None
            
            url = f"{self.base_url}/topapi/v2/user/get"
            params = {
                "access_token": access_token
            }
            data = {
                "userid": userid
            }
            
            async with aiohttp.ClientSession() as session:
                async with session.post(url, params=params, json=data) as response:
                    if response.status == 200:
                        result = await response.json()
                        if result.get("errcode") == 0:
                            return result.get("result", {})
                        else:
                            logger.error(f"获取用户详细信息失败: {result}")
                            return None
                    else:
                        logger.error(f"获取用户详细信息请求失败: {response.status}")
                        return None
        except Exception as e:
            logger.error(f"获取用户详细信息异常: {str(e)}")
            return None
    
    async def send_work_notification(self, user_id: str = None, userid_list: List[str] = None, message: str = None, msg: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        发送工作通知（兼容单用户和多用户）
        
        Args:
            user_id: 单个用户ID
            userid_list: 用户ID列表
            message: 简单文本消息
            msg: 复杂消息内容
            
        Returns:
            Dict[str, Any]: 发送结果
        """
        try:
            access_token = await self.get_access_token()
            if not access_token:
                return {"success": False, "error": "获取访问令牌失败"}
            
            # 处理用户列表
            if user_id:
                target_users = [user_id]
            elif userid_list:
                target_users = userid_list
            else:
                return {"success": False, "error": "未指定目标用户"}
            
            # 处理消息内容
            if message and not msg:
                msg = {
                    "msgtype": "text",
                    "text": {
                        "content": message
                    }
                }
            elif not msg:
                return {"success": False, "error": "未指定消息内容"}
            
            url = f"{self.base_url}/topapi/message/corpconversation/asyncsend_v2"
            params = {
                "access_token": access_token
            }
            data = {
                "agent_id": self.app_key,  # 使用应用的agentId
                "userid_list": ",".join(target_users),
                "msg": msg
            }
            
            async with aiohttp.ClientSession() as session:
                async with session.post(url, params=params, json=data) as response:
                    if response.status == 200:
                        result = await response.json()
                        if result.get("errcode") == 0:
                            logger.info(f"发送工作通知成功: {result}")
                            return {
                                "success": True,
                                "task_id": result.get("task_id"),
                                "message": "发送成功"
                            }
                        else:
                            logger.error(f"发送工作通知失败: {result}")
                            return {
                                "success": False,
                                "error": result.get("errmsg", "发送失败")
                            }
                    else:
                        logger.error(f"发送工作通知请求失败: {response.status}")
                        return {"success": False, "error": f"请求失败: {response.status}"}
        except Exception as e:
            logger.error(f"发送工作通知异常: {str(e)}")
            return {"success": False, "error": str(e)}
    
    async def send_group_message(self, chat_id: str, message: str = None, msg: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        发送群组消息
        
        Args:
            chat_id: 群组ID
            message: 简单文本消息
            msg: 复杂消息内容
            
        Returns:
            Dict[str, Any]: 发送结果
        """
        try:
            access_token = await self.get_access_token()
            if not access_token:
                return {"success": False, "error": "获取访问令牌失败"}
            
            # 处理消息内容
            if message and not msg:
                msg = {
                    "msgtype": "text",
                    "text": {
                        "content": message
                    }
                }
            elif not msg:
                return {"success": False, "error": "未指定消息内容"}
            
            url = f"{self.base_url}/chat/send"
            params = {
                "access_token": access_token
            }
            data = {
                "chatid": chat_id,
                "msg": msg
            }
            
            async with aiohttp.ClientSession() as session:
                async with session.post(url, params=params, json=data) as response:
                    if response.status == 200:
                        result = await response.json()
                        if result.get("errcode") == 0:
                            logger.info(f"发送群组消息成功: {result}")
                            return {
                                "success": True,
                                "message_id": result.get("messageId"),
                                "message": "发送成功"
                            }
                        else:
                            logger.error(f"发送群组消息失败: {result}")
                            return {
                                "success": False,
                                "error": result.get("errmsg", "发送失败")
                            }
                    else:
                        logger.error(f"发送群组消息请求失败: {response.status}")
                        return {"success": False, "error": f"请求失败: {response.status}"}
        except Exception as e:
            logger.error(f"发送群组消息异常: {str(e)}")
            return {"success": False, "error": str(e)}
    
    async def send_interactive_card(self, target_type: str, target_id: str, card_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        发送交互式卡片消息
        
        Args:
            target_type: 目标类型 (user/group)
            target_id: 目标ID
            card_data: 卡片数据
            
        Returns:
            Dict[str, Any]: 发送结果
        """
        try:
            access_token = await self.get_access_token()
            if not access_token:
                return {"success": False, "error": "获取访问令牌失败"}
            
            if target_type == "user":
                url = f"{self.base_url}/topapi/message/corpconversation/asyncsend_v2"
                data = {
                    "agent_id": self.app_key,
                    "userid_list": target_id,
                    "msg": {
                        "msgtype": "action_card",
                        "action_card": card_data
                    }
                }
            elif target_type == "group":
                url = f"{self.base_url}/chat/send"
                data = {
                    "chatid": target_id,
                    "msg": {
                        "msgtype": "action_card",
                        "action_card": card_data
                    }
                }
            else:
                return {"success": False, "error": "不支持的目标类型"}
            
            params = {"access_token": access_token}
            
            async with aiohttp.ClientSession() as session:
                async with session.post(url, params=params, json=data) as response:
                    if response.status == 200:
                        result = await response.json()
                        if result.get("errcode") == 0:
                            logger.info(f"发送交互式卡片成功: {result}")
                            return {"success": True, "result": result}
                        else:
                            logger.error(f"发送交互式卡片失败: {result}")
                            return {"success": False, "error": result.get("errmsg", "发送失败")}
                    else:
                        logger.error(f"发送交互式卡片请求失败: {response.status}")
                        return {"success": False, "error": f"请求失败: {response.status}"}
        except Exception as e:
            logger.error(f"发送交互式卡片异常: {str(e)}")
            return {"success": False, "error": str(e)}
    
    async def get_chat_info(self, chat_id: str) -> Optional[Dict[str, Any]]:
        """
        获取群组信息
        
        Args:
            chat_id: 群组ID
            
        Returns:
            Dict[str, Any]: 群组信息
        """
        try:
            access_token = await self.get_access_token()
            if not access_token:
                return None
            
            url = f"{self.base_url}/chat/get"
            params = {
                "access_token": access_token,
                "chatid": chat_id
            }
            
            async with aiohttp.ClientSession() as session:
                async with session.get(url, params=params) as response:
                    if response.status == 200:
                        result = await response.json()
                        if result.get("errcode") == 0:
                            return result
                        else:
                            logger.error(f"获取群组信息失败: {result}")
                            return None
                    else:
                        logger.error(f"获取群组信息请求失败: {response.status}")
                        return None
        except Exception as e:
            logger.error(f"获取群组信息异常: {str(e)}")
            return None
    
    async def create_ai_card_message(self, title: str, content: str, actions: List[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        创建AI助手卡片消息
        
        Args:
            title: 卡片标题
            content: 卡片内容
            actions: 操作按钮列表
            
        Returns:
            Dict[str, Any]: 卡片消息数据
        """
        card_data = {
            "title": title,
            "text": content,
            "hideAvatar": "0",
            "btnOrientation": "0"
        }
        
        if actions:
            if len(actions) == 1:
                # 单个按钮
                action = actions[0]
                card_data["singleTitle"] = action.get("title", "查看详情")
                card_data["singleURL"] = action.get("url", "#")
            else:
                # 多个按钮
                card_data["btns"] = [
                    {
                        "title": action.get("title", "按钮"),
                        "actionURL": action.get("url", "#")
                    }
                    for action in actions
                ]
        
        return card_data
    
    async def send_ai_response_card(self, target_type: str, target_id: str, ai_response: str, 
                                  thread_id: str = None, assistant_name: str = "AI助手") -> Dict[str, Any]:
        """
        发送AI回复卡片
        
        Args:
            target_type: 目标类型 (user/group)
            target_id: 目标ID
            ai_response: AI回复内容
            thread_id: 对话线程ID
            assistant_name: 助手名称
            
        Returns:
            Dict[str, Any]: 发送结果
        """
        # 创建操作按钮
        actions = [
            {
                "title": "继续对话",
                "url": f"https://saas.houshanai.com/dingtalk/chat?thread_id={thread_id}" if thread_id else "#"
            },
            {
                "title": "查看历史",
                "url": f"https://saas.houshanai.com/dingtalk/history?thread_id={thread_id}" if thread_id else "#"
            }
        ]
        
        # 创建卡片数据
        card_data = await self.create_ai_card_message(
            title=f"🤖 {assistant_name}",
            content=ai_response,
            actions=actions
        )
        
        # 发送卡片
        return await self.send_interactive_card(target_type, target_id, card_data)
    
    async def get_department_list(self, dept_id: int = 1) -> Optional[List[Dict[str, Any]]]:
        """
        获取部门列表
        
        Args:
            dept_id: 部门ID，默认为根部门
            
        Returns:
            List[Dict[str, Any]]: 部门列表
        """
        try:
            access_token = await self.get_access_token()
            if not access_token:
                return None
            
            url = f"{self.base_url}/topapi/v2/department/listsub"
            params = {
                "access_token": access_token
            }
            data = {
                "dept_id": dept_id
            }
            
            async with aiohttp.ClientSession() as session:
                async with session.post(url, params=params, json=data) as response:
                    if response.status == 200:
                        result = await response.json()
                        if result.get("errcode") == 0:
                            return result.get("result", [])
                        else:
                            logger.error(f"获取部门列表失败: {result}")
                            return None
                    else:
                        logger.error(f"获取部门列表请求失败: {response.status}")
                        return None
        except Exception as e:
            logger.error(f"获取部门列表异常: {str(e)}")
            return None
    
    async def get_department_users(self, dept_id: int) -> Optional[List[Dict[str, Any]]]:
        """
        获取部门用户列表
        
        Args:
            dept_id: 部门ID
            
        Returns:
            List[Dict[str, Any]]: 用户列表
        """
        try:
            access_token = await self.get_access_token()
            if not access_token:
                return None
            
            url = f"{self.base_url}/topapi/user/simplelist"
            params = {
                "access_token": access_token
            }
            data = {
                "dept_id": dept_id
            }
            
            async with aiohttp.ClientSession() as session:
                async with session.post(url, params=params, json=data) as response:
                    if response.status == 200:
                        result = await response.json()
                        if result.get("errcode") == 0:
                            return result.get("userlist", [])
                        else:
                            logger.error(f"获取部门用户列表失败: {result}")
                            return None
                    else:
                        logger.error(f"获取部门用户列表请求失败: {response.status}")
                        return None
        except Exception as e:
            logger.error(f"获取部门用户列表异常: {str(e)}")
            return None
    
    @staticmethod
    def generate_webhook_signature(timestamp: str, secret: str) -> str:
        """
        生成Webhook签名
        
        Args:
            timestamp: 时间戳
            secret: 密钥
            
        Returns:
            str: 签名
        """
        string_to_sign = f"{timestamp}\n{secret}"
        hmac_code = hmac.new(secret.encode(), string_to_sign.encode(), digestmod=hashlib.sha256).digest()
        sign = urllib.parse.quote_plus(base64.b64encode(hmac_code).decode())
        return sign
    
    @staticmethod
    async def send_webhook_message(webhook_url: str, message: Dict[str, Any], secret: str = None) -> bool:
        """
        发送Webhook消息
        
        Args:
            webhook_url: Webhook URL
            message: 消息内容
            secret: 密钥（可选）
            
        Returns:
            bool: 发送是否成功
        """
        try:
            # 如果有密钥，生成签名
            if secret:
                timestamp = str(int(round(time.time() * 1000)))
                sign = DingTalkAPI.generate_webhook_signature(timestamp, secret)
                webhook_url = f"{webhook_url}&timestamp={timestamp}&sign={sign}"
            
            async with aiohttp.ClientSession() as session:
                async with session.post(webhook_url, json=message) as response:
                    if response.status == 200:
                        result = await response.json()
                        if result.get("errcode") == 0:
                            logger.info(f"发送Webhook消息成功: {result}")
                            return True
                        else:
                            logger.error(f"发送Webhook消息失败: {result}")
                            return False
                    else:
                        logger.error(f"发送Webhook消息请求失败: {response.status}")
                        return False
        except Exception as e:
            logger.error(f"发送Webhook消息异常: {str(e)}")
            return False 