#!/usr/bin/env python
# -*- coding: utf-8 -*-

import logging
import json
import time
import hmac
import hashlib
import base64
import urllib.parse
import asyncio
import aiohttp
from datetime import datetime
from typing import Dict, List, Any, Optional
import uuid

from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from sqlalchemy import and_, or_, update

from db.database import get_db
from models.user import User, ThirdPartyAccount
from ..models.models import DingTalkUserMapping, DingTalkGroup

# 初始化日志
logger = logging.getLogger(__name__)

async def sync_dingtalk_data(
    tenant_id: uuid.UUID,
    project_id: uuid.UUID,
    access_token: str
) -> Dict[str, Any]:
    """
    同步钉钉用户和群组数据
    
    Args:
        tenant_id: 租户ID
        project_id: 项目ID
        access_token: 钉钉API访问令牌
    
    Returns:
        Dict[str, Any]: 同步结果
    """
    result = {
        "success": False,
        "message": "",
        "data": {
            "users_synced": 0,
            "groups_synced": 0,
            "errors": []
        }
    }
    
    async for db in get_db():
        try:
            # 同步用户
            users_result = await sync_users(db, tenant_id, project_id, access_token)
            result["data"]["users_synced"] = users_result["count"]
            
            if not users_result["success"]:
                result["data"]["errors"].append(users_result["error"])
            
            # 同步群组
            groups_result = await sync_groups(db, tenant_id, project_id, access_token)
            result["data"]["groups_synced"] = groups_result["count"]
            
            if not groups_result["success"]:
                result["data"]["errors"].append(groups_result["error"])
            
            # 提交事务
            await db.commit()
            
            # 设置结果
            result["success"] = True
            result["message"] = f"同步成功: {users_result['count']} 个用户, {groups_result['count']} 个群组"
            
            return result
        
        except Exception as e:
            logger.error(f"同步钉钉数据失败: {str(e)}")
            await db.rollback()
            result["message"] = f"同步钉钉数据失败: {str(e)}"
            result["data"]["errors"].append(str(e))
            return result
        finally:
            await db.close()

async def sync_users(
    db: AsyncSession,
    tenant_id: uuid.UUID,
    project_id: uuid.UUID,
    access_token: str
) -> Dict[str, Any]:
    """
    同步钉钉用户
    
    Args:
        db: 数据库会话
        tenant_id: 租户ID
        project_id: 项目ID
        access_token: 钉钉API访问令牌
    
    Returns:
        Dict[str, Any]: 同步结果
    """
    result = {
        "success": False,
        "count": 0,
        "error": ""
    }
    
    try:
        # 获取钉钉用户列表
        dingtalk_users = await get_dingtalk_users(access_token)
        
        if not dingtalk_users["success"]:
            result["error"] = dingtalk_users["error"]
            return result
        
        # 获取系统用户列表
        users_query = select(User).where(
            and_(
                User.tenant_id == tenant_id,
                User.is_active == True
            )
        )
        users_result = await db.execute(users_query)
        users = users_result.scalars().all()
        
        # 获取第三方账号
        third_party_query = select(ThirdPartyAccount).where(
            and_(
                ThirdPartyAccount.tenant_id == tenant_id,
                ThirdPartyAccount.provider == "dingtalk"
            )
        )
        third_party_result = await db.execute(third_party_query)
        third_party_accounts = third_party_result.scalars().all()
        
        # 创建映射: user_id -> third_party_account
        user_third_party_map = {str(account.user_id): account for account in third_party_accounts}
        
        # 同步用户
        synced_count = 0
        
        for user in users:
            # 检查是否有第三方账号
            third_party_account = user_third_party_map.get(str(user.id))
            
            if third_party_account and third_party_account.provider_user_id:
                # 在钉钉用户中查找
                dingtalk_user = next(
                    (du for du in dingtalk_users["users"] if du["userid"] == third_party_account.provider_user_id),
                    None
                )
                
                if dingtalk_user:
                    # 检查是否已有映射
                    mapping_query = select(DingTalkUserMapping).where(
                        and_(
                            DingTalkUserMapping.tenant_id == tenant_id,
                            DingTalkUserMapping.project_id == project_id,
                            DingTalkUserMapping.user_id == user.id
                        )
                    )
                    mapping_result = await db.execute(mapping_query)
                    mapping = mapping_result.scalar_one_or_none()
                    
                    if mapping:
                        # 更新映射
                        mapping.dingtalk_user_id = dingtalk_user["userid"]
                        mapping.dingtalk_name = dingtalk_user.get("name", "")
                        mapping.dingtalk_mobile = dingtalk_user.get("mobile", "")
                        mapping.dingtalk_email = dingtalk_user.get("email", "")
                        mapping.updated_at = datetime.now()
                    else:
                        # 创建映射
                        mapping = DingTalkUserMapping(
                            id=uuid.uuid4(),
                            tenant_id=tenant_id,
                            project_id=project_id,
                            user_id=user.id,
                            dingtalk_user_id=dingtalk_user["userid"],
                            dingtalk_name=dingtalk_user.get("name", ""),
                            dingtalk_mobile=dingtalk_user.get("mobile", ""),
                            dingtalk_email=dingtalk_user.get("email", "")
                        )
                        db.add(mapping)
                    
                    synced_count += 1
        
        result["success"] = True
        result["count"] = synced_count
        
        return result
    
    except Exception as e:
        logger.error(f"同步钉钉用户失败: {str(e)}")
        result["error"] = f"同步钉钉用户失败: {str(e)}"
        return result

async def sync_groups(
    db: AsyncSession,
    tenant_id: uuid.UUID,
    project_id: uuid.UUID,
    access_token: str
) -> Dict[str, Any]:
    """
    同步钉钉群组
    
    Args:
        db: 数据库会话
        tenant_id: 租户ID
        project_id: 项目ID
        access_token: 钉钉API访问令牌
    
    Returns:
        Dict[str, Any]: 同步结果
    """
    result = {
        "success": False,
        "count": 0,
        "error": ""
    }
    
    try:
        # 获取钉钉群组列表
        dingtalk_groups = await get_dingtalk_groups(access_token)
        
        if not dingtalk_groups["success"]:
            result["error"] = dingtalk_groups["error"]
            return result
        
        # 同步群组
        synced_count = 0
        
        for group in dingtalk_groups["groups"]:
            # 检查是否已有群组
            group_query = select(DingTalkGroup).where(
                and_(
                    DingTalkGroup.tenant_id == tenant_id,
                    DingTalkGroup.project_id == project_id,
                    DingTalkGroup.group_id == group["chatid"]
                )
            )
            group_result = await db.execute(group_query)
            existing_group = group_result.scalar_one_or_none()
            
            if existing_group:
                # 更新群组
                existing_group.name = group.get("name", "")
                existing_group.owner_id = group.get("owner", "")
                existing_group.member_count = group.get("memberCount", 0)
                existing_group.updated_at = datetime.now()
            else:
                # 创建群组
                new_group = DingTalkGroup(
                    id=uuid.uuid4(),
                    tenant_id=tenant_id,
                    project_id=project_id,
                    group_id=group["chatid"],
                    name=group.get("name", ""),
                    owner_id=group.get("owner", ""),
                    member_count=group.get("memberCount", 0)
                )
                db.add(new_group)
            
            synced_count += 1
        
        result["success"] = True
        result["count"] = synced_count
        
        return result
    
    except Exception as e:
        logger.error(f"同步钉钉群组失败: {str(e)}")
        result["error"] = f"同步钉钉群组失败: {str(e)}"
        return result

async def get_dingtalk_users(access_token: str) -> Dict[str, Any]:
    """
    获取钉钉用户列表
    
    Args:
        access_token: 钉钉API访问令牌
    
    Returns:
        Dict[str, Any]: 用户列表
    """
    result = {
        "success": False,
        "users": [],
        "error": ""
    }
    
    try:
        # 钉钉API接口
        api_url = f"https://oapi.dingtalk.com/user/list?access_token={access_token}"
        
        async with aiohttp.ClientSession() as session:
            # 获取部门列表
            dept_url = f"https://oapi.dingtalk.com/department/list?access_token={access_token}"
            async with session.get(dept_url) as response:
                dept_data = await response.json()
                
                if dept_data.get("errcode") != 0:
                    result["error"] = f"获取部门列表失败: {dept_data.get('errmsg', '未知错误')}"
                    return result
                
                departments = dept_data.get("department", [])
                
                # 获取每个部门的用户
                all_users = []
                
                for dept in departments:
                    dept_id = dept["id"]
                    user_url = f"{api_url}&department_id={dept_id}"
                    
                    async with session.get(user_url) as user_response:
                        user_data = await user_response.json()
                        
                        if user_data.get("errcode") != 0:
                            logger.warning(f"获取部门 {dept_id} 用户列表失败: {user_data.get('errmsg', '未知错误')}")
                            continue
                        
                        users = user_data.get("userlist", [])
                        all_users.extend(users)
                
                # 去重
                unique_users = []
                user_ids = set()
                
                for user in all_users:
                    if user["userid"] not in user_ids:
                        unique_users.append(user)
                        user_ids.add(user["userid"])
                
                result["success"] = True
                result["users"] = unique_users
                
                return result
    
    except Exception as e:
        logger.error(f"获取钉钉用户列表失败: {str(e)}")
        result["error"] = f"获取钉钉用户列表失败: {str(e)}"
        return result

async def get_dingtalk_groups(access_token: str) -> Dict[str, Any]:
    """
    获取钉钉群组列表
    
    Args:
        access_token: 钉钉API访问令牌
    
    Returns:
        Dict[str, Any]: 群组列表
    """
    result = {
        "success": False,
        "groups": [],
        "error": ""
    }
    
    try:
        # 钉钉API接口
        api_url = f"https://oapi.dingtalk.com/chat/list?access_token={access_token}"
        
        async with aiohttp.ClientSession() as session:
            async with session.get(api_url) as response:
                data = await response.json()
                
                if data.get("errcode") != 0:
                    result["error"] = f"获取群组列表失败: {data.get('errmsg', '未知错误')}"
                    return result
                
                groups = data.get("chatlist", [])
                
                # 获取每个群组的详细信息
                detailed_groups = []
                
                for group in groups:
                    chat_id = group["chatid"]
                    detail_url = f"https://oapi.dingtalk.com/chat/get?access_token={access_token}&chatid={chat_id}"
                    
                    async with session.get(detail_url) as detail_response:
                        detail_data = await detail_response.json()
                        
                        if detail_data.get("errcode") != 0:
                            logger.warning(f"获取群组 {chat_id} 详情失败: {detail_data.get('errmsg', '未知错误')}")
                            detailed_groups.append(group)
                        else:
                            detailed_groups.append(detail_data.get("chat", group))
                
                result["success"] = True
                result["groups"] = detailed_groups
                
                return result
    
    except Exception as e:
        logger.error(f"获取钉钉群组列表失败: {str(e)}")
        result["error"] = f"获取钉钉群组列表失败: {str(e)}"
        return result
