"""
初始化系统配置数据
"""
from uuid import UUID
from sqlalchemy.orm import Session
from models.system_config import SystemConfig

def init_theme_settings(db: Session, project_id: UUID):
    """初始化主题设置"""
    theme_configs = {
        "mode": {"value": "light"},
        "primaryColor": {"value": "#1890ff"},
        "borderRadius": {"value": 4},
        "compactMode": {"value": False},
        "customFont": {"value": False},
        "fontFamily": {"value": "default"}
    }
    
    # 检查配置是否已存在，不存在则创建
    for key, value in theme_configs.items():
        config = db.query(SystemConfig).filter(
            SystemConfig.project_id == project_id,
            SystemConfig.config_type == "theme",
            SystemConfig.config_key == key
        ).first()
        
        if not config:
            config = SystemConfig(
                project_id=project_id,
                config_type="theme",
                config_key=key,
                config_value=value
            )
            db.add(config)
    
    db.commit()
    return db.query(SystemConfig).filter(
        SystemConfig.project_id == project_id,
        SystemConfig.config_type == "theme"
    ).all()

def init_notification_settings(db: Session, project_id: UUID):
    """初始化通知设置"""
    notification_configs = {
        "emailNotification": {"value": True},
        "smsNotification": {"value": False},
        "pushNotification": {"value": True},
        "notificationFrequency": {"value": "immediate"},
        "dailyDigest": {"value": True},
        "soundEnabled": {"value": True}
    }
    
    # 检查配置是否已存在，不存在则创建
    for key, value in notification_configs.items():
        config = db.query(SystemConfig).filter(
            SystemConfig.project_id == project_id,
            SystemConfig.config_type == "notification",
            SystemConfig.config_key == key
        ).first()
        
        if not config:
            config = SystemConfig(
                project_id=project_id,
                config_type="notification",
                config_key=key,
                config_value=value
            )
            db.add(config)
    
    db.commit()
    return db.query(SystemConfig).filter(
        SystemConfig.project_id == project_id,
        SystemConfig.config_type == "notification"
    ).all()

def init_security_settings(db: Session, project_id: UUID):
    """初始化安全设置"""
    security_configs = {
        "passwordExpiration": {"value": 90},
        "twoFactorAuth": {"value": False},
        "loginAttempts": {"value": 5},
        "sessionTimeout": {"value": 30},
        "ipRestriction": {"value": False}
    }
    
    # 检查配置是否已存在，不存在则创建
    for key, value in security_configs.items():
        config = db.query(SystemConfig).filter(
            SystemConfig.project_id == project_id,
            SystemConfig.config_type == "security",
            SystemConfig.config_key == key
        ).first()
        
        if not config:
            config = SystemConfig(
                project_id=project_id,
                config_type="security",
                config_key=key,
                config_value=value
            )
            db.add(config)
    
    db.commit()
    return db.query(SystemConfig).filter(
        SystemConfig.project_id == project_id,
        SystemConfig.config_type == "security"
    ).all()

def init_general_settings(db: Session, project_id: UUID):
    """初始化常规设置"""
    general_configs = {
        "language": {"value": "zh_CN"},
        "timezone": {"value": "Asia/Shanghai"},
        "dateFormat": {"value": "YYYY-MM-DD"},
        "timeFormat": {"value": "24hour"}
    }
    
    # 检查配置是否已存在，不存在则创建
    for key, value in general_configs.items():
        config = db.query(SystemConfig).filter(
            SystemConfig.project_id == project_id,
            SystemConfig.config_type == "general",
            SystemConfig.config_key == key
        ).first()
        
        if not config:
            config = SystemConfig(
                project_id=project_id,
                config_type="general",
                config_key=key,
                config_value=value
            )
            db.add(config)
    
    db.commit()
    return db.query(SystemConfig).filter(
        SystemConfig.project_id == project_id,
        SystemConfig.config_type == "general"
    ).all()

def init_third_party_login_settings(db: Session, project_id: UUID):
    """初始化第三方登录设置"""
    third_party_login_configs = {
        "wechat_work": {
            "value": {
                "enabled": False,
                "corp_id": "",
                "agent_id": "",
                "secret": "",
                "redirect_uri": ""
            }
        },
        "dingtalk": {
            "value": {
                "enabled": False,
                "app_key": "",
                "app_secret": "",
                "redirect_uri": ""
            }
        },
        "feishu": {
            "value": {
                "enabled": False,
                "app_id": "",
                "app_secret": "",
                "redirect_uri": ""
            }
        },
        "wechat": {
            "value": {
                "enabled": False,
                "app_id": "",
                "app_secret": "",
                "redirect_uri": ""
            }
        }
    }
    
    # 检查配置是否已存在，不存在则创建
    for key, value in third_party_login_configs.items():
        config = db.query(SystemConfig).filter(
            SystemConfig.project_id == project_id,
            SystemConfig.config_type == "third_party_login",
            SystemConfig.config_key == key
        ).first()
        
        if not config:
            config = SystemConfig(
                project_id=project_id,
                config_type="third_party_login",
                config_key=key,
                config_value=value
            )
            db.add(config)
    
    db.commit()
    return db.query(SystemConfig).filter(
        SystemConfig.project_id == project_id,
        SystemConfig.config_type == "third_party_login"
    ).all()

def init_system_configs(db: Session, project_id: UUID):
    """初始化所有系统配置"""
    init_theme_settings(db, project_id)
    init_notification_settings(db, project_id)
    init_security_settings(db, project_id)
    init_general_settings(db, project_id)
    init_third_party_login_settings(db, project_id)
    
    return db.query(SystemConfig).filter(
        SystemConfig.project_id == project_id
    ).all()
