"""Add purchase_order_id to arrival_confirmations

Revision ID: add_purchase_order_relation
Revises: add_third_party_login_fields
Create Date: 2024-01-01 00:00:00.000000

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = 'add_purchase_order_relation'
down_revision = 'add_third_party_login_fields'
branch_labels = None
depends_on = None


def upgrade():
    # Add purchase_order_id column to arrival_confirmations table
    op.add_column('arrival_confirmations', 
                  sa.Column('purchase_order_id', 
                           postgresql.UUID(as_uuid=True), 
                           nullable=True,
                           comment='关联采购单ID'))
    
    # Create index on purchase_order_id
    op.create_index('ix_arrival_confirmations_purchase_order_id', 
                    'arrival_confirmations', 
                    ['purchase_order_id'])
    
    # Create foreign key constraint
    op.create_foreign_key('fk_arrival_confirmations_purchase_order_id',
                         'arrival_confirmations',
                         'purchase_orders',
                         ['purchase_order_id'],
                         ['id'],
                         ondelete='SET NULL')


def downgrade():
    # Drop foreign key constraint
    op.drop_constraint('fk_arrival_confirmations_purchase_order_id', 
                      'arrival_confirmations', 
                      type_='foreignkey')
    
    # Drop index
    op.drop_index('ix_arrival_confirmations_purchase_order_id', 
                  table_name='arrival_confirmations')
    
    # Drop column
    op.drop_column('arrival_confirmations', 'purchase_order_id') 