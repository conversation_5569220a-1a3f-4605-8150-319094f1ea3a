"""Add third party login fields to users

Revision ID: add_third_party_login_fields
Revises: 
Create Date: 2024-01-20 12:00:00.000000

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'add_third_party_login_fields'
down_revision = None
branch_labels = None
depends_on = None


def upgrade():
    # 添加钉钉和微信登录相关字段到users表
    op.add_column('users', sa.Column('dingtalk_union_id', sa.String(100), nullable=True))
    op.add_column('users', sa.Column('dingtalk_user_id', sa.String(100), nullable=True))
    op.add_column('users', sa.Column('wechat_unionid', sa.String(100), nullable=True))
    op.add_column('users', sa.Column('wechat_openid', sa.String(100), nullable=True))
    
    # 添加唯一约束
    op.create_unique_constraint('uq_users_dingtalk_union_id', 'users', ['dingtalk_union_id'])
    op.create_unique_constraint('uq_users_wechat_unionid', 'users', ['wechat_unionid'])
    op.create_unique_constraint('uq_users_wechat_openid', 'users', ['wechat_openid'])


def downgrade():
    # 删除唯一约束
    op.drop_constraint('uq_users_wechat_openid', 'users', type_='unique')
    op.drop_constraint('uq_users_wechat_unionid', 'users', type_='unique')
    op.drop_constraint('uq_users_dingtalk_union_id', 'users', type_='unique')
    
    # 删除字段
    op.drop_column('users', 'wechat_openid')
    op.drop_column('users', 'wechat_unionid')
    op.drop_column('users', 'dingtalk_user_id')
    op.drop_column('users', 'dingtalk_union_id') 