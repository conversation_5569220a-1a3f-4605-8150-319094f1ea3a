#!/usr/bin/env python
# -*- coding: utf-8 -*-

import io
import logging
import pandas as pd
from typing import List, Any, Optional, Dict, Union
from openpyxl.styles import Font, PatternFill, Border, Side, Alignment

logger = logging.getLogger(__name__)

class ExcelService:
    """
    Excel服务，用于生成Excel文件
    """

    @staticmethod
    def generate_excel(
        file_obj: io.BytesIO,
        sheet_name: str,
        headers: List[str],
        data: List[List[Any]],
        title: Optional[str] = None,
        column_widths: Optional[Dict[int, int]] = None
    ) -> None:
        """
        生成Excel文件

        Args:
            file_obj: 文件对象
            sheet_name: 工作表名称
            headers: 表头
            data: 数据
            title: 标题
            column_widths: 列宽
        """
        try:
            # 将数据转换为DataFrame
            df = pd.DataFrame(data, columns=headers)

            # 创建Excel写入器，使用openpyxl引擎
            with pd.ExcelWriter(file_obj, engine='openpyxl') as writer:
                # 写入数据
                start_row = 2 if title else 0
                df.to_excel(writer, sheet_name=sheet_name, index=False, startrow=start_row)

                # 获取工作簿和工作表
                workbook = writer.book
                worksheet = writer.sheets[sheet_name]

                # 定义样式
                title_font = Font(bold=True, size=14)
                title_fill = PatternFill(start_color="E0E0E0", end_color="E0E0E0", fill_type="solid")
                title_alignment = Alignment(horizontal='center', vertical='center')
                
                header_font = Font(bold=True)
                header_fill = PatternFill(start_color="D9D9D9", end_color="D9D9D9", fill_type="solid")
                header_alignment = Alignment(horizontal='center', vertical='center')
                
                border = Border(
                    left=Side(style='thin'),
                    right=Side(style='thin'),
                    top=Side(style='thin'),
                    bottom=Side(style='thin')
                )

                # 设置标题
                if title:
                    # 合并单元格并设置标题
                    worksheet.merge_cells(start_row=1, start_column=1, end_row=1, end_column=len(headers))
                    title_cell = worksheet.cell(row=1, column=1, value=title)
                    title_cell.font = title_font
                    title_cell.fill = title_fill
                    title_cell.alignment = title_alignment
                    title_cell.border = border
                    
                    # 设置标题行高
                    worksheet.row_dimensions[1].height = 30

                # 设置表头格式
                header_row = start_row + 1
                for col_idx, header in enumerate(headers, 1):
                    cell = worksheet.cell(row=header_row, column=col_idx)
                    cell.font = header_font
                    cell.fill = header_fill
                    cell.alignment = header_alignment
                    cell.border = border

                # 设置数据格式
                data_start_row = header_row + 1
                for row_idx in range(len(data)):
                    for col_idx in range(len(headers)):
                        cell = worksheet.cell(row=data_start_row + row_idx, column=col_idx + 1)
                        cell.border = border
                        cell.alignment = Alignment(horizontal='left', vertical='center')

                # 设置列宽
                if column_widths:
                    for col_idx, width in column_widths.items():
                        col_letter = worksheet.cell(row=1, column=col_idx).column_letter
                        worksheet.column_dimensions[col_letter].width = width
                else:
                    # 自动设置列宽
                    for col_idx in range(len(headers)):
                        col_letter = worksheet.cell(row=1, column=col_idx + 1).column_letter
                        worksheet.column_dimensions[col_letter].width = 15

        except Exception as e:
            logger.error(f"生成Excel文件失败: {e}")
            raise
