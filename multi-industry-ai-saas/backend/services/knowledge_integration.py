#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
知识库和 AI 知识库整合服务
"""

import logging
import uuid
from typing import Optional, Dict, Any, List
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from sqlalchemy import and_

from models.knowledge_base import KnowledgeDocument
from models.ai import AIKnowledgeBase, AIKnowledgeDocument
from services.knowledge_base import KnowledgeBaseService
from services.ai.knowledge_service import AIKnowledgeService
from schemas.ai import AIKnowledgeDocumentCreate

logger = logging.getLogger(__name__)

class KnowledgeIntegrationService:
    """
    知识库和 AI 知识库整合服务
    """

    @staticmethod
    async def sync_document_to_ai_knowledge_base(
        db: AsyncSession,
        document_id: uuid.UUID,
        knowledge_base_id: uuid.UUID,
        user_id: uuid.UUID
    ) -> Optional[Dict[str, Any]]:
        """
        将普通知识库文档同步到 AI 知识库

        Args:
            db: 数据库会话
            document_id: 普通知识库文档 ID
            knowledge_base_id: AI 知识库 ID
            user_id: 用户 ID

        Returns:
            同步后的 AI 知识库文档
        """
        try:
            # 获取普通知识库文档
            document = await KnowledgeBaseService.get_document_by_id(
                db=db,
                document_id=document_id,
                project_id=None  # 这里不需要 project_id 过滤，因为已经有 document_id
            )

            if not document:
                logger.error(f"文档 {document_id} 不存在")
                return None

            # 获取 AI 知识库
            knowledge_base = await AIKnowledgeService.get_knowledge_base(
                db=db,
                knowledge_base_id=knowledge_base_id
            )

            if not knowledge_base:
                logger.error(f"AI 知识库 {knowledge_base_id} 不存在")
                return None

            # 检查文档是否已经同步到 AI 知识库
            query = select(AIKnowledgeDocument).where(
                and_(
                    AIKnowledgeDocument.knowledge_base_id == knowledge_base_id,
                    AIKnowledgeDocument.request_metadata.contains({"source_document_id": str(document_id)})
                )
            )

            result = await db.execute(query)
            existing_ai_document = result.scalars().first()

            if existing_ai_document:
                logger.info(f"文档 {document_id} 已经同步到 AI 知识库 {knowledge_base_id}")

                # 更新 AI 知识库文档
                update_data = {
                    "title": document["title"],
                    "content": document["content"],
                }

                if document["file_url"]:
                    update_data["file_path"] = document["file_url"]

                updated_ai_document = await AIKnowledgeService.update_document(
                    db=db,
                    document_id=existing_ai_document.id,
                    document_data=update_data
                )

                return updated_ai_document

            # 创建 AI 知识库文档
            ai_document_data = AIKnowledgeDocumentCreate(
                knowledge_base_id=knowledge_base_id,
                title=document["title"],
                content=document["content"],
                file_path=document["file_url"] if document["file_url"] else None,
                file_type=document["file_type"] if document["file_type"] else None,
                file_size=document["file_size"] if document["file_size"] else None,
                request_metadata={
                    "source_document_id": str(document_id),
                    "source_project_id": str(document["project_id"]),
                    "synced_by": str(user_id)
                }
            )

            ai_document = await AIKnowledgeService.create_document(
                db=db,
                document_data=ai_document_data
            )

            logger.info(f"文档 {document_id} 已同步到 AI 知识库 {knowledge_base_id}")
            return ai_document
        except Exception as e:
            logger.error(f"同步文档到 AI 知识库失败: {str(e)}")
            raise

    @staticmethod
    async def get_ai_knowledge_bases_for_project(
        db: AsyncSession,
        project_id: uuid.UUID
    ) -> List[Dict[str, Any]]:
        """
        获取项目可用的 AI 知识库列表

        Args:
            db: 数据库会话
            project_id: 项目 ID

        Returns:
            AI 知识库列表
        """
        try:
            # 获取项目的 AI 知识库
            project_knowledge_bases, _ = await AIKnowledgeService.get_knowledge_bases(
                db=db,
                project_id=project_id,
                status="active"
            )

            # 获取项目所属租户的 AI 知识库
            # 首先获取项目信息
            from models.project import Project
            project_query = select(Project).where(Project.id == project_id)
            project_result = await db.execute(project_query)
            project = project_result.scalars().first()

            if not project:
                return [kb for kb in project_knowledge_bases]

            # 获取租户的 AI 知识库
            tenant_knowledge_bases, _ = await AIKnowledgeService.get_knowledge_bases(
                db=db,
                tenant_id=project.tenant_id,
                project_id=None,  # 只获取租户级的知识库
                status="active"
            )

            # 合并项目和租户的 AI 知识库
            all_knowledge_bases = list(project_knowledge_bases) + list(tenant_knowledge_bases)

            return all_knowledge_bases
        except Exception as e:
            logger.error(f"获取项目可用的 AI 知识库列表失败: {str(e)}")
            raise

    @staticmethod
    async def get_document_ai_knowledge_bases(
        db: AsyncSession,
        document_id: uuid.UUID
    ) -> List[Dict[str, Any]]:
        """
        获取文档已同步到的 AI 知识库列表

        Args:
            db: 数据库会话
            document_id: 文档 ID

        Returns:
            AI 知识库列表
        """
        try:
            # 查询已同步到的 AI 知识库文档
            query = select(AIKnowledgeDocument).where(
                AIKnowledgeDocument.request_metadata.contains({"source_document_id": str(document_id)})
            )

            result = await db.execute(query)
            ai_documents = result.scalars().all()

            # 获取对应的 AI 知识库
            knowledge_base_ids = [doc.knowledge_base_id for doc in ai_documents]

            knowledge_bases = []
            for kb_id in knowledge_base_ids:
                kb = await AIKnowledgeService.get_knowledge_base(db, kb_id)
                if kb:
                    knowledge_bases.append(kb)

            return knowledge_bases
        except Exception as e:
            logger.error(f"获取文档已同步到的 AI 知识库列表失败: {str(e)}")
            raise
