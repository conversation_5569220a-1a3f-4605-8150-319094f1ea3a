from typing import List, Optional, Dict, Any
import uuid
from sqlalchemy import select, func, or_, and_
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import joinedload

from models.sales_management import SalesChannel, PaymentMethod


class SalesChannelService:
    @staticmethod
    async def create_sales_channel(
        db: AsyncSession,
        channel_data: Dict[str, Any],
        project_id: uuid.UUID
    ) -> SalesChannel:
        """创建销售渠道"""
        db_channel = SalesChannel(
            project_id=project_id,
            name=channel_data.get("name"),
            code=channel_data.get("code"),
            type=channel_data.get("type", "offline"),
            description=channel_data.get("description"),
            is_active=channel_data.get("is_active", True),
            config=channel_data.get("config")
        )
        
        db.add(db_channel)
        await db.commit()
        await db.refresh(db_channel)
        
        return db_channel

    @staticmethod
    async def get_sales_channels(
        db: AsyncSession,
        project_id: uuid.UUID,
        skip: int = 0,
        limit: int = 100,
        search: Optional[str] = None,
        type: Optional[str] = None,
        is_active: Optional[bool] = None
    ) -> List[SalesChannel]:
        """获取销售渠道列表"""
        query = select(SalesChannel).where(SalesChannel.project_id == project_id)
        
        # 应用过滤条件
        if search:
            search_term = f"%{search}%"
            query = query.where(
                or_(
                    SalesChannel.name.ilike(search_term),
                    SalesChannel.code.ilike(search_term),
                    SalesChannel.description.ilike(search_term)
                )
            )
        
        if type:
            query = query.where(SalesChannel.type == type)
        
        if is_active is not None:
            query = query.where(SalesChannel.is_active == is_active)
        
        # 应用排序和分页
        query = query.order_by(SalesChannel.name).offset(skip).limit(limit)
        
        result = await db.execute(query)
        return result.scalars().all()

    @staticmethod
    async def count_sales_channels(
        db: AsyncSession,
        project_id: uuid.UUID,
        search: Optional[str] = None,
        type: Optional[str] = None,
        is_active: Optional[bool] = None
    ) -> int:
        """计算销售渠道总数"""
        query = select(func.count(SalesChannel.id)).where(SalesChannel.project_id == project_id)
        
        # 应用过滤条件
        if search:
            search_term = f"%{search}%"
            query = query.where(
                or_(
                    SalesChannel.name.ilike(search_term),
                    SalesChannel.code.ilike(search_term),
                    SalesChannel.description.ilike(search_term)
                )
            )
        
        if type:
            query = query.where(SalesChannel.type == type)
        
        if is_active is not None:
            query = query.where(SalesChannel.is_active == is_active)
        
        result = await db.execute(query)
        return result.scalar_one()

    @staticmethod
    async def get_sales_channel_by_id(
        db: AsyncSession,
        channel_id: uuid.UUID
    ) -> Optional[SalesChannel]:
        """根据ID获取销售渠道"""
        query = select(SalesChannel).where(SalesChannel.id == channel_id)
        result = await db.execute(query)
        return result.scalar_one_or_none()

    @staticmethod
    async def update_sales_channel(
        db: AsyncSession,
        channel_id: uuid.UUID,
        channel_data: Dict[str, Any]
    ) -> Optional[SalesChannel]:
        """更新销售渠道"""
        query = select(SalesChannel).where(SalesChannel.id == channel_id)
        result = await db.execute(query)
        db_channel = result.scalar_one_or_none()
        
        if not db_channel:
            return None
        
        # 更新字段
        for key, value in channel_data.items():
            if hasattr(db_channel, key) and value is not None:
                setattr(db_channel, key, value)
        
        await db.commit()
        await db.refresh(db_channel)
        
        return db_channel

    @staticmethod
    async def delete_sales_channel(
        db: AsyncSession,
        channel_id: uuid.UUID
    ) -> bool:
        """删除销售渠道"""
        query = select(SalesChannel).where(SalesChannel.id == channel_id)
        result = await db.execute(query)
        db_channel = result.scalar_one_or_none()
        
        if not db_channel:
            return False
        
        await db.delete(db_channel)
        await db.commit()
        
        return True


class PaymentMethodService:
    @staticmethod
    async def create_payment_method(
        db: AsyncSession,
        payment_method_data: Dict[str, Any],
        project_id: uuid.UUID
    ) -> PaymentMethod:
        """创建支付方式"""
        db_payment_method = PaymentMethod(
            project_id=project_id,
            name=payment_method_data.get("name"),
            code=payment_method_data.get("code"),
            icon=payment_method_data.get("icon"),
            description=payment_method_data.get("description"),
            is_active=payment_method_data.get("is_active", True),
            is_default=payment_method_data.get("is_default", False),
            sort_order=payment_method_data.get("sort_order", 0),
            config=payment_method_data.get("config")
        )
        
        db.add(db_payment_method)
        await db.commit()
        await db.refresh(db_payment_method)
        
        return db_payment_method

    @staticmethod
    async def get_payment_methods(
        db: AsyncSession,
        project_id: uuid.UUID,
        skip: int = 0,
        limit: int = 100,
        search: Optional[str] = None,
        is_active: Optional[bool] = None,
        is_default: Optional[bool] = None
    ) -> List[PaymentMethod]:
        """获取支付方式列表"""
        query = select(PaymentMethod).where(PaymentMethod.project_id == project_id)
        
        # 应用过滤条件
        if search:
            search_term = f"%{search}%"
            query = query.where(
                or_(
                    PaymentMethod.name.ilike(search_term),
                    PaymentMethod.code.ilike(search_term),
                    PaymentMethod.description.ilike(search_term)
                )
            )
        
        if is_active is not None:
            query = query.where(PaymentMethod.is_active == is_active)
        
        if is_default is not None:
            query = query.where(PaymentMethod.is_default == is_default)
        
        # 应用排序和分页
        query = query.order_by(PaymentMethod.sort_order, PaymentMethod.name).offset(skip).limit(limit)
        
        result = await db.execute(query)
        return result.scalars().all()

    @staticmethod
    async def count_payment_methods(
        db: AsyncSession,
        project_id: uuid.UUID,
        search: Optional[str] = None,
        is_active: Optional[bool] = None,
        is_default: Optional[bool] = None
    ) -> int:
        """计算支付方式总数"""
        query = select(func.count(PaymentMethod.id)).where(PaymentMethod.project_id == project_id)
        
        # 应用过滤条件
        if search:
            search_term = f"%{search}%"
            query = query.where(
                or_(
                    PaymentMethod.name.ilike(search_term),
                    PaymentMethod.code.ilike(search_term),
                    PaymentMethod.description.ilike(search_term)
                )
            )
        
        if is_active is not None:
            query = query.where(PaymentMethod.is_active == is_active)
        
        if is_default is not None:
            query = query.where(PaymentMethod.is_default == is_default)
        
        result = await db.execute(query)
        return result.scalar_one()

    @staticmethod
    async def get_payment_method_by_id(
        db: AsyncSession,
        payment_method_id: uuid.UUID
    ) -> Optional[PaymentMethod]:
        """根据ID获取支付方式"""
        query = select(PaymentMethod).where(PaymentMethod.id == payment_method_id)
        result = await db.execute(query)
        return result.scalar_one_or_none()

    @staticmethod
    async def update_payment_method(
        db: AsyncSession,
        payment_method_id: uuid.UUID,
        payment_method_data: Dict[str, Any]
    ) -> Optional[PaymentMethod]:
        """更新支付方式"""
        query = select(PaymentMethod).where(PaymentMethod.id == payment_method_id)
        result = await db.execute(query)
        db_payment_method = result.scalar_one_or_none()
        
        if not db_payment_method:
            return None
        
        # 更新字段
        for key, value in payment_method_data.items():
            if hasattr(db_payment_method, key) and value is not None:
                setattr(db_payment_method, key, value)
        
        await db.commit()
        await db.refresh(db_payment_method)
        
        return db_payment_method

    @staticmethod
    async def delete_payment_method(
        db: AsyncSession,
        payment_method_id: uuid.UUID
    ) -> bool:
        """删除支付方式"""
        query = select(PaymentMethod).where(PaymentMethod.id == payment_method_id)
        result = await db.execute(query)
        db_payment_method = result.scalar_one_or_none()
        
        if not db_payment_method:
            return False
        
        await db.delete(db_payment_method)
        await db.commit()
        
        return True
