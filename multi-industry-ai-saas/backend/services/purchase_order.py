#!/usr/bin/env python
# -*- coding: utf-8 -*-

import logging
import uuid
from typing import List, Optional, Dict, Any, Tuple
from datetime import datetime, date, timedelta
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from sqlalchemy import or_, and_, func, desc, asc
from sqlalchemy.orm import joinedload
import pandas as pd

from models.purchase_order import PurchaseOrder, PurchaseOrderItem
from models.supplier import Supplier
from models.warehouse import Warehouse
from models.user import User
from models.product import Product
from schemas.purchase_order import PurchaseOrderCreate, PurchaseOrderUpdate, PurchaseOrderStatusUpdate, PurchaseOrderPaymentUpdate

logger = logging.getLogger(__name__)

class PurchaseOrderService:
    @staticmethod
    async def generate_order_number() -> str:
        """生成订单编号"""
        # 生成格式为 PO + 年月日 + 6位随机数
        now = datetime.now()
        date_str = now.strftime("%Y%m%d")
        random_str = str(uuid.uuid4().int)[:6]
        return f"PO{date_str}{random_str}"

    @staticmethod
    async def create_purchase_order(
        db: AsyncSession,
        order_data: PurchaseOrderCreate,
        project_id: uuid.UUID,
        user_id: uuid.UUID
    ) -> PurchaseOrder:
        """创建采购订单"""
        # 生成订单编号
        order_number = await PurchaseOrderService.generate_order_number()

        # 计算订单金额
        total_amount = 0.0
        discount_amount = 0.0
        tax_amount = 0.0

        # 创建订单
        db_order = PurchaseOrder(
            order_number=order_number,
            project_id=project_id,
            warehouse_id=order_data.warehouse_id,
            order_date=order_data.order_date,
            expected_delivery_date=order_data.expected_delivery_date,
            status=order_data.status,
            payment_method=order_data.payment_method,
            notes=order_data.notes,
            created_by=user_id,
            updated_by=user_id
        )

        db.add(db_order)
        await db.flush()

        # 创建订单项
        for item_data in order_data.items:
            # 计算项目金额
            item_total = item_data.quantity * item_data.unit_price
            item_discount = item_total * (item_data.discount_rate / 100)
            item_tax = (item_total - item_discount) * (item_data.tax_rate / 100)
            item_final = item_total - item_discount + item_tax

            total_amount += item_total
            discount_amount += item_discount
            tax_amount += item_tax

            db_item = PurchaseOrderItem(
                purchase_order_id=db_order.id,
                product_id=item_data.product_id,
                supplier_id=item_data.supplier_id,
                category_id=item_data.category_id,
                product_name=item_data.product_name,
                product_code=item_data.product_code,
                product_unit=item_data.product_unit,
                product_specification=item_data.product_specification,
                quantity=item_data.quantity,
                unit_price=item_data.unit_price,
                discount_rate=item_data.discount_rate,
                tax_rate=item_data.tax_rate,
                total_amount=item_final,
                notes=item_data.notes
            )

            db.add(db_item)

        # 更新订单金额
        shipping_fee = 0.0  # 可以根据需要计算运费
        final_amount = total_amount - discount_amount + tax_amount + shipping_fee

        db_order.total_amount = total_amount
        db_order.discount_amount = discount_amount
        db_order.tax_amount = tax_amount
        db_order.shipping_fee = shipping_fee
        db_order.final_amount = final_amount

        await db.commit()
        await db.refresh(db_order)

        return db_order

    @staticmethod
    async def get_purchase_orders(
        db: AsyncSession,
        project_id: uuid.UUID,
        skip: int = 0,
        limit: int = 100,
        warehouse_id: Optional[uuid.UUID] = None,
        status: Optional[str] = None,
        payment_status: Optional[str] = None,
        search: Optional[str] = None,
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None,
        sort_by: str = "created_at",
        sort_order: str = "desc"
    ) -> List[PurchaseOrder]:
        """获取采购订单列表"""
        query = select(PurchaseOrder).where(PurchaseOrder.project_id == project_id)

        # 应用过滤条件
        if warehouse_id:
            query = query.where(PurchaseOrder.warehouse_id == warehouse_id)

        if status:
            query = query.where(PurchaseOrder.status == status)

        if payment_status:
            query = query.where(PurchaseOrder.payment_status == payment_status)

        if search:
            search_term = f"%{search}%"
            query = query.where(
                or_(
                    PurchaseOrder.order_number.ilike(search_term),
                    PurchaseOrder.notes.ilike(search_term)
                )
            )

        if start_date:
            query = query.where(PurchaseOrder.order_date >= start_date)

        if end_date:
            query = query.where(PurchaseOrder.order_date <= end_date)

        # 应用排序
        if sort_order.lower() == "asc":
            query = query.order_by(asc(getattr(PurchaseOrder, sort_by)))
        else:
            query = query.order_by(desc(getattr(PurchaseOrder, sort_by)))

        # 应用分页
        query = query.offset(skip).limit(limit)

        # 加载关联数据
        query = query.options(
            joinedload(PurchaseOrder.warehouse),
            joinedload(PurchaseOrder.creator),
            joinedload(PurchaseOrder.updater),
            joinedload(PurchaseOrder.items).joinedload(PurchaseOrderItem.supplier),
            joinedload(PurchaseOrder.items).joinedload(PurchaseOrderItem.category)
        )

        result = await db.execute(query)
        return result.scalars().unique().all()

    @staticmethod
    async def count_purchase_orders(
        db: AsyncSession,
        project_id: uuid.UUID,
        warehouse_id: Optional[uuid.UUID] = None,
        status: Optional[str] = None,
        payment_status: Optional[str] = None,
        search: Optional[str] = None,
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None
    ) -> int:
        """计算采购订单总数"""
        query = select(func.count(PurchaseOrder.id)).where(PurchaseOrder.project_id == project_id)

        # 应用过滤条件
        if warehouse_id:
            query = query.where(PurchaseOrder.warehouse_id == warehouse_id)

        if status:
            query = query.where(PurchaseOrder.status == status)

        if payment_status:
            query = query.where(PurchaseOrder.payment_status == payment_status)

        if search:
            search_term = f"%{search}%"
            query = query.where(
                or_(
                    PurchaseOrder.order_number.ilike(search_term),
                    PurchaseOrder.notes.ilike(search_term)
                )
            )

        if start_date:
            query = query.where(PurchaseOrder.order_date >= start_date)

        if end_date:
            query = query.where(PurchaseOrder.order_date <= end_date)

        result = await db.execute(query)
        return result.scalar_one()

    @staticmethod
    async def get_purchase_order_by_id(
        db: AsyncSession,
        order_id: uuid.UUID
    ) -> Optional[PurchaseOrder]:
        """根据ID获取采购订单"""
        query = select(PurchaseOrder).where(PurchaseOrder.id == order_id)
        query = query.options(
            joinedload(PurchaseOrder.warehouse),
            joinedload(PurchaseOrder.creator),
            joinedload(PurchaseOrder.updater),
            joinedload(PurchaseOrder.items).joinedload(PurchaseOrderItem.product),
            joinedload(PurchaseOrder.items).joinedload(PurchaseOrderItem.supplier),
            joinedload(PurchaseOrder.items).joinedload(PurchaseOrderItem.category)
        )

        result = await db.execute(query)
        return result.scalars().unique().first()

    @staticmethod
    async def update_purchase_order(
        db: AsyncSession,
        order_id: uuid.UUID,
        order_data: PurchaseOrderUpdate,
        user_id: uuid.UUID
    ) -> Optional[PurchaseOrder]:
        """更新采购订单"""
        query = select(PurchaseOrder).where(PurchaseOrder.id == order_id)
        result = await db.execute(query)
        db_order = result.scalar_one_or_none()

        if not db_order:
            return None

        # 更新订单信息
        update_data = order_data.dict(exclude_unset=True)

        for key, value in update_data.items():
            setattr(db_order, key, value)

        db_order.updated_by = user_id
        db_order.updated_at = datetime.utcnow()

        await db.commit()
        await db.refresh(db_order)

        return db_order

    @staticmethod
    async def update_purchase_order_status(
        db: AsyncSession,
        order_id: uuid.UUID,
        status_data: PurchaseOrderStatusUpdate,
        user_id: uuid.UUID
    ) -> Optional[PurchaseOrder]:
        """更新采购订单状态"""
        query = select(PurchaseOrder).where(PurchaseOrder.id == order_id)
        result = await db.execute(query)
        db_order = result.scalar_one_or_none()

        if not db_order:
            return None

        # 更新状态
        db_order.status = status_data.status

        if status_data.actual_delivery_date:
            db_order.actual_delivery_date = status_data.actual_delivery_date

        if status_data.notes:
            db_order.notes = status_data.notes

        db_order.updated_by = user_id
        db_order.updated_at = datetime.utcnow()

        await db.commit()
        await db.refresh(db_order)

        return db_order

    @staticmethod
    async def update_purchase_order_payment(
        db: AsyncSession,
        order_id: uuid.UUID,
        payment_data: PurchaseOrderPaymentUpdate,
        user_id: uuid.UUID
    ) -> Optional[PurchaseOrder]:
        """更新采购订单支付状态"""
        query = select(PurchaseOrder).where(PurchaseOrder.id == order_id)
        result = await db.execute(query)
        db_order = result.scalar_one_or_none()

        if not db_order:
            return None

        # 更新支付状态
        db_order.payment_status = payment_data.payment_status

        if payment_data.payment_method:
            db_order.payment_method = payment_data.payment_method

        if payment_data.payment_date:
            db_order.payment_date = payment_data.payment_date

        if payment_data.notes:
            db_order.notes = payment_data.notes

        db_order.updated_by = user_id
        db_order.updated_at = datetime.utcnow()

        await db.commit()
        await db.refresh(db_order)

        return db_order

    @staticmethod
    async def delete_purchase_order(
        db: AsyncSession,
        order_id: uuid.UUID
    ) -> bool:
        """删除采购订单"""
        query = select(PurchaseOrder).where(PurchaseOrder.id == order_id)
        result = await db.execute(query)
        db_order = result.scalar_one_or_none()

        if not db_order:
            return False

        await db.delete(db_order)
        await db.commit()

        return True

    @staticmethod
    async def add_purchase_order_item(
        db: AsyncSession,
        order_id: uuid.UUID,
        item_data: Dict[str, Any],
        user_id: uuid.UUID
    ) -> Tuple[PurchaseOrder, PurchaseOrderItem]:
        """添加采购订单项"""
        # 获取订单
        query = select(PurchaseOrder).where(PurchaseOrder.id == order_id)
        result = await db.execute(query)
        db_order = result.scalar_one_or_none()

        if not db_order:
            return None, None

        # 计算项目金额
        quantity = item_data.get("quantity", 0)
        unit_price = item_data.get("unit_price", 0)
        discount_rate = item_data.get("discount_rate", 0)
        tax_rate = item_data.get("tax_rate", 0)

        item_total = quantity * unit_price
        item_discount = item_total * (discount_rate / 100)
        item_tax = (item_total - item_discount) * (tax_rate / 100)
        item_final = item_total - item_discount + item_tax

        # 创建订单项
        db_item = PurchaseOrderItem(
            purchase_order_id=order_id,
            product_id=item_data.get("product_id"),
            product_name=item_data.get("product_name"),
            product_code=item_data.get("product_code"),
            product_unit=item_data.get("product_unit"),
            product_specification=item_data.get("product_specification"),
            quantity=quantity,
            unit_price=unit_price,
            discount_rate=discount_rate,
            tax_rate=tax_rate,
            total_amount=item_final,
            notes=item_data.get("notes")
        )

        db.add(db_item)
        await db.flush()

        # 更新订单金额
        db_order.total_amount += item_total
        db_order.discount_amount += item_discount
        db_order.tax_amount += item_tax
        db_order.final_amount = db_order.total_amount - db_order.discount_amount + db_order.tax_amount + db_order.shipping_fee

        db_order.updated_by = user_id
        db_order.updated_at = datetime.utcnow()

        await db.commit()
        await db.refresh(db_order)
        await db.refresh(db_item)

        return db_order, db_item

    @staticmethod
    async def update_purchase_order_item(
        db: AsyncSession,
        item_id: uuid.UUID,
        item_data: Dict[str, Any],
        user_id: uuid.UUID
    ) -> Tuple[PurchaseOrder, PurchaseOrderItem]:
        """更新采购订单项"""
        # 获取订单项
        query = select(PurchaseOrderItem).where(PurchaseOrderItem.id == item_id)
        result = await db.execute(query)
        db_item = result.scalar_one_or_none()

        if not db_item:
            return None, None

        # 获取订单
        query = select(PurchaseOrder).where(PurchaseOrder.id == db_item.purchase_order_id)
        result = await db.execute(query)
        db_order = result.scalar_one_or_none()

        if not db_order:
            return None, None

        # 计算原项目金额
        old_total = db_item.quantity * db_item.unit_price
        old_discount = old_total * (db_item.discount_rate / 100)
        old_tax = (old_total - old_discount) * (db_item.tax_rate / 100)
        old_final = old_total - old_discount + old_tax

        # 更新订单项
        for key, value in item_data.items():
            setattr(db_item, key, value)

        # 计算新项目金额
        new_total = db_item.quantity * db_item.unit_price
        new_discount = new_total * (db_item.discount_rate / 100)
        new_tax = (new_total - new_discount) * (db_item.tax_rate / 100)
        new_final = new_total - new_discount + new_tax

        db_item.total_amount = new_final

        # 更新订单金额
        db_order.total_amount = db_order.total_amount - old_total + new_total
        db_order.discount_amount = db_order.discount_amount - old_discount + new_discount
        db_order.tax_amount = db_order.tax_amount - old_tax + new_tax
        db_order.final_amount = db_order.total_amount - db_order.discount_amount + db_order.tax_amount + db_order.shipping_fee

        db_order.updated_by = user_id
        db_order.updated_at = datetime.utcnow()

        await db.commit()
        await db.refresh(db_order)
        await db.refresh(db_item)

        return db_order, db_item

    @staticmethod
    async def delete_purchase_order_item(
        db: AsyncSession,
        item_id: uuid.UUID,
        user_id: uuid.UUID
    ) -> Optional[PurchaseOrder]:
        """删除采购订单项"""
        # 获取订单项
        query = select(PurchaseOrderItem).where(PurchaseOrderItem.id == item_id)
        result = await db.execute(query)
        db_item = result.scalar_one_or_none()

        if not db_item:
            return None

        # 获取订单
        query = select(PurchaseOrder).where(PurchaseOrder.id == db_item.purchase_order_id)
        result = await db.execute(query)
        db_order = result.scalar_one_or_none()

        if not db_order:
            return None

        # 计算项目金额
        item_total = db_item.quantity * db_item.unit_price
        item_discount = item_total * (db_item.discount_rate / 100)
        item_tax = (item_total - item_discount) * (db_item.tax_rate / 100)

        # 更新订单金额
        db_order.total_amount -= item_total
        db_order.discount_amount -= item_discount
        db_order.tax_amount -= item_tax
        db_order.final_amount = db_order.total_amount - db_order.discount_amount + db_order.tax_amount + db_order.shipping_fee

        db_order.updated_by = user_id
        db_order.updated_at = datetime.utcnow()

        # 删除订单项
        await db.delete(db_item)

        await db.commit()
        await db.refresh(db_order)

        return db_order

    @staticmethod
    async def get_purchase_order_with_details(
        db: AsyncSession,
        order_id: uuid.UUID
    ) -> Optional[Dict[str, Any]]:
        """获取采购订单详情，包括关联信息"""
        db_order = await PurchaseOrderService.get_purchase_order_by_id(db, order_id)

        if not db_order:
            return None

        # 构建响应数据
        order_data = {
            "id": db_order.id,
            "order_number": db_order.order_number,
            "project_id": db_order.project_id,
            "warehouse_id": db_order.warehouse_id,
            "order_date": db_order.order_date,
            "expected_delivery_date": db_order.expected_delivery_date,
            "actual_delivery_date": db_order.actual_delivery_date,
            "status": db_order.status,
            "total_amount": db_order.total_amount,
            "discount_amount": db_order.discount_amount,
            "tax_amount": db_order.tax_amount,
            "shipping_fee": db_order.shipping_fee,
            "final_amount": db_order.final_amount,
            "payment_status": db_order.payment_status,
            "payment_method": db_order.payment_method,
            "payment_date": db_order.payment_date,
            "notes": db_order.notes,
            "created_by": db_order.created_by,
            "updated_by": db_order.updated_by,
            "created_at": db_order.created_at,
            "updated_at": db_order.updated_at,

            # 关联信息
            "warehouse_name": db_order.warehouse.name if db_order.warehouse else None,
            "creator_name": db_order.creator.full_name if db_order.creator else None,
            "updater_name": db_order.updater.full_name if db_order.updater else None,

            # 订单项
            "items": []
        }

        # 添加订单项
        for item in db_order.items:
            item_data = {
                "id": item.id,
                "purchase_order_id": item.purchase_order_id,
                "product_id": item.product_id,
                "supplier_id": item.supplier_id,
                "category_id": item.category_id,
                "product_name": item.product_name,
                "product_code": item.product_code,
                "product_unit": item.product_unit,
                "product_specification": item.product_specification,
                "quantity": item.quantity,
                "unit_price": item.unit_price,
                "discount_rate": item.discount_rate,
                "tax_rate": item.tax_rate,
                "total_amount": item.total_amount,
                "received_quantity": item.received_quantity,
                "notes": item.notes,
                "created_at": item.created_at,
                "updated_at": item.updated_at,
                
                # 关联信息
                "supplier_name": item.supplier.name if item.supplier else None,
                "category_name": item.category.name if item.category else None
            }

            order_data["items"].append(item_data)

        return order_data

    @staticmethod
    async def process_upload(
        db: AsyncSession,
        project_id: uuid.UUID,
        tenant_id: Optional[uuid.UUID],
        user_id: uuid.UUID,
        upload_type: str,
        warehouse_id: Optional[uuid.UUID],
        distribution_mode: Optional[str],
        distribution_items: List[Dict[str, Any]],
        data_frame: Any
    ) -> Dict[str, Any]:
        """处理上传数据并创建相关订单"""
        result = {
            "purchase_order_id": None,
            "arrival_confirmations": [],
            "warehouse_entries": [],
            "message": "上传处理成功"
        }

        # 收集所有商品信息（不按供应商分组）
        all_items = []

        # 如果是分拨单，从distribution_items中获取商品和供应商信息
        if upload_type == "both" and distribution_items:
            for item in distribution_items:
                product_name = item.get("product_name")
                supplier_id = item.get("supplier_id")

                if not product_name:
                    continue

                # 从数据框中查找匹配的商品
                for _, row in data_frame.iterrows():
                    if pd.isna(row['商品名称']) or pd.isna(row['数量']):
                        continue

                    if str(row['商品名称']) == product_name:
                        quantity = float(row['数量'])
                        unit_price = float(row['单价']) if not pd.isna(row['单价']) else 0.0

                        item_data = {
                            "product_name": str(row['商品名称']),
                            "product_code": str(row['商品编码']) if '商品编码' in data_frame.columns and not pd.isna(row['商品编码']) else None,
                            "product_unit": str(row['单位']) if '单位' in data_frame.columns and not pd.isna(row['单位']) else None,
                            "product_specification": str(row['规格']) if '规格' in data_frame.columns and not pd.isna(row['规格']) else None,
                            "quantity": quantity,
                            "unit_price": unit_price,
                            "notes": str(row['备注']) if '备注' in data_frame.columns and not pd.isna(row['备注']) else None,
                            "supplier_id": supplier_id  # 商品级别的供应商
                        }

                        all_items.append(item_data)
                        break
        else:
            # 如果是纯采购单，从数据框中获取商品信息
            for _, row in data_frame.iterrows():
                # 跳过空行
                if pd.isna(row['商品名称']) or pd.isna(row['数量']):
                    continue

                quantity = float(row['数量'])
                unit_price = float(row['单价']) if not pd.isna(row['单价']) else 0.0

                item_data = {
                    "product_name": str(row['商品名称']),
                    "product_code": str(row['商品编码']) if '商品编码' in data_frame.columns and not pd.isna(row['商品编码']) else None,
                    "product_unit": str(row['单位']) if '单位' in data_frame.columns and not pd.isna(row['单位']) else None,
                    "product_specification": str(row['规格']) if '规格' in data_frame.columns and not pd.isna(row['规格']) else None,
                    "quantity": quantity,
                    "unit_price": unit_price,
                    "notes": str(row['备注']) if '备注' in data_frame.columns and not pd.isna(row['备注']) else None,
                    "supplier_id": None  # 纯采购单可能没有供应商信息
                }

                all_items.append(item_data)

        # 创建单个采购订单包含所有商品
        if all_items:
            # 生成订单编号
            order_number = await PurchaseOrderService.generate_order_number()

            # 计算总金额
            total_amount = sum(item["quantity"] * item["unit_price"] for item in all_items)

            # 创建采购订单（不指定供应商，因为供应商是商品级别的）
            db_order = PurchaseOrder(
                tenant_id=tenant_id,
                order_number=order_number,
                project_id=project_id,
                warehouse_id=warehouse_id if distribution_mode == "partial" else None,
                order_date=datetime.now(),
                expected_delivery_date=datetime.now() + timedelta(days=7),
                status="confirmed",  # 直接设为已确认状态
                total_amount=total_amount,
                discount_amount=0.0,
                tax_amount=0.0,
                shipping_fee=0.0,
                final_amount=total_amount,
                payment_status="unpaid",
                notes=f"{'采购分拨' if upload_type == 'both' else '采购'}单上传创建",
                created_by=user_id,
                updated_by=user_id
            )

            db.add(db_order)
            await db.flush()

            result["purchase_order_id"] = db_order.id

            # 创建采购订单项（供应商信息在商品级别）
            for item in all_items:
                db_item = PurchaseOrderItem(
                    tenant_id=tenant_id,
                    project_id=project_id,
                    purchase_order_id=db_order.id,
                    supplier_id=item.get("supplier_id"),  # 商品级别的供应商
                    product_name=item["product_name"],
                    product_code=item["product_code"],
                    product_unit=item["product_unit"],
                    product_specification=item["product_specification"],
                    quantity=item["quantity"],
                    unit_price=item["unit_price"],
                    discount_rate=0.0,
                    tax_rate=0.0,
                    total_amount=item["quantity"] * item["unit_price"],
                    received_quantity=0.0,
                    notes=item["notes"]
                )

                db.add(db_item)

        # 处理分拨数据（如果有的话）
        if upload_type in ["both", "distribution"] and distribution_items:
            from models.store_operations import ArrivalConfirmation, ArrivalConfirmationItem
            from models.store import Store
            from models.warehouse import Warehouse
            
            # 按目的地分组分拨商品
            destinations = {}
            
            for item in distribution_items:
                product_name = item.get("product_name")
                product_specification = item.get("product_specification")
                product_unit = item.get("product_unit")
                unit_price = float(item.get("unit_price", 0))
                
                for dest in item.get("destinations", []):
                    dest_type = dest.get("type")
                    dest_id = dest.get("target_id")
                    dest_quantity = float(dest.get("quantity", 0))
                    
                    if not dest_id or dest_quantity <= 0:
                        continue
                    
                    # 创建目的地键
                    dest_key = f"{dest_type}_{dest_id}"
                    
                    if dest_key not in destinations:
                        # 获取目的地名称
                        if dest_type == "store":
                            store = await db.get(Store, dest_id)
                            if not store:
                                continue
                            dest_name = store.name
                        else:  # warehouse
                            warehouse = await db.get(Warehouse, dest_id)
                            if not warehouse:
                                continue
                            dest_name = warehouse.name
                        
                        destinations[dest_key] = {
                            "type": dest_type,
                            "target_id": dest_id,
                            "target_name": dest_name,
                            "items": [],
                            "total_amount": 0.0
                        }
                    
                    # 添加商品到目的地
                    item_total = dest_quantity * unit_price
                    destinations[dest_key]["items"].append({
                        "product_name": product_name,
                        "product_code": item.get("product_code"),
                        "product_unit": product_unit,
                        "product_specification": product_specification,
                        "quantity": dest_quantity,
                        "unit_price": unit_price,
                        "notes": item.get("notes")
                    })
                    destinations[dest_key]["total_amount"] += item_total
            
            # 为每个目的地创建到货确认单
            for dest_key, dest_data in destinations.items():
                # 生成到货单号
                arrival_number = f"ARR{datetime.now().strftime('%Y%m%d%H%M%S')}{uuid.uuid4().hex[:6]}"
                
                # 创建到货确认单，关联到刚创建的采购单
                arrival = ArrivalConfirmation(
                    tenant_id=tenant_id,
                    project_id=project_id,
                    store_id=dest_data["target_id"] if dest_data["type"] == "store" else None,
                    warehouse_id=dest_data["target_id"] if dest_data["type"] == "warehouse" else None,
                    purchase_order_id=result["purchase_order_id"],  # 关联到刚创建的采购单
                    arrival_number=arrival_number,
                    arrival_type="warehouse",  # 仓库配货类型
                    supplier_id=None,  # 分拨单不直接关联供应商
                    arrival_date=datetime.now(),
                    status="pending",  # 待确认状态
                    total_amount=dest_data["total_amount"],
                    total_items=len(dest_data["items"]),
                    notes=f"{'采购分拨' if upload_type == 'both' else '分拨'}单自动创建" +
                          (f" - 来源采购单: {result['purchase_order_id']}" if result["purchase_order_id"] else ""),
                    created_by=user_id,
                    updated_by=user_id
                )
                
                db.add(arrival)
                await db.flush()
                
                # 创建到货确认单项
                for item in dest_data["items"]:
                    arrival_item = ArrivalConfirmationItem(
                        arrival_id=arrival.id,
                        product_name=item["product_name"],
                        product_code=item["product_code"],
                        unit=item["product_unit"],
                        specification=item["product_specification"],
                        expected_quantity=int(item["quantity"]),
                        actual_quantity=0,  # 初始为0，等待确认
                        price=item["unit_price"],
                        amount=item["quantity"] * item["unit_price"],
                        is_confirmed=False,
                        notes=item["notes"]
                    )
                    
                    db.add(arrival_item)
                
                result["arrival_confirmations"].append({
                    "id": str(arrival.id),
                    "arrival_number": arrival.arrival_number,
                    "type": dest_data["type"],
                    "target_id": str(dest_data["target_id"]),
                    "target_name": dest_data["target_name"],
                    "purchase_order_id": str(result["purchase_order_id"]) if result["purchase_order_id"] else None
                })
        
        await db.commit()
        return result

    @staticmethod
    async def create_distribution_order(
        db: AsyncSession,
        project_id: uuid.UUID,
        tenant_id: Optional[uuid.UUID],
        user_id: uuid.UUID,
        source_warehouse_id: uuid.UUID,
        destination_type: str,
        destination_id: uuid.UUID,
        items: List[Any]
    ) -> Dict[str, Any]:
        """创建分拨订单和到货确认单"""
        from models.arrival_confirmation import ArrivalConfirmation, ArrivalConfirmationItem

        # 生成订单编号
        order_number = await PurchaseOrderService.generate_order_number()

        # 生成到货单号
        arrival_number = f"ARR{datetime.now().strftime('%Y%m%d%H%M%S')}{uuid.uuid4().hex[:6]}"

        # 计算订单金额
        total_amount = 0.0
        for item in items:
            total_amount += item.quantity * item.unit_price

        # 创建采购订单
        db_order = PurchaseOrder(
            tenant_id=tenant_id,
            order_number=order_number,
            project_id=project_id,
            warehouse_id=source_warehouse_id,  # 源仓库
            order_date=datetime.now(),
            expected_delivery_date=datetime.now(),
            status="confirmed",  # 直接设为已确认状态
            total_amount=total_amount,
            discount_amount=0.0,
            tax_amount=0.0,
            shipping_fee=0.0,
            final_amount=total_amount,
            payment_status="paid",  # 内部分拨，设为已支付
            payment_method="internal",
            notes=f"分拨单 - {'门店' if destination_type == 'store' else '仓库'}分拨",
            created_by=user_id,
            updated_by=user_id
        )

        db.add(db_order)
        await db.flush()

        # 创建采购订单项
        for item in items:
            db_item = PurchaseOrderItem(
                tenant_id=tenant_id,
                project_id=project_id,
                purchase_order_id=db_order.id,
                product_id=item.product_id,
                product_name=item.product_name,
                product_code=item.product_code,
                product_unit=item.product_unit,
                product_specification=item.product_specification,
                quantity=item.quantity,
                unit_price=item.unit_price,
                discount_rate=0.0,
                tax_rate=0.0,
                total_amount=item.quantity * item.unit_price,
                received_quantity=0.0,
                notes=item.notes
            )

            db.add(db_item)

        # 创建到货确认单
        arrival = ArrivalConfirmation(
            tenant_id=tenant_id,
            project_id=project_id,
            store_id=destination_id if destination_type == "store" else None,
            warehouse_id=destination_id if destination_type == "warehouse" else None,
            arrival_number=arrival_number,
            arrival_type="market_purchase",  # 市场采购类型
            supplier_id=None,  # 内部分拨没有供应商
            arrival_date=datetime.now(),
            status="pending",  # 待确认状态
            total_amount=total_amount,
            total_items=len(items),
            notes=f"分拨单自动创建 - 来源订单: {order_number}",
            created_by=user_id,
            updated_by=user_id
        )

        db.add(arrival)
        await db.flush()

        # 创建到货确认单项
        for item in items:
            arrival_item = ArrivalConfirmationItem(
                tenant_id=tenant_id,
                project_id=project_id,
                arrival_id=arrival.id,
                product_id=item.product_id,
                product_name=item.product_name,
                product_code=item.product_code,
                product_unit=item.product_unit,
                product_specification=item.product_specification,
                expected_quantity=item.quantity,
                actual_quantity=0.0,  # 初始为0，等待确认
                unit_price=item.unit_price,
                amount=item.quantity * item.unit_price,
                is_confirmed=False,
                notes=item.notes
            )

            db.add(arrival_item)

        await db.commit()

        return {
            "purchase_order_id": db_order.id,
            "arrival_id": arrival.id
        }
