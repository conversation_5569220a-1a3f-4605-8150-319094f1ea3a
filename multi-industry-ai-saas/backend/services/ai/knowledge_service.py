#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
AI 知识库服务
"""

import logging
import uuid
import json
import time
from typing import List, Optional, Dict, Any, Tuple, Union
from sqlalchemy import select, func, and_, or_, desc, text
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import joinedload

from models.ai import AIKnowledgeBase, AIKnowledgeDocument, AIKnowledgeChunk, AIModel
from models.project import Project
from models.tenant import Tenant
from schemas.ai import (
    AIKnowledgeBaseCreate,
    AIKnowledgeBaseUpdate,
    AIKnowledgeDocumentCreate,
    AIKnowledgeDocumentUpdate,
    AIKnowledgeChunkCreate,
    AIKnowledgeQueryRequest,
    AIKnowledgeQueryResult,
)
from services.ai import AIEmbeddingService

logger = logging.getLogger(__name__)

class AIKnowledgeService:
    """AI 知识库服务"""

    @staticmethod
    async def get_knowledge_bases(
        db: AsyncSession,
        skip: int = 0,
        limit: int = 100,
        tenant_id: Optional[uuid.UUID] = None,
        project_id: Optional[uuid.UUID] = None,
        industry_type: Optional[str] = None,
        status: Optional[str] = None,
    ) -> Tuple[List[AIKnowledgeBase], int]:
        """
        获取 AI 知识库列表
        """
        query = select(AIKnowledgeBase).options(
            joinedload(AIKnowledgeBase.embedding_model)
        )

        # 构建查询条件
        conditions = []

        if tenant_id:
            conditions.append(AIKnowledgeBase.tenant_id == tenant_id)

        if project_id:
            conditions.append(AIKnowledgeBase.project_id == project_id)

        if industry_type:
            conditions.append(AIKnowledgeBase.industry_type == industry_type)

        if status:
            conditions.append(AIKnowledgeBase.status == status)

        if conditions:
            query = query.where(and_(*conditions))

        # 获取总数
        count_query = select(func.count()).select_from(query.subquery())
        total = await db.scalar(count_query)

        # 获取分页数据
        query = query.order_by(desc(AIKnowledgeBase.created_at)).offset(skip).limit(limit)
        result = await db.execute(query)
        knowledge_bases = result.scalars().all()

        return knowledge_bases, total

    @staticmethod
    async def get_knowledge_base(
        db: AsyncSession,
        knowledge_base_id: uuid.UUID,
    ) -> Optional[AIKnowledgeBase]:
        """
        获取 AI 知识库详情
        """
        query = select(AIKnowledgeBase).options(
            joinedload(AIKnowledgeBase.embedding_model)
        ).where(AIKnowledgeBase.id == knowledge_base_id)

        result = await db.execute(query)
        return result.scalars().first()

    @staticmethod
    async def create_knowledge_base(
        db: AsyncSession,
        knowledge_base_data: AIKnowledgeBaseCreate,
    ) -> AIKnowledgeBase:
        """
        创建 AI 知识库
        """
        # 检查租户是否存在
        tenant_query = select(Tenant).where(Tenant.id == knowledge_base_data.tenant_id)
        tenant_result = await db.execute(tenant_query)
        tenant = tenant_result.scalars().first()

        if not tenant:
            raise ValueError(f"租户 ID '{knowledge_base_data.tenant_id}' 不存在")

        # 检查项目是否存在
        if knowledge_base_data.project_id:
            project_query = select(Project).where(Project.id == knowledge_base_data.project_id)
            project_result = await db.execute(project_query)
            project = project_result.scalars().first()

            if not project:
                raise ValueError(f"项目 ID '{knowledge_base_data.project_id}' 不存在")

            # 检查项目是否属于该租户
            if project.tenant_id != knowledge_base_data.tenant_id:
                raise ValueError(f"项目 '{project.name}' 不属于租户 ID '{knowledge_base_data.tenant_id}'")

        # 检查嵌入模型是否存在
        if knowledge_base_data.embedding_model_id:
            model_query = select(AIModel).where(
                AIModel.id == knowledge_base_data.embedding_model_id,
                AIModel.model_type == "embedding"
            )
            model_result = await db.execute(model_query)
            model = model_result.scalars().first()

            if not model:
                raise ValueError(f"嵌入模型 ID '{knowledge_base_data.embedding_model_id}' 不存在或不是嵌入模型")

        # 创建知识库
        knowledge_base = AIKnowledgeBase(**knowledge_base_data.dict())
        db.add(knowledge_base)
        await db.commit()
        await db.refresh(knowledge_base)

        logger.info(f"创建 AI 知识库成功: {knowledge_base.id}")
        return knowledge_base

    @staticmethod
    async def update_knowledge_base(
        db: AsyncSession,
        knowledge_base_id: uuid.UUID,
        knowledge_base_data: AIKnowledgeBaseUpdate,
    ) -> Optional[AIKnowledgeBase]:
        """
        更新 AI 知识库
        """
        # 获取知识库
        knowledge_base = await AIKnowledgeService.get_knowledge_base(db, knowledge_base_id)
        if not knowledge_base:
            return None

        # 检查嵌入模型是否存在
        if knowledge_base_data.embedding_model_id:
            model_query = select(AIModel).where(
                AIModel.id == knowledge_base_data.embedding_model_id,
                AIModel.model_type == "embedding"
            )
            model_result = await db.execute(model_query)
            model = model_result.scalars().first()

            if not model:
                raise ValueError(f"嵌入模型 ID '{knowledge_base_data.embedding_model_id}' 不存在或不是嵌入模型")

        # 更新知识库
        update_data = knowledge_base_data.dict(exclude_unset=True)
        for key, value in update_data.items():
            setattr(knowledge_base, key, value)

        await db.commit()
        await db.refresh(knowledge_base)

        logger.info(f"更新 AI 知识库成功: {knowledge_base.id}")
        return knowledge_base

    @staticmethod
    async def delete_knowledge_base(
        db: AsyncSession,
        knowledge_base_id: uuid.UUID,
    ) -> bool:
        """
        删除 AI 知识库
        """
        # 获取知识库
        knowledge_base = await AIKnowledgeService.get_knowledge_base(db, knowledge_base_id)
        if not knowledge_base:
            return False

        # 删除知识库
        await db.delete(knowledge_base)
        await db.commit()

        logger.info(f"删除 AI 知识库成功: {knowledge_base.id}")
        return True

    @staticmethod
    async def get_documents(
        db: AsyncSession,
        knowledge_base_id: uuid.UUID,
        skip: int = 0,
        limit: int = 100,
        embedding_status: Optional[str] = None,
    ) -> Tuple[List[AIKnowledgeDocument], int]:
        """
        获取 AI 知识文档列表
        """
        query = select(AIKnowledgeDocument).where(
            AIKnowledgeDocument.knowledge_base_id == knowledge_base_id
        )

        if embedding_status:
            query = query.where(AIKnowledgeDocument.embedding_status == embedding_status)

        # 获取总数
        count_query = select(func.count()).select_from(query.subquery())
        total = await db.scalar(count_query)

        # 获取分页数据
        query = query.order_by(desc(AIKnowledgeDocument.created_at)).offset(skip).limit(limit)
        result = await db.execute(query)
        documents = result.scalars().all()

        return documents, total

    @staticmethod
    async def get_document(
        db: AsyncSession,
        document_id: uuid.UUID,
    ) -> Optional[AIKnowledgeDocument]:
        """
        获取 AI 知识文档详情
        """
        query = select(AIKnowledgeDocument).where(AIKnowledgeDocument.id == document_id)
        result = await db.execute(query)
        return result.scalars().first()

    @staticmethod
    async def create_document(
        db: AsyncSession,
        document_data: AIKnowledgeDocumentCreate,
    ) -> AIKnowledgeDocument:
        """
        创建 AI 知识文档
        """
        # 检查知识库是否存在
        knowledge_base = await AIKnowledgeService.get_knowledge_base(db, document_data.knowledge_base_id)
        if not knowledge_base:
            raise ValueError(f"知识库 ID '{document_data.knowledge_base_id}' 不存在")

        # 创建文档
        document = AIKnowledgeDocument(**document_data.dict())
        db.add(document)

        # 更新知识库文档数量
        knowledge_base.document_count += 1

        await db.commit()
        await db.refresh(document)

        logger.info(f"创建 AI 知识文档成功: {document.id}")
        return document

    @staticmethod
    async def update_document(
        db: AsyncSession,
        document_id: uuid.UUID,
        document_data: AIKnowledgeDocumentUpdate,
    ) -> Optional[AIKnowledgeDocument]:
        """
        更新 AI 知识文档
        """
        # 获取文档
        document = await AIKnowledgeService.get_document(db, document_id)
        if not document:
            return None

        # 更新文档
        update_data = document_data.dict(exclude_unset=True)
        for key, value in update_data.items():
            setattr(document, key, value)

        await db.commit()
        await db.refresh(document)

        logger.info(f"更新 AI 知识文档成功: {document.id}")
        return document

    @staticmethod
    async def delete_document(
        db: AsyncSession,
        document_id: uuid.UUID,
    ) -> bool:
        """
        删除 AI 知识文档
        """
        # 获取文档
        document = await AIKnowledgeService.get_document(db, document_id)
        if not document:
            return False

        # 获取知识库
        knowledge_base = await AIKnowledgeService.get_knowledge_base(db, document.knowledge_base_id)
        if knowledge_base:
            # 更新知识库文档数量和文本块数量
            knowledge_base.document_count = max(0, knowledge_base.document_count - 1)
            knowledge_base.chunk_count = max(0, knowledge_base.chunk_count - document.chunk_count)

        # 删除文档
        await db.delete(document)
        await db.commit()

        logger.info(f"删除 AI 知识文档成功: {document.id}")
        return True

    @staticmethod
    async def get_chunks(
        db: AsyncSession,
        document_id: uuid.UUID,
        skip: int = 0,
        limit: int = 100,
    ) -> Tuple[List[AIKnowledgeChunk], int]:
        """
        获取 AI 知识文本块列表
        """
        query = select(AIKnowledgeChunk).where(
            AIKnowledgeChunk.document_id == document_id
        )

        # 获取总数
        count_query = select(func.count()).select_from(query.subquery())
        total = await db.scalar(count_query)

        # 获取分页数据
        query = query.order_by(AIKnowledgeChunk.chunk_index).offset(skip).limit(limit)
        result = await db.execute(query)
        chunks = result.scalars().all()

        return chunks, total

    @staticmethod
    async def create_chunk(
        db: AsyncSession,
        chunk_data: AIKnowledgeChunkCreate,
    ) -> AIKnowledgeChunk:
        """
        创建 AI 知识文本块
        """
        # 检查文档是否存在
        document = await AIKnowledgeService.get_document(db, chunk_data.document_id)
        if not document:
            raise ValueError(f"文档 ID '{chunk_data.document_id}' 不存在")

        # 检查知识库是否存在
        knowledge_base = await AIKnowledgeService.get_knowledge_base(db, chunk_data.knowledge_base_id)
        if not knowledge_base:
            raise ValueError(f"知识库 ID '{chunk_data.knowledge_base_id}' 不存在")

        # 创建文本块
        chunk = AIKnowledgeChunk(**chunk_data.dict())
        db.add(chunk)

        # 更新文档文本块数量
        document.chunk_count += 1

        # 更新知识库文本块数量
        knowledge_base.chunk_count += 1

        await db.commit()
        await db.refresh(chunk)

        logger.info(f"创建 AI 知识文本块成功: {chunk.id}")
        return chunk

    @staticmethod
    async def delete_chunk(
        db: AsyncSession,
        chunk_id: uuid.UUID,
    ) -> bool:
        """
        删除 AI 知识文本块
        """
        # 获取文本块
        query = select(AIKnowledgeChunk).where(AIKnowledgeChunk.id == chunk_id)
        result = await db.execute(query)
        chunk = result.scalars().first()

        if not chunk:
            return False

        # 获取文档
        document = await AIKnowledgeService.get_document(db, chunk.document_id)
        if document:
            # 更新文档文本块数量
            document.chunk_count = max(0, document.chunk_count - 1)

        # 获取知识库
        knowledge_base = await AIKnowledgeService.get_knowledge_base(db, chunk.knowledge_base_id)
        if knowledge_base:
            # 更新知识库文本块数量
            knowledge_base.chunk_count = max(0, knowledge_base.chunk_count - 1)

        # 删除文本块
        await db.delete(chunk)
        await db.commit()

        logger.info(f"删除 AI 知识文本块成功: {chunk.id}")
        return True

    @staticmethod
    async def process_document(
        db: AsyncSession,
        document_id: uuid.UUID,
        user_id: uuid.UUID,
        chunk_size: int = 1000,
        chunk_overlap: int = 200,
    ) -> bool:
        """
        处理文档，生成文本块和嵌入向量
        """
        # 获取文档
        document = await AIKnowledgeService.get_document(db, document_id)
        if not document:
            raise ValueError(f"文档 ID '{document_id}' 不存在")

        # 获取知识库
        knowledge_base = await AIKnowledgeService.get_knowledge_base(db, document.knowledge_base_id)
        if not knowledge_base:
            raise ValueError(f"知识库 ID '{document.knowledge_base_id}' 不存在")

        # 检查嵌入模型
        if not knowledge_base.embedding_model_id:
            raise ValueError(f"知识库 '{knowledge_base.name}' 未配置嵌入模型")

        try:
            # 更新文档状态
            document.embedding_status = "processing"
            await db.commit()

            # 获取文档内容
            content = document.content

            # 如果文档内容为空，但有文件ID，则尝试从存储服务获取文件内容
            if not content and document.file_id:
                from services.storage_service import StorageService

                # 获取文件信息
                file = await StorageService.get_file(
                    db=db,
                    file_id=uuid.UUID(document.file_id),
                    project_id=knowledge_base.project_id
                )

                if file:
                    # 获取文件内容
                    file_info = await StorageService.download_file(
                        db=db,
                        file_id=uuid.UUID(document.file_id),
                        project_id=knowledge_base.project_id
                    )

                    if file_info:
                        # 根据文件类型处理内容
                        import os
                        from pathlib import Path

                        file_path = Path(file_info["file_path"])
                        mime_type = file.mime_type

                        # 文本文件直接读取
                        if mime_type.startswith("text/"):
                            with open(file_path, "r", encoding="utf-8") as f:
                                content = f.read()
                        # PDF文件使用PyPDF2解析
                        elif mime_type == "application/pdf":
                            try:
                                import PyPDF2
                                with open(file_path, "rb") as f:
                                    pdf_reader = PyPDF2.PdfReader(f)
                                    content = ""
                                    for page in pdf_reader.pages:
                                        content += page.extract_text() + "\n"
                            except ImportError:
                                logger.warning("PyPDF2 not installed, cannot process PDF")
                                content = ""
                        # Word文档使用python-docx解析
                        elif mime_type in ["application/msword", "application/vnd.openxmlformats-officedocument.wordprocessingml.document"]:
                            try:
                                import docx
                                doc = docx.Document(file_path)
                                content = "\n".join([para.text for para in doc.paragraphs])
                            except ImportError:
                                logger.warning("python-docx not installed, cannot process Word documents")
                                content = ""

                        # 更新文档内容
                        if content:
                            document.content = content
                            await db.commit()

            if not content:
                raise ValueError(f"文档 '{document.title}' 没有内容，且无法从文件中提取内容")

            # 分割文本
            chunks = AIKnowledgeService._split_text(content, chunk_size, chunk_overlap)

            # 删除现有的文本块
            delete_query = text(f"""
                DELETE FROM ai_knowledge_chunks
                WHERE document_id = '{document_id}'
            """)
            await db.execute(delete_query)

            # 重置文档和知识库的文本块数量
            old_chunk_count = document.chunk_count
            document.chunk_count = 0
            knowledge_base.chunk_count -= old_chunk_count

            # 创建新的文本块
            for i, chunk_text in enumerate(chunks):
                # 创建文本块
                chunk_data = AIKnowledgeChunkCreate(
                    knowledge_base_id=knowledge_base.id,
                    document_id=document.id,
                    content=chunk_text,
                    chunk_index=i,
                    token_count=len(chunk_text.split()),  # 简单估算
                    chunk_metadata={
                        "document_title": document.title,
                        "source": document.source_url or document.file_path,
                    }
                )

                # 生成嵌入向量
                embedding_result = await AIEmbeddingService.create_embedding(
                    db=db,
                    project_id=knowledge_base.project_id or knowledge_base.tenant_id,  # 使用项目 ID 或租户 ID
                    user_id=user_id,
                    input=chunk_text,
                    model_id=knowledge_base.embedding_model_id,
                )

                # 提取嵌入向量
                embedding_data = embedding_result.get("data", [])
                if embedding_data:
                    embedding_vector = embedding_data[0].get("embedding", [])
                    chunk_data.embedding = embedding_vector

                # 创建文本块
                await AIKnowledgeService.create_chunk(db, chunk_data)

            # 更新文档状态
            document.embedding_status = "completed"
            await db.commit()

            logger.info(f"处理文档成功: {document.id}, 生成 {len(chunks)} 个文本块")
            return True

        except Exception as e:
            # 更新文档状态
            document.embedding_status = "failed"
            document.error_message = str(e)
            await db.commit()

            logger.error(f"处理文档失败: {document.id}, 错误: {e}")
            raise ValueError(f"处理文档失败: {str(e)}")

    @staticmethod
    def _split_text(text: str, chunk_size: int = 1000, chunk_overlap: int = 200) -> List[str]:
        """
        分割文本为多个文本块
        """
        # 简单按段落分割
        paragraphs = text.split("\n\n")
        chunks = []
        current_chunk = ""

        for paragraph in paragraphs:
            # 如果段落太长，进一步分割
            if len(paragraph) > chunk_size:
                sentences = paragraph.split(". ")
                for sentence in sentences:
                    if len(current_chunk) + len(sentence) + 2 <= chunk_size:
                        current_chunk += sentence + ". "
                    else:
                        if current_chunk:
                            chunks.append(current_chunk.strip())
                        current_chunk = sentence + ". "
            else:
                if len(current_chunk) + len(paragraph) + 2 <= chunk_size:
                    current_chunk += paragraph + "\n\n"
                else:
                    if current_chunk:
                        chunks.append(current_chunk.strip())
                    current_chunk = paragraph + "\n\n"

        if current_chunk:
            chunks.append(current_chunk.strip())

        # 添加重叠
        if chunk_overlap > 0 and len(chunks) > 1:
            overlapped_chunks = []
            for i in range(len(chunks)):
                if i == 0:
                    overlapped_chunks.append(chunks[i])
                else:
                    prev_chunk = chunks[i-1]
                    current_chunk = chunks[i]

                    # 获取上一个块的末尾
                    overlap_text = prev_chunk[-chunk_overlap:] if len(prev_chunk) > chunk_overlap else prev_chunk

                    # 添加重叠
                    overlapped_chunks.append(overlap_text + current_chunk)

            return overlapped_chunks

        return chunks

    @staticmethod
    async def query_knowledge_base(
        db: AsyncSession,
        query_data: AIKnowledgeQueryRequest,
        user_id: uuid.UUID,
    ) -> List[AIKnowledgeQueryResult]:
        """
        查询知识库
        """
        # 获取知识库
        knowledge_base = await AIKnowledgeService.get_knowledge_base(db, query_data.knowledge_base_id)
        if not knowledge_base:
            raise ValueError(f"知识库 ID '{query_data.knowledge_base_id}' 不存在")

        # 检查嵌入模型
        if not knowledge_base.embedding_model_id:
            raise ValueError(f"知识库 '{knowledge_base.name}' 未配置嵌入模型")

        # 生成查询嵌入向量
        embedding_result = await AIEmbeddingService.create_embedding(
            db=db,
            project_id=knowledge_base.project_id or knowledge_base.tenant_id,  # 使用项目 ID 或租户 ID
            user_id=user_id,
            input=query_data.query,
            model_id=knowledge_base.embedding_model_id,
        )

        # 提取嵌入向量
        embedding_data = embedding_result.get("data", [])
        if not embedding_data:
            raise ValueError("生成查询嵌入向量失败")

        query_embedding = embedding_data[0].get("embedding", [])
        if not query_embedding:
            raise ValueError("生成查询嵌入向量失败")

        # 构建向量搜索查询
        # 注意：这里使用 PostgreSQL 的向量搜索功能，需要安装 pgvector 扩展
        # 如果没有安装 pgvector，可以使用其他方式实现向量搜索
        vector_query = text(f"""
            SELECT
                c.id,
                c.document_id,
                d.title as document_title,
                c.content,
                c.chunk_metadata,
                c.page_number,
                1 - (c.embedding <=> :query_embedding) as similarity
            FROM
                ai_knowledge_chunks c
            JOIN
                ai_knowledge_documents d ON c.document_id = d.id
            WHERE
                c.knowledge_base_id = :knowledge_base_id
                AND c.embedding IS NOT NULL
            ORDER BY
                c.embedding <=> :query_embedding
            LIMIT :top_k
        """)

        # 执行查询
        result = await db.execute(
            vector_query,
            {
                "query_embedding": query_embedding,
                "knowledge_base_id": str(query_data.knowledge_base_id),
                "top_k": query_data.top_k,
            }
        )

        # 处理结果
        rows = result.mappings().all()
        query_results = []

        for row in rows:
            similarity = row["similarity"]

            # 过滤低相似度的结果
            if similarity < query_data.similarity_threshold:
                continue

            query_result = AIKnowledgeQueryResult(
                chunk_id=row["id"],
                document_id=row["document_id"],
                document_title=row["document_title"],
                content=row["content"],
                similarity=similarity,
                chunk_metadata=row["chunk_metadata"],
                page_number=row["page_number"],
            )

            query_results.append(query_result)

        return query_results
