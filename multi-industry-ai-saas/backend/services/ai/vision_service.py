#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
AI 视觉服务
"""

import logging
import uuid
import json
import json_repair
import time
import base64
import httpx
import asyncio
from typing import List, Optional, Dict, Any, Union
from sqlalchemy.ext.asyncio import AsyncSession

from models.ai import AIConfig, AIModel, AIProvider
from models.project import Project
from models.user import User
from services.ai import AIConfigService, AIUsageService
from schemas.ai import AIUsageCreate

logger = logging.getLogger(__name__)

class AIVisionService:
    """AI 视觉服务"""

    @staticmethod
    async def analyze_image(
        db: AsyncSession,
        project_id: uuid.UUID,
        user_id: uuid.UUID,
        image_data: Union[str, bytes],
        prompt: str,
        model_id: Optional[uuid.UUID] = None,
        config_id: Optional[uuid.UUID] = None,
        temperature: Optional[float] = None,
        max_tokens: Optional[int] = None,
        additional_messages: Optional[List[Dict[str, Any]]] = None,
    ) -> Dict[str, Any]:
        """
        分析图像 - 2025年增强版本
        支持最新的视觉模型：Qwen2.5-VL, Gemini 2.5 Pro等

        image_data 可以是 base64 编码的字符串或二进制数据
        """
        start_time = time.time()
        
        try:
            # 获取有效的 AI 配置
            if config_id:
                # 如果指定了配置ID，直接使用该配置
                config = await AIConfigService.get_config(db, config_id)
                if not config or config.status != "active":
                    raise ValueError(f"AI 配置 ID '{config_id}' 不存在或未激活")
            elif model_id:
                # 如果指定了模型ID，查找该模型的配置
                config = await AIConfigService.get_effective_config(
                    db=db,
                    project_id=project_id,
                    model_id=model_id,
                )
            else:
                # 如果没有指定配置或模型，使用系统整合设置的默认视觉模型
                from services.ai.system_integration_service import SystemAIIntegrationService
                
                # 获取系统整合配置
                integration_config = await SystemAIIntegrationService.get_integration_config(
                    db=db,
                    project_id=project_id
                )
                
                if not integration_config:
                    raise ValueError("未找到系统AI整合配置")
                
                # 获取默认视觉模型ID
                default_vision_model_id = None
                if isinstance(integration_config, dict):
                    default_vision_model_id = integration_config.get("default_vision_model_id")
                else:
                    default_vision_model_id = integration_config.default_vision_model_id
                
                if not default_vision_model_id:
                    raise ValueError("系统整合配置中未设置默认视觉模型")
                
                # 使用默认视觉模型查找配置
                config = await AIConfigService.get_effective_config(
                    db=db,
                    project_id=project_id,
                    model_id=default_vision_model_id,
                )
                
            if not config:
                raise ValueError("未找到有效的 AI 配置")
                
            # 获取模型信息
            model = await db.get(AIModel, config.model_id)
            if not model:
                raise ValueError(f"AI 模型 ID '{config.model_id}' 不存在")
                
            # 检查模型是否支持视觉功能
            model_capabilities = model.capabilities or {}
            
            # 应用配置中的能力覆盖（如果有的话）
            if config.config and config.config.get("capabilities_override"):
                capabilities_override = config.config["capabilities_override"]
                # 合并基础能力和覆盖能力
                effective_capabilities = {**model_capabilities, **capabilities_override}
            else:
                effective_capabilities = model_capabilities
                
            if not effective_capabilities.get("supports_vision"):
                raise ValueError(f"模型 '{model.name}' 的有效配置不支持视觉功能")
                
            # 获取提供商信息
            provider = await db.get(AIProvider, config.provider_id)
            if not provider:
                raise ValueError(f"AI 提供商 ID '{config.provider_id}' 不存在")
                
            # 获取项目信息
            project = await db.get(Project, project_id)
            if not project:
                raise ValueError(f"项目 ID '{project_id}' 不存在")
                
            # 处理图像数据
            if isinstance(image_data, bytes):
                # 将二进制数据转换为 base64 编码的字符串
                image_base64 = base64.b64encode(image_data).decode('utf-8')
            else:
                # 假设已经是 base64 编码的字符串
                image_base64 = image_data
                
            # 构建请求参数
            if provider.name in ["openai", "azure", "openrouter"]:
                # OpenAI 格式 (OpenRouter也使用此格式)
                messages = []
                
                # 添加系统消息
                messages.append({
                    "role": "system",
                    "content": "你是一个视觉分析助手，可以分析和描述图像内容。"
                })
                
                # 添加用户消息（包含图像）
                messages.append({
                    "role": "user",
                    "content": [
                        {"type": "text", "text": prompt},
                        {
                            "type": "image_url",
                            "image_url": {
                                "url": f"data:image/jpeg;base64,{image_base64}"
                            }
                        }
                    ]
                })
                
                # 添加额外的消息
                if additional_messages:
                    messages.extend(additional_messages)
                    
                request_params = {
                    "model": model.name,
                    "messages": messages,
                }
                
                # 添加可选参数
                if temperature is not None:
                    request_params["temperature"] = temperature
                elif config.temperature is not None:
                    request_params["temperature"] = config.temperature
                else:
                    # 为免费模型设置合理的默认温度
                    request_params["temperature"] = 0.7
                    
                if max_tokens is not None:
                    request_params["max_tokens"] = max_tokens
                elif config.max_tokens is not None:
                    request_params["max_tokens"] = config.max_tokens
                else:
                    # 为免费模型设置合理的最大token数
                    request_params["max_tokens"] = 4096
                
                # OpenRouter免费模型的特殊处理
                if provider.name == "openrouter" and ":free" in model.name:
                    # 确保免费模型的参数在合理范围内
                    if request_params.get("temperature", 0) > 1.0:
                        request_params["temperature"] = 1.0
                    if request_params.get("max_tokens", 0) > 8192:
                        request_params["max_tokens"] = 8192
                    
            elif provider.name == "anthropic":
                # Anthropic 格式
                messages = []
                
                # 添加用户消息（包含图像）
                messages.append({
                    "role": "user",
                    "content": [
                        {"type": "text", "text": prompt},
                        {
                            "type": "image",
                            "source": {
                                "type": "base64",
                                "media_type": "image/jpeg",
                                "data": image_base64
                            }
                        }
                    ]
                })
                
                # 添加额外的消息
                if additional_messages:
                    for msg in additional_messages:
                        if msg["role"] != "system":  # Anthropic 的 system 消息单独处理
                            messages.append(msg)
                            
                request_params = {
                    "model": model.name,
                    "messages": messages,
                }
                
                # 添加系统消息
                system_message = "你是一个视觉分析助手，可以分析和描述图像内容。"
                if additional_messages:
                    for msg in additional_messages:
                        if msg["role"] == "system":
                            system_message = msg["content"]
                            break
                            
                request_params["system"] = system_message
                
                # 添加可选参数
                if temperature is not None:
                    request_params["temperature"] = temperature
                elif config.temperature is not None:
                    request_params["temperature"] = config.temperature
                    
                if max_tokens is not None:
                    request_params["max_tokens"] = max_tokens
                elif config.max_tokens is not None:
                    request_params["max_tokens"] = config.max_tokens
                    
            elif provider.name in ["alibaba_bailian", "dashscope"]:
                # 阿里巴巴百炼/通义千问格式 - 支持Qwen2.5-VL
                messages = []

                # 通用的系统消息，适用于所有视觉模型
                system_content = "你是一个专业的视觉分析助手，擅长表格OCR和结构化数据提取。请仔细分析图像内容，准确识别表格结构和数据，按照用户要求的格式返回结果。"

                messages.append({
                    "role": "system",
                    "content": system_content
                })

                # 添加用户消息（包含图像）
                messages.append({
                    "role": "user",
                    "content": [
                        {"type": "text", "text": prompt},
                        {
                            "type": "image_url",
                            "image_url": {
                                "url": f"data:image/jpeg;base64,{image_base64}"
                            }
                        }
                    ]
                })

                # 添加额外的消息
                if additional_messages:
                    messages.extend(additional_messages)

                request_params = {
                    "model": model.name,
                    "messages": messages,
                }

                # 使用传入的参数或配置的参数
                if temperature is not None:
                    request_params["temperature"] = temperature
                elif config.temperature is not None:
                    request_params["temperature"] = config.temperature
                else:
                    request_params["temperature"] = 0.1
                    
                if max_tokens is not None:
                    # 确保max_tokens在DashScope允许的范围内[1, 8192]
                    request_params["max_tokens"] = min(max_tokens, 8192)
                elif config.max_tokens is not None:
                    # 确保max_tokens在DashScope允许的范围内[1, 8192]
                    request_params["max_tokens"] = min(config.max_tokens, 8192)
                else:
                    # 为百炼模型设置最大允许的token数，充分利用DashScope限制
                    request_params["max_tokens"] = 8192

            elif provider.name in ["google", "gemini"]:
                # Google Gemini 2.5 Pro 格式
                contents = []

                # 通用的系统指令
                system_instruction = "你是一个专业的视觉分析助手，擅长表格OCR和结构化数据提取。请仔细分析图像内容，准确识别表格结构和数据，按照用户要求的格式返回结果。"

                # Gemini格式的内容
                content_parts = [
                    {"text": prompt},
                    {
                        "inline_data": {
                            "mime_type": "image/jpeg",
                            "data": image_base64
                        }
                    }
                ]

                contents.append({
                    "role": "user",
                    "parts": content_parts
                })

                request_params = {
                    "model": model.name,
                    "contents": contents,
                    "systemInstruction": {"parts": [{"text": system_instruction}]}
                }

                # Gemini 2.5 Pro优化参数
                generation_config = {}
                if temperature is not None:
                    generation_config["temperature"] = temperature
                else:
                    generation_config["temperature"] = 0.1  # 表格识别需要低温度

                if max_tokens is not None:
                    generation_config["maxOutputTokens"] = min(max_tokens, 32768)  # Gemini 2.5 Pro支持大输出
                else:
                    generation_config["maxOutputTokens"] = 16384

                request_params["generationConfig"] = generation_config

            else:
                raise ValueError(f"不支持的 AI 提供商: {provider.name}")
                    
            # 根据提供商类型构建请求
            api_url = config.api_endpoint or provider.api_base_url
            
            if provider.name == "openai":
                # OpenAI API
                if not api_url.endswith("/chat/completions"):
                    api_url = f"{api_url.rstrip('/')}/chat/completions"
                    
                headers = {
                    "Content-Type": "application/json",
                    "Authorization": f"Bearer {config.api_key}",
                }
                
            elif provider.name == "openrouter":
                # OpenRouter API (OpenAI兼容)
                if not api_url.endswith("/chat/completions"):
                    api_url = f"{api_url.rstrip('/')}/chat/completions"
                    
                headers = {
                    "Content-Type": "application/json",
                    "Authorization": f"Bearer {config.api_key}",
                    "HTTP-Referer": "https://ai-saas-platform.com",  # OpenRouter要求
                    "X-Title": "AI SaaS Platform",  # OpenRouter应用标识
                    "OpenRouter-Beta": "false",  # 确保不使用beta功能
                }
                
            elif provider.name == "azure":
                # Azure OpenAI API
                if "{resource_name}" in api_url:
                    # 替换占位符
                    config_json = config.config or {}
                    resource_name = config_json.get("resource_name")
                    deployment_id = config_json.get("deployment_id")
                    api_version = config_json.get("api_version", "2023-05-15")
                    
                    if not resource_name or not deployment_id:
                        raise ValueError("Azure OpenAI 配置缺少 resource_name 或 deployment_id")
                        
                    api_url = api_url.replace("{resource_name}", resource_name)
                    api_url = api_url.replace("{deployment_id}", deployment_id)
                    
                    # 构建完整 URL
                    api_url = f"{api_url}/chat/completions?api-version={api_version}"
                    
                headers = {
                    "Content-Type": "application/json",
                    "api-key": config.api_key,
                }
                
            elif provider.name == "anthropic":
                # Anthropic API
                if not api_url.endswith("/messages"):
                    api_url = f"{api_url.rstrip('/')}/messages"
                    
                headers = {
                    "Content-Type": "application/json",
                    "x-api-key": config.api_key,
                    "anthropic-version": "2023-06-01",
                }
                
            elif provider.name in ["alibaba_bailian", "dashscope"]:
                # 阿里巴巴百炼 API (使用OpenAI兼容模式)
                if not api_url.endswith("/chat/completions"):
                    if "compatible-mode" not in api_url:
                        api_url = "https://dashscope.aliyuncs.com/compatible-mode/v1/chat/completions"
                    else:
                        api_url = f"{api_url.rstrip('/')}/chat/completions"

                headers = {
                    "Content-Type": "application/json",
                    "Authorization": f"Bearer {config.api_key}",
                }

            elif provider.name in ["google", "gemini"]:
                # Google Gemini API
                if not api_url.endswith("/generateContent"):
                    # 构建Gemini API URL
                    if "generativelanguage.googleapis.com" not in api_url:
                        api_url = f"https://generativelanguage.googleapis.com/v1beta/models/{model.name}:generateContent"
                    else:
                        api_url = f"{api_url.rstrip('/')}/generateContent"

                headers = {
                    "Content-Type": "application/json",
                    "x-goog-api-key": config.api_key,
                }

            else:
                raise ValueError(f"不支持的 AI 提供商: {provider.name}")
                
            # 添加代理
            proxy = None
            if config.proxy_url:
                proxy = config.proxy_url
                
            # 发送请求
            max_retries = 3
            retry_delay = 5  # seconds
            last_exception = None

            async with httpx.AsyncClient(proxies=proxy) as client:
                for attempt in range(max_retries):
                    try:
                        logger.info(f"AI Vision API - Attempt {attempt + 1}/{max_retries} to {api_url}")
                        response = await client.post(
                            api_url,
                            json=request_params,
                            headers=headers,
                            timeout=600,  # 增加超时到600秒 (原为300秒)
                        )
                        response.raise_for_status() # Raise HTTPStatusError for bad responses (4xx or 5xx)
                        
                        # 记录响应内容用于调试
                        response_text = response.text
                        logger.debug(f"AI视觉响应原始文本 (attempt {attempt+1}): {response_text[:1000]}...")

                        # 解析响应
                        try:
                            result = response.json()
                            # Successful attempt, break the loop
                            last_exception = None # Clear last exception
                            break 
                        except json.JSONDecodeError as json_err:
                            logger.warning(f"AI视觉响应JSON解析失败 (attempt {attempt+1}): {json_err}")
                            last_exception = ValueError(f"AI响应格式无效，JSON解析失败: {json_err} (Raw: {response_text[:200]}...)")
                            # If JSON parsing fails, retrying might not help unless it was a truncated response
                            # Consider breaking or specific handling for persistent JSON errors
                            if attempt < max_retries - 1:
                                await asyncio.sleep(retry_delay)
                                continue # Go to next attempt
                            else:
                                raise last_exception # Raise after final attempt

                    except httpx.HTTPStatusError as e:
                        logger.error(f"AI API请求失败 (attempt {attempt + 1}) - 状态码: {e.response.status_code}")
                        logger.error(f"响应内容: {e.response.text[:500]}")
                        last_exception = e
                        if e.response.status_code >= 500: # Retry on server errors
                            if attempt < max_retries - 1:
                                await asyncio.sleep(retry_delay)
                                continue
                            else:
                                raise
                        else: # Don't retry on client errors (4xx) immediately
                            raise
                    except httpx.RequestError as e: # Catches network errors, timeout errors, etc.
                        logger.error(f"AI API请求出现 httpx.RequestError (attempt {attempt + 1}): {type(e).__name__} - {e}")
                        last_exception = e
                        if attempt < max_retries - 1:
                            await asyncio.sleep(retry_delay)
                            continue
                        else:
                            # After final attempt, re-raise the last captured exception
                            raise ValueError(f"AI 视觉请求在 {max_retries} 次尝试后失败. 最后错误: {type(last_exception).__name__} - {last_exception}") 
                    # Ensure 'result' is defined if loop completes without breaking due to non-exception path (shouldn't happen with current logic)
                else: # This else clause belongs to the for loop
                    if last_exception:
                         # If loop finished all retries and an exception was set, raise it.
                        # This path implies all retries failed.
                        # The specific re-raise within the httpx.RequestError block might make this redundant for that case.
                        raise ValueError(f"AI 视觉请求在 {max_retries} 次尝试后失败(loop completed). 最后错误: {type(last_exception).__name__} - {last_exception}")
                    # Fallback if loop finishes unexpectedly without result and no exception (should not happen)
                    raise ValueError("AI 视觉请求在所有尝试后未能获取有效结果")

                # 检查响应状态 (This block might be redundant if raise_for_status is effective and loop breaks)
                # if response.status_code != 200:
                #     logger.error(f"AI API请求失败 - 状态码: {response.status_code}")
                #     logger.error(f"请求URL: {api_url}")
                #     logger.error(f"请求头: {headers}")
                #     logger.error(f"请求参数: {json.dumps(request_params, ensure_ascii=False, indent=2)}")
                #     logger.error(f"响应内容: {response.text}")
                    
                # response.raise_for_status() # Already called within retry loop
                
                # 记录响应内容用于调试 (Already logged within retry loop)
                # response_text = response.text 
                # logger.debug(f"AI视觉响应原始文本: {response_text[:1000]}...")
                
                # 解析响应 (Already done within retry loop)
                # try:
                #     result = response.json()
                # except json.JSONDecodeError as json_err:
                # ... (rest of JSON parsing logic moved into the loop)
                
            # 计算耗时
            duration_ms = int((time.time() - start_time) * 1000)
            
            # 解析 token 使用情况
            prompt_tokens = 0
            completion_tokens = 0
            total_tokens = 0
            
            if provider.name in ["openai", "azure", "openrouter", "alibaba_bailian", "dashscope"]:
                usage = result.get("usage", {})
                prompt_tokens = usage.get("prompt_tokens", 0)
                completion_tokens = usage.get("completion_tokens", 0)
                total_tokens = usage.get("total_tokens", 0)

            elif provider.name == "anthropic":
                usage = result.get("usage", {})
                prompt_tokens = usage.get("input_tokens", 0)
                completion_tokens = usage.get("output_tokens", 0)
                total_tokens = prompt_tokens + completion_tokens

            elif provider.name in ["google", "gemini"]:
                # Gemini API token使用情况
                usage_metadata = result.get("usageMetadata", {})
                prompt_tokens = usage_metadata.get("promptTokenCount", 0)
                completion_tokens = usage_metadata.get("candidatesTokenCount", 0)
                total_tokens = usage_metadata.get("totalTokenCount", prompt_tokens + completion_tokens)
                
            # 计算成本
            cost = 0.0
            if model.input_price_per_1k_tokens and model.output_price_per_1k_tokens:
                input_cost = (prompt_tokens / 1000) * model.input_price_per_1k_tokens
                output_cost = (completion_tokens / 1000) * model.output_price_per_1k_tokens
                cost = input_cost + output_cost
                
            # 记录使用情况
            usage_data = AIUsageCreate(
                tenant_id=config.tenant_id or project.tenant_id,
                project_id=project_id,
                user_id=user_id,
                config_id=config.id,
                model_id=model.id,
                request_type="vision",
                prompt_tokens=prompt_tokens,
                completion_tokens=completion_tokens,
                total_tokens=total_tokens,
                cost=cost,
                duration_ms=duration_ms,
                status="success",
                request_metadata={
                    "provider": provider.name,
                    "model": model.name,
                    "temperature": request_params.get("temperature"),
                    "max_tokens": request_params.get("max_tokens"),
                    "prompt_length": len(prompt),
                    "has_additional_messages": bool(additional_messages),
                }
            )
            
            await AIUsageService.create_usage(db, usage_data)
            
            return result
            
        except Exception as e:
            # 计算耗时
            duration_ms = int((time.time() - start_time) * 1000)
            
            # 记录错误
            logger.error(f"AI 视觉请求失败: {e}")
            
            # 如果已经获取到配置和模型，记录使用情况
            if 'config' in locals() and 'model' in locals() and 'project' in locals():
                error_usage_data = AIUsageCreate(
                    tenant_id=config.tenant_id or project.tenant_id,
                    project_id=project_id,
                    user_id=user_id,
                    config_id=config.id if 'config' in locals() else None,
                    model_id=model.id if 'model' in locals() else None,
                    request_type="vision",
                    prompt_tokens=0,
                    completion_tokens=0,
                    total_tokens=0,
                    cost=0,
                    duration_ms=duration_ms,
                    status="failed",
                    error_message=str(e),
                    request_metadata={
                        "provider": provider.name if 'provider' in locals() else None,
                        "model": model.name if 'model' in locals() else None,
                        "prompt_length": len(prompt) if 'prompt' in locals() else 0,
                        "has_additional_messages": bool(additional_messages) if 'additional_messages' in locals() else False,
                    }
                )
                
                await AIUsageService.create_usage(db, error_usage_data)
                
            raise ValueError(f"AI 视觉请求失败: {str(e)}")
