#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
AI 助手服务
"""

import logging
import uuid
import json
import time
import asyncio
from typing import List, Optional, Dict, Any, Tuple, Union
from sqlalchemy import select, func, and_, or_, desc, text
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import joinedload
from datetime import datetime, timedelta

from models.ai import (
    AIAssistant, AIAssistantThread, AIAssistantMessage, AIAssistantTool,
    AIModel, AIConfig, AIKnowledgeBase
)
from models.project import Project
from models.tenant import Tenant
from models.user import User
from models.ai_mcp_server import AIMCPServer
from schemas.ai import (
    AIAssistantCreate, AIAssistantUpdate,
    AIAssistantToolCreate, AIAssistantToolUpdate,
    AIAssistantThreadCreate, AIAssistantThreadUpdate,
    AIAssistantMessageCreate, AIAssistantChatRequest
)
from schemas.ai.chat import <PERSON>ChatRequest, AIChatMessage, AIFunction
from services.ai.chat_service import AI<PERSON>hatService
from services.ai.tool_service import AIToolService
from core.exceptions import BusinessException

logger = logging.getLogger(__name__)

class AIAssistantService:
    """AI 助手服务"""

    @staticmethod
    async def get_assistants(
        db: AsyncSession,
        skip: int = 0,
        limit: int = 100,
        tenant_id: Optional[uuid.UUID] = None,
        project_id: Optional[uuid.UUID] = None,
        status: Optional[str] = None,
        is_public: Optional[bool] = None,
        created_by: Optional[uuid.UUID] = None,
    ) -> Tuple[List[AIAssistant], int]:
        """
        获取 AI 助手列表
        """
        query = select(AIAssistant).options(
            joinedload(AIAssistant.model).joinedload(AIModel.provider),
            joinedload(AIAssistant.config),
            joinedload(AIAssistant.creator)
        )

        # 构建查询条件
        conditions = []

        if tenant_id:
            conditions.append(AIAssistant.tenant_id == tenant_id)

        if project_id:
            conditions.append(AIAssistant.project_id == project_id)

        if status:
            conditions.append(AIAssistant.status == status)

        if is_public is not None:
            conditions.append(AIAssistant.is_public == is_public)

        if created_by:
            conditions.append(AIAssistant.created_by == created_by)

        if conditions:
            query = query.where(and_(*conditions))

        # 获取总数
        count_query = select(func.count()).select_from(query.subquery())
        total = await db.scalar(count_query)

        # 获取分页数据
        query = query.order_by(desc(AIAssistant.created_at)).offset(skip).limit(limit)
        result = await db.execute(query)
        assistants = result.scalars().all()

        # 添加关联数据
        for assistant in assistants:
            if assistant.model:
                assistant.model_name = assistant.model.display_name or assistant.model.name
                assistant.model_display_name = assistant.model.display_name
                if assistant.model.provider:
                    assistant.provider_name = assistant.model.provider.name
            
            if assistant.config:
                assistant.config_name = assistant.config.name
            
            if assistant.creator:
                assistant.creator_name = assistant.creator.username
            
            # 兼容前端字段
            assistant.avatar = assistant.avatar_url

        return assistants, total

    @staticmethod
    async def get_assistant(
        db: AsyncSession,
        assistant_id: uuid.UUID,
    ) -> Optional[AIAssistant]:
        """
        获取 AI 助手详情
        """
        query = select(AIAssistant).options(
            joinedload(AIAssistant.model).joinedload(AIModel.provider),
            joinedload(AIAssistant.config),
            joinedload(AIAssistant.creator)
        ).where(AIAssistant.id == assistant_id)

        result = await db.execute(query)
        assistant = result.scalars().first()
        
        if assistant:
            # 添加关联数据
            if assistant.model:
                assistant.model_name = assistant.model.display_name or assistant.model.name
                assistant.model_display_name = assistant.model.display_name
                if assistant.model.provider:
                    assistant.provider_name = assistant.model.provider.name
            
            if assistant.config:
                assistant.config_name = assistant.config.name
            
            if assistant.creator:
                assistant.creator_name = assistant.creator.username
            
            # 兼容前端字段
            assistant.avatar = assistant.avatar_url
        
        return assistant

    @staticmethod
    async def create_assistant(
        db: AsyncSession,
        assistant_data: AIAssistantCreate,
        user_id: uuid.UUID,
    ) -> AIAssistant:
        """
        创建 AI 助手
        """
        # 检查租户是否存在
        tenant_query = select(Tenant).where(Tenant.id == assistant_data.tenant_id)
        tenant_result = await db.execute(tenant_query)
        tenant = tenant_result.scalars().first()

        if not tenant:
            raise ValueError(f"租户 ID '{assistant_data.tenant_id}' 不存在")

        # 检查项目是否存在
        if assistant_data.project_id:
            project_query = select(Project).where(Project.id == assistant_data.project_id)
            project_result = await db.execute(project_query)
            project = project_result.scalars().first()

            if not project:
                raise ValueError(f"项目 ID '{assistant_data.project_id}' 不存在")

            # 检查项目是否属于该租户
            if project.tenant_id != assistant_data.tenant_id:
                raise ValueError(f"项目 '{project.name}' 不属于租户 ID '{assistant_data.tenant_id}'")

        # 检查模型是否存在
        if assistant_data.model_id:
            model_query = select(AIModel).where(AIModel.id == assistant_data.model_id)
            model_result = await db.execute(model_query)
            model = model_result.scalars().first()

            if not model:
                raise ValueError(f"模型 ID '{assistant_data.model_id}' 不存在")

        # 检查配置是否存在
        if assistant_data.config_id:
            config_query = select(AIConfig).where(AIConfig.id == assistant_data.config_id)
            config_result = await db.execute(config_query)
            config = config_result.scalars().first()

            if not config:
                raise ValueError(f"配置 ID '{assistant_data.config_id}' 不存在")

        # 检查知识库是否存在
        if assistant_data.knowledge_base_ids:
            for kb_id in assistant_data.knowledge_base_ids:
                kb_query = select(AIKnowledgeBase).where(AIKnowledgeBase.id == kb_id)
                kb_result = await db.execute(kb_query)
                kb = kb_result.scalars().first()

                if not kb:
                    raise ValueError(f"知识库 ID '{kb_id}' 不存在")

                # 检查知识库是否属于该租户或项目
                if kb.tenant_id != assistant_data.tenant_id:
                    raise ValueError(f"知识库 '{kb.name}' 不属于租户 ID '{assistant_data.tenant_id}'")

                if assistant_data.project_id and kb.project_id and kb.project_id != assistant_data.project_id:
                    raise ValueError(f"知识库 '{kb.name}' 不属于项目 ID '{assistant_data.project_id}'")

        # 检查工具是否存在
        if assistant_data.tool_ids:
            for tool_id in assistant_data.tool_ids:
                tool_query = select(AIAssistantTool).where(AIAssistantTool.id == tool_id)
                tool_result = await db.execute(tool_query)
                tool = tool_result.scalars().first()

                if not tool:
                    raise ValueError(f"工具 ID '{tool_id}' 不存在")

                # 检查工具是否属于该租户或项目
                if tool.tenant_id != assistant_data.tenant_id:
                    raise ValueError(f"工具 '{tool.name}' 不属于租户 ID '{assistant_data.tenant_id}'")

                if assistant_data.project_id and tool.project_id and tool.project_id != assistant_data.project_id:
                    raise ValueError(f"工具 '{tool.name}' 不属于项目 ID '{assistant_data.project_id}'")

        # 创建助手
        assistant_dict = assistant_data.dict()
        assistant_dict["created_by"] = user_id

        assistant = AIAssistant(**assistant_dict)
        db.add(assistant)
        await db.commit()
        await db.refresh(assistant)

        logger.info(f"创建 AI 助手成功: {assistant.id}")
        return assistant

    @staticmethod
    async def update_assistant(
        db: AsyncSession,
        assistant_id: uuid.UUID,
        assistant_data: AIAssistantUpdate,
    ) -> Optional[AIAssistant]:
        """
        更新 AI 助手
        """
        # 获取助手
        assistant = await AIAssistantService.get_assistant(db, assistant_id)
        if not assistant:
            return None

        # 检查模型是否存在
        if assistant_data.model_id:
            model_query = select(AIModel).where(AIModel.id == assistant_data.model_id)
            model_result = await db.execute(model_query)
            model = model_result.scalars().first()

            if not model:
                raise ValueError(f"模型 ID '{assistant_data.model_id}' 不存在")

        # 检查配置是否存在
        if assistant_data.config_id:
            config_query = select(AIConfig).where(AIConfig.id == assistant_data.config_id)
            config_result = await db.execute(config_query)
            config = config_result.scalars().first()

            if not config:
                raise ValueError(f"配置 ID '{assistant_data.config_id}' 不存在")

        # 检查知识库是否存在
        if assistant_data.knowledge_base_ids:
            for kb_id in assistant_data.knowledge_base_ids:
                kb_query = select(AIKnowledgeBase).where(AIKnowledgeBase.id == kb_id)
                kb_result = await db.execute(kb_query)
                kb = kb_result.scalars().first()

                if not kb:
                    raise ValueError(f"知识库 ID '{kb_id}' 不存在")

                # 检查知识库是否属于该租户或项目
                if kb.tenant_id != assistant.tenant_id:
                    raise ValueError(f"知识库 '{kb.name}' 不属于租户 ID '{assistant.tenant_id}'")

                if assistant.project_id and kb.project_id and kb.project_id != assistant.project_id:
                    raise ValueError(f"知识库 '{kb.name}' 不属于项目 ID '{assistant.project_id}'")

        # 检查工具是否存在
        if assistant_data.tool_ids:
            for tool_id in assistant_data.tool_ids:
                tool_query = select(AIAssistantTool).where(AIAssistantTool.id == tool_id)
                tool_result = await db.execute(tool_query)
                tool = tool_result.scalars().first()

                if not tool:
                    raise ValueError(f"工具 ID '{tool_id}' 不存在")

                # 检查工具是否属于该租户或项目
                if tool.tenant_id != assistant.tenant_id:
                    raise ValueError(f"工具 '{tool.name}' 不属于租户 ID '{assistant.tenant_id}'")

                if assistant.project_id and tool.project_id and tool.project_id != assistant.project_id:
                    raise ValueError(f"工具 '{tool.name}' 不属于项目 ID '{assistant.project_id}'")

        # 更新助手
        update_data = assistant_data.dict(exclude_unset=True)
        for key, value in update_data.items():
            setattr(assistant, key, value)

        await db.commit()
        await db.refresh(assistant)

        logger.info(f"更新 AI 助手成功: {assistant.id}")
        return assistant

    @staticmethod
    async def delete_assistant(
        db: AsyncSession,
        assistant_id: uuid.UUID,
    ) -> bool:
        """
        删除 AI 助手
        """
        # 获取助手
        assistant = await AIAssistantService.get_assistant(db, assistant_id)
        if not assistant:
            return False

        # 删除助手
        await db.delete(assistant)
        await db.commit()

        logger.info(f"删除 AI 助手成功: {assistant.id}")
        return True

    @staticmethod
    async def get_tools(
        db: AsyncSession,
        skip: int = 0,
        limit: int = 100,
        tenant_id: Optional[uuid.UUID] = None,
        project_id: Optional[uuid.UUID] = None,
        type: Optional[str] = None,
        status: Optional[str] = None,
        created_by: Optional[uuid.UUID] = None,
    ) -> Tuple[List[AIAssistantTool], int]:
        """
        获取 AI 助手工具列表
        """
        query = select(AIAssistantTool).options(
            joinedload(AIAssistantTool.creator)
        )

        # 构建查询条件
        conditions = []

        if tenant_id:
            conditions.append(AIAssistantTool.tenant_id == tenant_id)

        if project_id:
            conditions.append(AIAssistantTool.project_id == project_id)

        if type:
            conditions.append(AIAssistantTool.type == type)

        if status:
            conditions.append(AIAssistantTool.status == status)

        if created_by:
            conditions.append(AIAssistantTool.created_by == created_by)

        if conditions:
            query = query.where(and_(*conditions))

        # 获取总数
        count_query = select(func.count()).select_from(query.subquery())
        total = await db.scalar(count_query)

        # 获取分页数据
        query = query.order_by(desc(AIAssistantTool.created_at)).offset(skip).limit(limit)
        result = await db.execute(query)
        tools = result.scalars().all()

        return tools, total

    @staticmethod
    async def get_tool(
        db: AsyncSession,
        tool_id: uuid.UUID,
    ) -> Optional[AIAssistantTool]:
        """
        获取 AI 助手工具详情
        """
        query = select(AIAssistantTool).options(
            joinedload(AIAssistantTool.creator)
        ).where(AIAssistantTool.id == tool_id)

        result = await db.execute(query)
        return result.scalars().first()

    @staticmethod
    async def create_tool(
        db: AsyncSession,
        tool_data: AIAssistantToolCreate,
        user_id: uuid.UUID,
    ) -> AIAssistantTool:
        """
        创建 AI 助手工具
        """
        # 检查租户是否存在
        tenant_query = select(Tenant).where(Tenant.id == tool_data.tenant_id)
        tenant_result = await db.execute(tenant_query)
        tenant = tenant_result.scalars().first()

        if not tenant:
            raise ValueError(f"租户 ID '{tool_data.tenant_id}' 不存在")

        # 检查项目是否存在
        if tool_data.project_id:
            project_query = select(Project).where(Project.id == tool_data.project_id)
            project_result = await db.execute(project_query)
            project = project_result.scalars().first()

            if not project:
                raise ValueError(f"项目 ID '{tool_data.project_id}' 不存在")

            # 检查项目是否属于该租户
            if project.tenant_id != tool_data.tenant_id:
                raise ValueError(f"项目 '{project.name}' 不属于租户 ID '{tool_data.tenant_id}'")

        # 创建工具
        tool_dict = tool_data.dict()
        tool_dict["created_by"] = user_id

        tool = AIAssistantTool(**tool_dict)
        db.add(tool)
        await db.commit()
        await db.refresh(tool)

        logger.info(f"创建 AI 助手工具成功: {tool.id}")
        return tool

    @staticmethod
    async def update_tool(
        db: AsyncSession,
        tool_id: uuid.UUID,
        tool_data: AIAssistantToolUpdate,
    ) -> Optional[AIAssistantTool]:
        """
        更新 AI 助手工具
        """
        # 获取工具
        tool = await AIAssistantService.get_tool(db, tool_id)
        if not tool:
            return None

        # 更新工具
        update_data = tool_data.dict(exclude_unset=True)
        for key, value in update_data.items():
            setattr(tool, key, value)

        await db.commit()
        await db.refresh(tool)

        logger.info(f"更新 AI 助手工具成功: {tool.id}")
        return tool

    @staticmethod
    async def delete_tool(
        db: AsyncSession,
        tool_id: uuid.UUID,
    ) -> bool:
        """
        删除 AI 助手工具
        """
        # 获取工具
        tool = await AIAssistantService.get_tool(db, tool_id)
        if not tool:
            return False

        # 检查是否为内置工具
        if tool.is_builtin:
            raise ValueError("内置工具不能删除")

        # 删除工具
        await db.delete(tool)
        await db.commit()

        logger.info(f"删除 AI 助手工具成功: {tool.id}")
        return True

    @staticmethod
    async def get_threads(
        db: AsyncSession,
        skip: int = 0,
        limit: int = 100,
        assistant_id: Optional[uuid.UUID] = None,
        user_id: Optional[uuid.UUID] = None,
        status: Optional[str] = None,
    ) -> Tuple[List[AIAssistantThread], int]:
        """
        获取 AI 助手对话线程列表
        """
        query = select(AIAssistantThread).options(
            joinedload(AIAssistantThread.assistant),
            joinedload(AIAssistantThread.user)
        )

        # 构建查询条件
        conditions = []

        if assistant_id:
            conditions.append(AIAssistantThread.assistant_id == assistant_id)

        if user_id:
            conditions.append(AIAssistantThread.user_id == user_id)

        if status:
            conditions.append(AIAssistantThread.status == status)

        if conditions:
            query = query.where(and_(*conditions))

        # 获取总数
        count_query = select(func.count()).select_from(query.subquery())
        total = await db.scalar(count_query)

        # 获取分页数据
        query = query.order_by(desc(AIAssistantThread.updated_at)).offset(skip).limit(limit)
        result = await db.execute(query)
        threads = result.scalars().all()

        # 获取每个线程的消息数量
        for thread in threads:
            message_count_query = select(func.count()).where(AIAssistantMessage.thread_id == thread.id)
            message_count = await db.scalar(message_count_query)
            thread.message_count = message_count

        return threads, total

    @staticmethod
    async def get_thread(
        db: AsyncSession,
        thread_id: uuid.UUID,
    ) -> Optional[AIAssistantThread]:
        """
        获取 AI 助手对话线程详情
        """
        query = select(AIAssistantThread).options(
            joinedload(AIAssistantThread.assistant),
            joinedload(AIAssistantThread.user)
        ).where(AIAssistantThread.id == thread_id)

        result = await db.execute(query)
        thread = result.scalars().first()

        if thread:
            # 获取线程的消息数量
            message_count_query = select(func.count()).where(AIAssistantMessage.thread_id == thread.id)
            message_count = await db.scalar(message_count_query)
            thread.message_count = message_count

        return thread

    @staticmethod
    async def create_thread(
        db: AsyncSession,
        thread_data: AIAssistantThreadCreate,
    ) -> AIAssistantThread:
        """
        创建 AI 助手对话线程
        """
        # 检查助手是否存在
        assistant_query = select(AIAssistant).where(AIAssistant.id == thread_data.assistant_id)
        assistant_result = await db.execute(assistant_query)
        assistant = assistant_result.scalars().first()

        if not assistant:
            raise ValueError(f"助手 ID '{thread_data.assistant_id}' 不存在")

        # 检查用户是否存在
        user_query = select(User).where(User.id == thread_data.user_id)
        user_result = await db.execute(user_query)
        user = user_result.scalars().first()

        if not user:
            raise ValueError(f"用户 ID '{thread_data.user_id}' 不存在")

        # 创建线程
        thread = AIAssistantThread(**thread_data.dict())
        db.add(thread)
        await db.commit()
        await db.refresh(thread)

        # 创建系统消息
        if assistant.instructions:
            system_message = AIAssistantMessage(
                thread_id=thread.id,
                role="system",
                content=assistant.instructions,
                content_type="text"
            )
            db.add(system_message)
            await db.commit()

        logger.info(f"创建 AI 助手对话线程成功: {thread.id}")
        return thread

    @staticmethod
    async def update_thread(
        db: AsyncSession,
        thread_id: uuid.UUID,
        thread_data: AIAssistantThreadUpdate,
    ) -> Optional[AIAssistantThread]:
        """
        更新 AI 助手对话线程
        """
        # 获取线程
        thread = await AIAssistantService.get_thread(db, thread_id)
        if not thread:
            return None

        # 更新线程
        update_data = thread_data.dict(exclude_unset=True)
        for key, value in update_data.items():
            setattr(thread, key, value)

        await db.commit()
        await db.refresh(thread)

        logger.info(f"更新 AI 助手对话线程成功: {thread.id}")
        return thread

    @staticmethod
    async def delete_thread(
        db: AsyncSession,
        thread_id: uuid.UUID,
    ) -> bool:
        """
        删除 AI 助手对话线程
        """
        # 获取线程
        thread = await AIAssistantService.get_thread(db, thread_id)
        if not thread:
            return False

        # 删除线程
        await db.delete(thread)
        await db.commit()

        logger.info(f"删除 AI 助手对话线程成功: {thread.id}")
        return True

    @staticmethod
    async def get_messages(
        db: AsyncSession,
        thread_id: uuid.UUID,
        skip: int = 0,
        limit: int = 100,
        role: Optional[str] = None,
    ) -> Tuple[List[AIAssistantMessage], int]:
        """
        获取 AI 助手消息列表
        """
        query = select(AIAssistantMessage).where(AIAssistantMessage.thread_id == thread_id)

        if role:
            query = query.where(AIAssistantMessage.role == role)

        # 获取总数
        count_query = select(func.count()).select_from(query.subquery())
        total = await db.scalar(count_query)

        # 获取分页数据
        query = query.order_by(desc(AIAssistantMessage.created_at)).offset(skip).limit(limit)
        result = await db.execute(query)
        messages = result.scalars().all()

        return messages, total

    @staticmethod
    async def get_message(
        db: AsyncSession,
        message_id: uuid.UUID,
    ) -> Optional[AIAssistantMessage]:
        """
        获取 AI 助手消息详情
        """
        query = select(AIAssistantMessage).where(AIAssistantMessage.id == message_id)
        result = await db.execute(query)
        return result.scalars().first()

    @staticmethod
    async def create_message(
        db: AsyncSession,
        message_data: AIAssistantMessageCreate,
    ) -> AIAssistantMessage:
        """
        创建 AI 助手消息
        """
        # 检查线程是否存在
        thread_query = select(AIAssistantThread).where(AIAssistantThread.id == message_data.thread_id)
        thread_result = await db.execute(thread_query)
        thread = thread_result.scalars().first()

        if not thread:
            raise ValueError(f"线程 ID '{message_data.thread_id}' 不存在")

        # 创建消息
        message = AIAssistantMessage(**message_data.dict())
        db.add(message)

        # 更新线程的最后消息时间
        thread.last_message_at = func.now()

        await db.commit()
        await db.refresh(message)

        logger.info(f"创建 AI 助手消息成功: {message.id}")
        return message

    @staticmethod
    async def get_mcp_tools_from_servers(
        db: AsyncSession,
        project_id: uuid.UUID,
        mcp_server_ids: List[uuid.UUID]
    ) -> List[Dict[str, Any]]:
        """从MCP服务器获取可用工具列表"""
        available_tools = []
        
        try:
            # 查询指定的MCP服务器
            result = await db.execute(
                select(AIMCPServer).where(
                    and_(
                        AIMCPServer.project_id == project_id,
                        AIMCPServer.id.in_(mcp_server_ids),
                        AIMCPServer.enabled == True,
                        AIMCPServer.status.in_(["connected", "initialized"])
                    )
                )
            )
            servers = result.scalars().all()
            
            for server in servers:
                if server.available_tools:
                    for tool in server.available_tools:
                        # 按照OpenAI function calling格式构建工具
                        tool_spec = {
                            "type": "function",
                            "function": {
                                "name": tool.get("name", ""),
                                "description": tool.get("description", ""),
                                "parameters": tool.get("inputSchema", {})
                            },
                            # 添加MCP服务器信息用于后续工具调用
                            "_mcp_server_id": str(server.id),
                            "_mcp_tool_name": tool.get("name", "")
                        }
                        
                        # 确保参数格式正确
                        if not tool_spec["function"]["parameters"]:
                            tool_spec["function"]["parameters"] = {
                                "type": "object",
                                "properties": {},
                                "required": []
                            }
                        
                        available_tools.append(tool_spec)
                        
            logger.info(f"从 {len(servers)} 个MCP服务器获取到 {len(available_tools)} 个工具")
            
        except Exception as e:
            logger.error(f"获取MCP工具失败: {e}")
            
        return available_tools

    @staticmethod
    async def execute_mcp_tool(
        db: AsyncSession,
        server_id: uuid.UUID,
        tool_name: str,
        parameters: Dict[str, Any],
        user_id: uuid.UUID
    ) -> Dict[str, Any]:
        """执行MCP工具"""
        try:
            # 获取MCP服务器
            result = await db.execute(
                select(AIMCPServer).where(AIMCPServer.id == server_id)
            )
            server = result.scalars().first()
            
            if not server:
                return {"success": False, "error": "MCP服务器不存在"}
            
            if not server.enabled:
                return {"success": False, "error": "MCP服务器已禁用"}
            
            # 根据服务器类型执行工具
            if server.source == "sapi" and server.sapi_server_id:
                # SAPI服务器通过内部代理调用
                return await AIAssistantService._execute_sapi_mcp_tool(
                    db, str(server.sapi_server_id), tool_name, parameters, user_id
                )
            else:
                # 外部MCP服务器直接调用
                return await AIAssistantService._execute_external_mcp_tool(
                    server, tool_name, parameters, user_id
                )
                
        except Exception as e:
            logger.error(f"执行MCP工具失败: {e}")
            return {"success": False, "error": str(e)}

    @staticmethod
    async def _execute_sapi_mcp_tool(
        db: AsyncSession,
        sapi_server_id: str,
        tool_name: str,
        parameters: Dict[str, Any],
        user_id: uuid.UUID
    ) -> Dict[str, Any]:
        """执行SAPI MCP工具"""
        try:
            # 通过SAPI代理执行工具
            from plugins.sapi.mcp_proxy import mcp_proxy
            
            # 构建MCP工具调用请求
            tool_request = {
                "jsonrpc": "2.0",
                "id": str(uuid.uuid4()),
                "method": "tools/call",
                "params": {
                    "name": tool_name,
                    "arguments": parameters
                }
            }
            
            # 通过代理发送请求
            response = await mcp_proxy.send_request(sapi_server_id, tool_request)
            
            if response.get("error"):
                return {
                    "success": False, 
                    "error": response["error"].get("message", "工具执行失败")
                }
            
            return {
                "success": True,
                "data": response.get("result", {})
            }
            
        except Exception as e:
            logger.error(f"执行SAPI MCP工具失败: {e}")
            return {"success": False, "error": str(e)}

    @staticmethod
    async def _execute_external_mcp_tool(
        server: AIMCPServer,
        tool_name: str,
        parameters: Dict[str, Any],
        user_id: uuid.UUID
    ) -> Dict[str, Any]:
        """执行外部MCP工具"""
        try:
            import httpx
            import json
            
            config = server.config or {}
            
            if server.transport_type == "streamhttp":
                url = config.get("url")
                headers = config.get("headers", {})
                headers.setdefault("Content-Type", "application/json")
                
                tool_request = {
                    "jsonrpc": "2.0",
                    "id": str(uuid.uuid4()),
                    "method": "tools/call",
                    "params": {
                        "name": tool_name,
                        "arguments": parameters
                    }
                }
                
                async with httpx.AsyncClient() as client:
                    response = await client.post(url, json=tool_request, headers=headers, timeout=30)
                    
                    if response.status_code == 200:
                        result = response.json()
                        
                        if result.get("error"):
                            return {
                                "success": False,
                                "error": result["error"].get("message", "工具执行失败")
                            }
                        
                        return {
                            "success": True,
                            "data": result.get("result", {})
                        }
                    else:
                        return {
                            "success": False,
                            "error": f"HTTP错误: {response.status_code}"
                        }
            elif server.transport_type == "sse":
                # SSE协议：使用与初始化相同的并发模式
                url = config.get("url")
                headers = config.get("headers", {})
                
                # 构建工具调用请求
                tool_request = {
                    "jsonrpc": "2.0",
                    "id": str(uuid.uuid4()),
                    "method": "tools/call",
                    "params": {
                        "name": tool_name,
                        "arguments": parameters
                    }
                }
                
                async with httpx.AsyncClient() as client:
                    # 建立SSE连接并发送工具调用请求
                    sse_headers = headers.copy()
                    sse_headers.setdefault("Accept", "text/event-stream")
                    sse_headers.setdefault("Cache-Control", "no-cache")
                    sse_headers.setdefault("Connection", "keep-alive")
                    
                    message_endpoint = None
                    tool_result = None
                    
                    async with client.stream('GET', url, headers=sse_headers, timeout=60) as sse_response:
                        if sse_response.status_code != 200:
                            return {
                                "success": False,
                                "error": f"SSE连接失败，状态码: {sse_response.status_code}"
                            }
                        
                        # 存储MCP响应
                        mcp_responses = {}
                        
                        async def sse_listener():
                            """监听SSE事件并解析MCP响应"""
                            nonlocal message_endpoint, mcp_responses
                            
                            buffer = ""
                            try:
                                async for chunk in sse_response.aiter_text():
                                    buffer += chunk
                                    lines = buffer.split('\n')
                                    buffer = lines[-1]
                                    
                                    for line in lines[:-1]:
                                        line = line.strip()
                                        if line:
                                            # 解析端点信息
                                            if line.startswith("data:") and "/mcp/message" in line:
                                                endpoint_path = line[5:].strip()
                                                if endpoint_path.startswith("/mcp/message"):
                                                    from urllib.parse import urlparse
                                                    parsed_url = urlparse(url)
                                                    message_endpoint = f"{parsed_url.scheme}://{parsed_url.netloc}{endpoint_path}"
                                            
                                            # 解析MCP响应
                                            elif line.startswith("data:") and line != "data:" and "{" in line:
                                                try:
                                                    response_text = line[5:].strip()
                                                    response_data = json.loads(response_text)
                                                    if "id" in response_data:
                                                        request_id = response_data["id"]
                                                        mcp_responses[request_id] = response_data
                                                except Exception as e:
                                                    logger.warning(f"解析SSE响应失败: {e}")
                            except Exception as e:
                                logger.warning(f"SSE监听异常: {e}")
                        
                        async def send_tool_request():
                            """发送工具调用请求"""
                            nonlocal tool_result
                            
                            # 等待获取到端点
                            max_wait = 10
                            wait_time = 0
                            while not message_endpoint and wait_time < max_wait:
                                await asyncio.sleep(0.5)
                                wait_time += 0.5
                            
                            if not message_endpoint:
                                tool_result = {"success": False, "error": "未能获取到消息端点"}
                                return
                            
                            try:
                                mcp_headers = {"Content-Type": "application/json"}
                                mcp_headers.update(headers)
                                
                                # 发送工具调用请求
                                await client.post(
                                    message_endpoint, 
                                    json=tool_request, 
                                    headers=mcp_headers, 
                                    timeout=30
                                )
                                
                                # 等待工具响应
                                request_id = tool_request["id"]
                                wait_time = 0
                                while wait_time < 30:
                                    if request_id in mcp_responses:
                                        response_data = mcp_responses.pop(request_id)
                                        
                                        if "error" in response_data:
                                            tool_result = {
                                                "success": False,
                                                "error": response_data["error"].get("message", "工具执行失败")
                                            }
                                        elif "result" in response_data:
                                            tool_result = {
                                                "success": True,
                                                "data": response_data["result"]
                                            }
                                        else:
                                            tool_result = {
                                                "success": False,
                                                "error": "未收到有效的工具响应"
                                            }
                                        return
                                    
                                    await asyncio.sleep(0.5)
                                    wait_time += 0.5
                                
                                tool_result = {"success": False, "error": "工具调用超时"}
                                
                            except Exception as e:
                                tool_result = {"success": False, "error": f"工具调用失败: {str(e)}"}
                        
                        # 并发执行SSE监听和工具调用
                        await asyncio.gather(
                            sse_listener(),
                            send_tool_request()
                        )
                        
                        return tool_result or {"success": False, "error": "工具调用失败"}
            else:
                return {
                    "success": False,
                    "error": f"不支持的传输协议: {server.transport_type}"
                }
                
        except Exception as e:
            logger.error(f"执行外部MCP工具失败: {e}")
            return {"success": False, "error": str(e)}

    @staticmethod
    async def chat_with_assistant(
        db: AsyncSession,
        chat_request: AIAssistantChatRequest,
        user_id: uuid.UUID,
    ) -> Dict[str, Any]:
        """与AI助手对话"""
        try:
            # 获取助手信息
            assistant = await AIAssistantService.get_assistant(
                db, chat_request.assistant_id
            )
            if not assistant:
                raise BusinessException("助手不存在或无权限访问")

            # 获取或创建对话线程
            if chat_request.thread_id:
                thread = await AIAssistantService.get_thread(
                    db, chat_request.thread_id
                )
                if not thread or thread.assistant_id != assistant.id:
                    raise BusinessException("对话线程不存在或不属于当前助手")
            else:
                # 创建新的对话线程
                thread = AIAssistantThread(
                    assistant_id=assistant.id,
                    user_id=user_id,
                    title=chat_request.content[:50] + "..." if len(chat_request.content) > 50 else chat_request.content,
                    status="active"
                )
                db.add(thread)
                await db.commit()
                await db.refresh(thread)

            # 创建用户消息记录
            user_message = AIAssistantMessage(
                thread_id=thread.id,
                role="user",
                content=chat_request.content
            )
            db.add(user_message)

            # 获取对话历史
            messages_result = await db.execute(
                select(AIAssistantMessage)
                .where(AIAssistantMessage.thread_id == thread.id)
                .order_by(AIAssistantMessage.created_at)
                .limit(50)  # 限制历史消息数量
            )
            history_messages = messages_result.scalars().all()

            # 构建对话消息列表
            chat_messages = []
            
            # 添加系统消息
            if assistant.instructions:
                chat_messages.append({
                    "role": "system",
                    "content": assistant.instructions
                })

            # 添加历史消息
            for msg in history_messages:
                chat_messages.append({
                    "role": msg.role,
                    "content": msg.content
                })

            # 添加当前用户消息
            chat_messages.append({
                "role": "user",
                "content": chat_request.content
            })

            # 获取知识库上下文（如果配置了）
            context = ""
            if assistant.knowledge_base_ids:
                try:
                    from services.knowledge_base import KnowledgeBaseService
                    context = await KnowledgeBaseService.search_context(
                        db=db,
                        query=chat_request.content,
                        knowledge_base_ids=assistant.knowledge_base_ids,
                        user_id=user_id
                    )
                except Exception as e:
                    logger.warning(f"获取知识库上下文失败: {e}")

            # 如果有上下文，添加到系统消息
            if context:
                system_message = {
                    "role": "system",
                    "content": f"{assistant.instructions}\n\n{context}"
                }

                # 替换原有的系统消息
                for i, msg in enumerate(chat_messages):
                    if msg["role"] == "system":
                        chat_messages[i] = system_message
                        break
                else:
                    # 如果没有系统消息，添加一个
                    chat_messages.insert(0, system_message)

            # 准备工具调用（包括MCP工具）
            available_tools = []
            
            # 1. 获取传统的MCP工具（保持向后兼容）
            if assistant.mcp_tools:
                from services.mcp_tool import MCPToolService
                
                # 获取助手配置的MCP工具
                for tool_id in assistant.mcp_tools:
                    try:
                        tool = await MCPToolService.get_tool(db, tool_id)
                        if tool and tool.status == "active":
                            available_tools.append({
                                "type": "function",
                                "function": {
                                    "name": tool.name,
                                    "description": tool.description,
                                    "parameters": tool.parameters or {}
                                },
                                "_legacy_mcp_tool_id": str(tool_id)
                            })
                    except Exception as e:
                        logger.error(f"获取传统MCP工具失败: {e}")

            # 2. 获取MCP服务器工具（新的方式）
            if assistant.mcp_server_ids:
                mcp_tools = await AIAssistantService.get_mcp_tools_from_servers(
                    db, assistant.project_id, assistant.mcp_server_ids
                )
                available_tools.extend(mcp_tools)

            # 调用 AI 聊天服务
            try:
                # 确保使用正确的项目ID
                project_id = assistant.project_id
                if not project_id:
                    # 如果助手没有项目ID，尝试从线程获取
                    if hasattr(thread, 'project_id') and thread.project_id:
                        project_id = thread.project_id
                    else:
                        # 最后使用租户ID作为fallback
                        project_id = assistant.tenant_id
                
                # 构建聊天请求参数
                chat_params = {
                    "db": db,
                    "project_id": project_id,
                    "user_id": user_id,
                    "messages": chat_messages,
                    "model_id": assistant.model_id,
                    "config_id": assistant.config_id,
                    "temperature": assistant.temperature,
                    "max_tokens": assistant.max_tokens,
                    "is_shared": False  # 确保记录使用统计
                }
                
                # 如果有可用工具，添加到请求中
                if available_tools:
                    chat_params["tools"] = available_tools
                    chat_params["tool_choice"] = "auto"
                
                chat_response = await AIChatService.chat_completion(**chat_params)

                # 检查是否有工具调用
                tool_calls = []
                if "choices" in chat_response and chat_response["choices"]:
                    message = chat_response["choices"][0].get("message", {})
                    if "tool_calls" in message:
                        tool_calls = message["tool_calls"]

                # 处理工具调用
                if tool_calls:
                    tool_results = []
                    for tool_call in tool_calls:
                        function_name = tool_call["function"]["name"]
                        function_args = json.loads(tool_call["function"]["arguments"])
                        
                        # 查找对应的工具
                        tool_result = None
                        
                        # 首先查找MCP服务器工具
                        for tool_spec in available_tools:
                            if (tool_spec["function"]["name"] == function_name and 
                                "_mcp_server_id" in tool_spec):
                                try:
                                    server_id = uuid.UUID(tool_spec["_mcp_server_id"])
                                    tool_result = await AIAssistantService.execute_mcp_tool(
                                        db=db,
                                        server_id=server_id,
                                        tool_name=function_name,
                                        parameters=function_args,
                                        user_id=user_id
                                    )
                                    break
                                except Exception as e:
                                    logger.error(f"执行MCP服务器工具失败: {e}")
                                    tool_result = {"success": False, "error": str(e)}
                                    break
                        
                        # 如果没找到MCP服务器工具，查找传统MCP工具
                        if not tool_result:
                            for tool_spec in available_tools:
                                if (tool_spec["function"]["name"] == function_name and 
                                    "_legacy_mcp_tool_id" in tool_spec):
                                    try:
                                        from services.mcp_tool import MCPToolService
                                        tool_id = uuid.UUID(tool_spec["_legacy_mcp_tool_id"])
                                        tool_result = await MCPToolService.execute_tool(
                                            db=db,
                                            tool_id=tool_id,
                                            parameters=function_args,
                                            user_id=user_id
                                        )
                                        break
                                    except Exception as e:
                                        logger.error(f"执行传统MCP工具失败: {e}")
                                        tool_result = {"success": False, "error": str(e)}
                                        break
                        
                        # 构建工具结果
                        if tool_result:
                            if tool_result.get("success"):
                                content = json.dumps(tool_result.get("data", {}))
                            else:
                                content = f"工具执行失败: {tool_result.get('error', '未知错误')}"
                        else:
                            content = f"未找到工具: {function_name}"
                        
                        tool_results.append({
                            "tool_call_id": tool_call["id"],
                            "role": "tool",
                            "name": function_name,
                            "content": content
                        })

                    # 如果有工具调用结果，需要再次调用AI获取最终回复
                    if tool_results:
                        # 添加工具调用消息到对话历史
                        chat_messages.append(chat_response["choices"][0]["message"])
                        chat_messages.extend(tool_results)
                        
                        # 重新构建聊天参数（不再包含工具，避免循环调用）
                        final_chat_params = {
                            "db": db,
                            "project_id": project_id,
                            "user_id": user_id,
                            "messages": chat_messages,
                            "model_id": assistant.model_id,
                            "config_id": assistant.config_id,
                            "temperature": assistant.temperature,
                            "max_tokens": assistant.max_tokens,
                            "is_shared": False
                        }
                        
                        # 获取最终回复
                        chat_response = await AIChatService.chat_completion(**final_chat_params)

                # 获取助手回复内容
                assistant_content = ""
                if "choices" in chat_response and chat_response["choices"]:
                    assistant_content = chat_response["choices"][0]["message"]["content"]

                # 创建助手消息记录
                assistant_message = AIAssistantMessage(
                    thread_id=thread.id,
                    role="assistant",
                    content=assistant_content
                )
                db.add(assistant_message)

                await db.commit()
                await db.refresh(assistant_message)

                return {
                    "thread_id": thread.id,
                    "assistant_id": assistant.id,
                    "assistant_name": assistant.name,
                    "response": assistant_content,
                    "created_at": assistant_message.created_at
                }

            except Exception as e:
                logger.error(f"AI聊天失败: {e}")
                await db.rollback()
                raise BusinessException(f"AI聊天失败: {str(e)}")

        except BusinessException:
            raise
        except Exception as e:
            logger.error(f"助手对话失败: {e}")
            await db.rollback()
            raise BusinessException(f"助手对话失败: {str(e)}")
