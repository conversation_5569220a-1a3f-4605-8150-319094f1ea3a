#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
AI 聊天服务
"""

import logging
import uuid
import json
import time
import httpx
from typing import List, Optional, Dict, Any, Union
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select
from sqlalchemy.orm import joinedload

from models.ai import AIConfig, AIModel, AIProvider
from models.project import Project
from models.user import User
from services.ai import AIConfigService, AIUsageService
from schemas.ai import AIUsageCreate

logger = logging.getLogger(__name__)

class AIChatService:
    """AI 聊天服务"""

    @staticmethod
    async def chat_completion(
        db: AsyncSession,
        project_id: uuid.UUID,
        user_id: Optional[uuid.UUID] = None,
        messages: List[Dict[str, Any]] = None,
        model_id: Optional[uuid.UUID] = None,
        config_id: Optional[uuid.UUID] = None,
        temperature: Optional[float] = None,
        max_tokens: Optional[int] = None,
        top_p: Optional[float] = None,
        frequency_penalty: Optional[float] = None,
        functions: Optional[List[Dict[str, Any]]] = None,
        function_call: Optional[Union[str, Dict[str, Any]]] = None,
        tools: Optional[List[Dict[str, Any]]] = None,
        tool_choice: Optional[Union[str, Dict[str, Any]]] = None,
        stream: bool = False,
        is_shared: bool = False,
    ) -> Dict[str, Any]:
        """
        AI 聊天完成
        """
        start_time = time.time()

        try:
            # 获取项目信息
            project_query = select(Project).where(Project.id == project_id)
            project_result = await db.execute(project_query)
            project = project_result.scalars().first()

            if not project:
                raise ValueError(f"项目 ID '{project_id}' 不存在")

            # 获取配置信息
            if config_id:
                config_query = select(AIConfig).options(
                    joinedload(AIConfig.provider)
                ).where(AIConfig.id == config_id)
                config_result = await db.execute(config_query)
                config = config_result.scalars().first()
            else:
                # 使用默认配置
                config_query = select(AIConfig).options(
                    joinedload(AIConfig.provider)
                ).where(
                    AIConfig.tenant_id == project.tenant_id,
                    AIConfig.is_default == True
                )
                config_result = await db.execute(config_query)
                config = config_result.scalars().first()

            if not config:
                raise ValueError("未找到可用的 AI 配置")

            provider = config.provider
            if not provider:
                raise ValueError("AI 配置缺少提供商信息")

            # 获取模型信息
            if model_id:
                model_query = select(AIModel).where(AIModel.id == model_id)
                model_result = await db.execute(model_query)
                model = model_result.scalars().first()
            else:
                # 使用配置的默认模型
                model_query = select(AIModel).where(AIModel.id == config.model_id)
                model_result = await db.execute(model_query)
                model = model_result.scalars().first()

            if not model:
                raise ValueError("未找到可用的 AI 模型")

            # 构建请求参数
            request_params = {
                "model": model.name,
                "messages": messages,
            }

            # 添加可选参数
            if temperature is not None:
                request_params["temperature"] = temperature
            elif model.default_temperature is not None:
                request_params["temperature"] = model.default_temperature

            if max_tokens is not None:
                request_params["max_tokens"] = max_tokens
            elif model.max_tokens is not None:
                request_params["max_tokens"] = model.max_tokens

            if top_p is not None:
                request_params["top_p"] = top_p

            if frequency_penalty is not None:
                request_params["frequency_penalty"] = frequency_penalty

            if functions:
                request_params["functions"] = functions

            if function_call:
                request_params["function_call"] = function_call

            if tools:
                request_params["tools"] = tools

            if tool_choice:
                request_params["tool_choice"] = tool_choice

            if stream:
                request_params["stream"] = True

            # 根据提供商类型构建请求
            api_url = config.api_endpoint or provider.api_base_url

            if provider.name == "openai":
                # OpenAI API
                if not api_url.endswith("/chat/completions"):
                    api_url = f"{api_url.rstrip('/')}/chat/completions"

                headers = {
                    "Content-Type": "application/json",
                    "Authorization": f"Bearer {config.api_key}",
                }

            elif provider.name == "azure":
                # Azure OpenAI API
                if "{resource_name}" in api_url:
                    # 替换占位符
                    config_json = config.config or {}
                    resource_name = config_json.get("resource_name")
                    deployment_id = config_json.get("deployment_id")
                    api_version = config_json.get("api_version", "2023-05-15")

                    if not resource_name or not deployment_id:
                        raise ValueError("Azure OpenAI 配置缺少 resource_name 或 deployment_id")

                    api_url = api_url.replace("{resource_name}", resource_name)
                    api_url = api_url.replace("{deployment_id}", deployment_id)

                    # 构建完整 URL
                    api_url = f"{api_url}?api-version={api_version}"

                headers = {
                    "Content-Type": "application/json",
                    "api-key": config.api_key,
                }

            elif provider.name == "anthropic":
                # Anthropic API
                if not api_url.endswith("/messages"):
                    api_url = f"{api_url.rstrip('/')}/messages"

                # Anthropic API 使用不同的参数格式
                if "messages" in request_params:
                    # 转换消息格式
                    anthropic_messages = []
                    system_message = None

                    for msg in request_params["messages"]:
                        if msg["role"] == "system":
                            system_message = msg["content"]
                        elif msg["role"] == "user":
                            anthropic_messages.append({
                                "role": "user",
                                "content": msg["content"],
                            })
                        elif msg["role"] == "assistant":
                            anthropic_messages.append({
                                "role": "assistant",
                                "content": msg["content"],
                            })

                    request_params["messages"] = anthropic_messages

                    if system_message:
                        request_params["system"] = system_message

                # 删除不支持的参数
                if "functions" in request_params:
                    del request_params["functions"]

                if "function_call" in request_params:
                    del request_params["function_call"]

                headers = {
                    "Content-Type": "application/json",
                    "x-api-key": config.api_key,
                    "anthropic-version": "2023-06-01",
                }

            elif provider.name in ["alibaba_bailian", "dashscope"]:
                # 阿里巴巴百炼 API (兼容OpenAI格式)
                if not api_url.endswith("/chat/completions"):
                    if "compatible-mode" not in api_url:
                        api_url = "https://dashscope.aliyuncs.com/compatible-mode/v1/chat/completions"
                    else:
                        api_url = f"{api_url.rstrip('/')}/chat/completions"

                headers = {
                    "Content-Type": "application/json",
                    "Authorization": f"Bearer {config.api_key}",
                }

            elif provider.name == "google":
                # Google Gemini API
                if "generativelanguage.googleapis.com" in api_url:
                    # Gemini API 格式
                    api_url = f"{api_url.rstrip('/')}/v1beta/models/{model.name}:generateContent?key={config.api_key}"
                    
                    # 转换为Gemini格式
                    gemini_contents = []
                    for msg in request_params["messages"]:
                        if msg["role"] == "system":
                            # Gemini没有system role，将其作为user消息处理
                            gemini_contents.append({
                                "role": "user",
                                "parts": [{"text": f"System: {msg['content']}"}]
                            })
                        elif msg["role"] == "user":
                            gemini_contents.append({
                                "role": "user", 
                                "parts": [{"text": msg["content"]}]
                            })
                        elif msg["role"] == "assistant":
                            gemini_contents.append({
                                "role": "model",
                                "parts": [{"text": msg["content"]}]
                            })
                    
                    request_params = {
                        "contents": gemini_contents,
                        "generationConfig": {}
                    }
                    
                    if temperature is not None:
                        request_params["generationConfig"]["temperature"] = temperature
                    if max_tokens is not None:
                        request_params["generationConfig"]["maxOutputTokens"] = max_tokens
                    if top_p is not None:
                        request_params["generationConfig"]["topP"] = top_p

                    headers = {
                        "Content-Type": "application/json",
                    }
                else:
                    # 使用OpenAI兼容格式
                    headers = {
                        "Content-Type": "application/json",
                        "Authorization": f"Bearer {config.api_key}",
                    }

            elif provider.name == "openrouter":
                # OpenRouter API (OpenAI兼容)
                if not api_url.endswith("/chat/completions"):
                    api_url = f"{api_url.rstrip('/')}/chat/completions"

                headers = {
                    "Content-Type": "application/json",
                    "Authorization": f"Bearer {config.api_key}",
                    "HTTP-Referer": "https://your-app.com",  # 可选，用于统计
                    "X-Title": "AI SaaS Platform",  # 可选，用于统计
                }

            else:
                raise ValueError(f"不支持的 AI 提供商: {provider.name}")

            # 添加代理
            proxy = None
            if config.proxy_url:
                proxy = config.proxy_url

            # 发送请求
            async with httpx.AsyncClient(proxies=proxy, timeout=120) as client:
                response = await client.post(
                    api_url,
                    json=request_params,
                    headers=headers,
                )

                # 检查响应状态
                response.raise_for_status()

                # 解析响应
                result = response.json()

            # 计算耗时
            duration_ms = int((time.time() - start_time) * 1000)

            # 解析 token 使用情况
            prompt_tokens = 0
            completion_tokens = 0
            total_tokens = 0

            if provider.name in ["openai", "azure", "alibaba_bailian", "dashscope", "openrouter"]:
                usage = result.get("usage", {})
                prompt_tokens = usage.get("prompt_tokens", 0)
                completion_tokens = usage.get("completion_tokens", 0)
                total_tokens = usage.get("total_tokens", 0)

            elif provider.name == "anthropic":
                usage = result.get("usage", {})
                prompt_tokens = usage.get("input_tokens", 0)
                completion_tokens = usage.get("output_tokens", 0)
                total_tokens = prompt_tokens + completion_tokens

            elif provider.name == "google":
                # Gemini API 的使用统计
                usage = result.get("usageMetadata", {})
                prompt_tokens = usage.get("promptTokenCount", 0)
                completion_tokens = usage.get("candidatesTokenCount", 0)
                total_tokens = usage.get("totalTokenCount", prompt_tokens + completion_tokens)
                
                # 转换Gemini响应为OpenAI格式
                if "candidates" in result and len(result["candidates"]) > 0:
                    candidate = result["candidates"][0]
                    content = ""
                    if "content" in candidate and "parts" in candidate["content"]:
                        content = "".join([part.get("text", "") for part in candidate["content"]["parts"]])
                    
                    result = {
                        "choices": [{
                            "message": {
                                "role": "assistant",
                                "content": content
                            },
                            "finish_reason": "stop",
                            "index": 0
                        }],
                        "usage": {
                            "prompt_tokens": prompt_tokens,
                            "completion_tokens": completion_tokens,
                            "total_tokens": total_tokens
                        },
                        "created": int(time.time()),
                        "model": model.name,
                        "object": "chat.completion"
                    }

            # 计算成本
            cost = 0.0
            if model.input_price_per_1k_tokens and model.output_price_per_1k_tokens:
                input_cost = (prompt_tokens / 1000) * model.input_price_per_1k_tokens
                output_cost = (completion_tokens / 1000) * model.output_price_per_1k_tokens
                cost = input_cost + output_cost

            # 记录使用情况（仅在非分享模式下记录）
            if not is_shared:
                usage_data = AIUsageCreate(
                    tenant_id=config.tenant_id or project.tenant_id,
                    project_id=project_id,
                    user_id=user_id,
                    config_id=config.id,
                    model_id=model.id,
                    request_type="chat",
                    prompt_tokens=prompt_tokens,
                    completion_tokens=completion_tokens,
                    total_tokens=total_tokens,
                    cost=cost,
                    duration_ms=duration_ms,
                    status="success",
                    request_metadata={
                        "provider": provider.name,
                        "model": model.name,
                        "temperature": request_params.get("temperature"),
                        "max_tokens": request_params.get("max_tokens"),
                        "functions": bool(functions),
                        "function_call": bool(function_call),
                        "stream": stream,
                        "message_count": len(messages),
                        "is_shared": is_shared,
                    }
                )

                await AIUsageService.create_usage(db, usage_data)

            return result

        except Exception as e:
            # 计算耗时
            duration_ms = int((time.time() - start_time) * 1000)

            # 记录错误
            logger.error(f"AI 聊天请求失败: {e}")

            # 如果已经获取到配置和模型，记录使用情况
            if not is_shared and 'config' in locals() and 'model' in locals():
                error_usage_data = AIUsageCreate(
                    tenant_id=config.tenant_id or project.tenant_id,
                    project_id=project_id,
                    user_id=user_id,
                    config_id=config.id if 'config' in locals() else None,
                    model_id=model.id if 'model' in locals() else None,
                    request_type="chat",
                    prompt_tokens=0,
                    completion_tokens=0,
                    total_tokens=0,
                    cost=0,
                    duration_ms=duration_ms,
                    status="failed",
                    error_message=str(e),
                    request_metadata={
                        "provider": provider.name if 'provider' in locals() else None,
                        "model": model.name if 'model' in locals() else None,
                        "message_count": len(messages) if 'messages' in locals() else 0,
                        "is_shared": is_shared,
                    }
                )

                await AIUsageService.create_usage(db, error_usage_data)

            raise ValueError(f"AI 聊天请求失败: {str(e)}")
