#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
AI 嵌入服务
"""

import logging
import uuid
import json
import time
import httpx
from typing import List, Optional, Dict, Any, Union
from sqlalchemy.ext.asyncio import AsyncSession

from models.ai import AIConfig, AIModel, AIProvider
from models.project import Project
from models.user import User
from services.ai import AIConfigService, AIUsageService
from schemas.ai import AIUsageCreate

logger = logging.getLogger(__name__)

class AIEmbeddingService:
    """AI 嵌入服务"""

    @staticmethod
    async def create_embedding(
        db: AsyncSession,
        project_id: uuid.UUID,
        user_id: uuid.UUID,
        input: Union[str, List[str]],
        model_id: Optional[uuid.UUID] = None,
        config_id: Optional[uuid.UUID] = None,
        dimensions: Optional[int] = None,
        user_data: Optional[Dict[str, Any]] = None,
    ) -> Dict[str, Any]:
        """
        创建嵌入
        """
        start_time = time.time()
        
        try:
            # 获取有效的 AI 配置
            if config_id:
                config = await AIConfigService.get_config(db, config_id)
                if not config or config.status != "active":
                    raise ValueError(f"AI 配置 ID '{config_id}' 不存在或未激活")
            else:
                config = await AIConfigService.get_effective_config(
                    db=db,
                    project_id=project_id,
                    model_id=model_id,
                    model_type="embedding",
                )
                
            if not config:
                raise ValueError("未找到有效的 AI 配置")
                
            # 获取模型信息
            model = await db.get(AIModel, config.model_id)
            if not model:
                raise ValueError(f"AI 模型 ID '{config.model_id}' 不存在")
                
            # 获取提供商信息
            provider = await db.get(AIProvider, config.provider_id)
            if not provider:
                raise ValueError(f"AI 提供商 ID '{config.provider_id}' 不存在")
                
            # 获取项目信息
            project = await db.get(Project, project_id)
            if not project:
                raise ValueError(f"项目 ID '{project_id}' 不存在")
                
            # 构建请求参数
            request_params = {
                "model": model.name,
                "input": input,
            }
            
            # 添加可选参数
            if dimensions:
                request_params["dimensions"] = dimensions
                
            if user_data:
                request_params["user"] = user_data
                
            # 根据提供商类型构建请求
            api_url = config.api_endpoint or provider.api_base_url
            
            if provider.name == "openai":
                # OpenAI API
                if not api_url.endswith("/embeddings"):
                    api_url = f"{api_url.rstrip('/')}/embeddings"
                    
                headers = {
                    "Content-Type": "application/json",
                    "Authorization": f"Bearer {config.api_key}",
                }
                
            elif provider.name == "azure":
                # Azure OpenAI API
                if "{resource_name}" in api_url:
                    # 替换占位符
                    config_json = config.config or {}
                    resource_name = config_json.get("resource_name")
                    deployment_id = config_json.get("deployment_id")
                    api_version = config_json.get("api_version", "2023-05-15")
                    
                    if not resource_name or not deployment_id:
                        raise ValueError("Azure OpenAI 配置缺少 resource_name 或 deployment_id")
                        
                    api_url = api_url.replace("{resource_name}", resource_name)
                    api_url = api_url.replace("{deployment_id}", deployment_id)
                    
                    # 构建完整 URL
                    api_url = f"{api_url}/embeddings?api-version={api_version}"
                    
                headers = {
                    "Content-Type": "application/json",
                    "api-key": config.api_key,
                }
                
            else:
                raise ValueError(f"不支持的 AI 提供商: {provider.name}")
                
            # 添加代理
            proxy = None
            if config.proxy_url:
                proxy = config.proxy_url
                
            # 发送请求
            async with httpx.AsyncClient(proxies=proxy) as client:
                response = await client.post(
                    api_url,
                    json=request_params,
                    headers=headers,
                    timeout=60,
                )
                
                # 检查响应状态
                response.raise_for_status()
                
                # 解析响应
                result = response.json()
                
            # 计算耗时
            duration_ms = int((time.time() - start_time) * 1000)
            
            # 解析 token 使用情况
            prompt_tokens = 0
            completion_tokens = 0
            total_tokens = 0
            
            if provider.name in ["openai", "azure"]:
                usage = result.get("usage", {})
                prompt_tokens = usage.get("prompt_tokens", 0)
                total_tokens = usage.get("total_tokens", 0)
                
            # 计算成本
            cost = 0.0
            if model.input_price_per_1k_tokens:
                cost = (total_tokens / 1000) * model.input_price_per_1k_tokens
                
            # 记录使用情况
            usage_data = AIUsageCreate(
                tenant_id=config.tenant_id or project.tenant_id,
                project_id=project_id,
                user_id=user_id,
                config_id=config.id,
                model_id=model.id,
                request_type="embedding",
                prompt_tokens=prompt_tokens,
                completion_tokens=completion_tokens,
                total_tokens=total_tokens,
                cost=cost,
                duration_ms=duration_ms,
                status="success",
                request_metadata={
                    "provider": provider.name,
                    "model": model.name,
                    "dimensions": dimensions,
                    "input_type": "string" if isinstance(input, str) else "array",
                    "input_length": len(input) if isinstance(input, list) else 1,
                }
            )
            
            await AIUsageService.create_usage(db, usage_data)
            
            return result
            
        except Exception as e:
            # 计算耗时
            duration_ms = int((time.time() - start_time) * 1000)
            
            # 记录错误
            logger.error(f"AI 嵌入请求失败: {e}")
            
            # 如果已经获取到配置和模型，记录使用情况
            if 'config' in locals() and 'model' in locals() and 'project' in locals():
                error_usage_data = AIUsageCreate(
                    tenant_id=config.tenant_id or project.tenant_id,
                    project_id=project_id,
                    user_id=user_id,
                    config_id=config.id if 'config' in locals() else None,
                    model_id=model.id if 'model' in locals() else None,
                    request_type="embedding",
                    prompt_tokens=0,
                    completion_tokens=0,
                    total_tokens=0,
                    cost=0,
                    duration_ms=duration_ms,
                    status="failed",
                    error_message=str(e),
                    request_metadata={
                        "provider": provider.name if 'provider' in locals() else None,
                        "model": model.name if 'model' in locals() else None,
                        "input_type": "string" if isinstance(input, str) else "array" if 'input' in locals() else None,
                        "input_length": len(input) if isinstance(input, list) and 'input' in locals() else 1 if 'input' in locals() else 0,
                    }
                )
                
                await AIUsageService.create_usage(db, error_usage_data)
                
            raise ValueError(f"AI 嵌入请求失败: {str(e)}")
