#!/usr/bin/env python
# -*- coding: utf-8 -*-

import logging
import uuid
from typing import List, Optional, Dict, Any, Tuple
from datetime import datetime, date
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from sqlalchemy import or_, and_, func, desc, asc, text, case, Float
from sqlalchemy.orm import joinedload

from models.sales_report import SalesReport, SalesReportItem
from models.store import Store
from models.sales_management import SalesChannel, PaymentMethod
from models.user import User
from models.product import Product
from schemas.sales_report import SalesReportCreate, SalesReportUpdate, SalesReportStatusUpdate

logger = logging.getLogger(__name__)

class SalesReportService:
    @staticmethod
    async def create_sales_report(
        db: AsyncSession,
        report_data: SalesReportCreate,
        project_id: uuid.UUID,
        user_id: uuid.UUID
    ) -> SalesReport:
        """创建销售上报"""
        # 初始化销售额
        items_total_sales = 0.0
        online_sales = report_data.online_sales or 0.0
        offline_sales = report_data.offline_sales or 0.0

        # 处理支付方式数据
        payment_methods = report_data.payment_methods or {}

        # 处理渠道销售数据
        channel_sales_data = {}

        if hasattr(report_data, 'channel_sales') and report_data.channel_sales:
            for channel in report_data.channel_sales:
                if hasattr(channel, 'channel_id') and hasattr(channel, 'amount'):
                    channel_id = str(channel.channel_id)
                    amount = float(channel.amount or 0)

                    # 更新渠道销售数据
                    channel_sales_data[channel_id] = amount

                    # 处理渠道支付方式
                    if hasattr(channel, 'payment_methods') and channel.payment_methods:
                        for pm in channel.payment_methods:
                            if isinstance(pm, dict) and 'method' in pm and 'amount' in pm:
                                # 处理字典格式的支付方式
                                payment_method = pm['method']
                                pm_amount = float(pm['amount'])
                            elif hasattr(pm, 'method') and hasattr(pm, 'amount'):
                                # 处理对象格式的支付方式
                                payment_method = pm.method
                                pm_amount = float(pm.amount)
                            else:
                                # 跳过无效的支付方式
                                continue

                            # 更新总的支付方式
                            if payment_method in payment_methods:
                                payment_methods[payment_method] += pm_amount
                            else:
                                payment_methods[payment_method] = pm_amount

        # 处理充值/售卡数据
        recharge_payment_methods = {}
        recharge_amount = 0.0
        card_sales_amount = 0.0
        recharge_count = 0
        card_sales_count = 0

        if hasattr(report_data, 'recharge_sales') and report_data.recharge_sales:
            for recharge in report_data.recharge_sales:
                # 计算充值/售卡总额和数量
                amount = float(recharge.amount or 0)
                count = int(recharge.count or 1)

                if recharge.type == 'recharge':
                    recharge_amount += amount
                    recharge_count += count
                elif recharge.type == 'card':
                    card_sales_amount += amount
                    card_sales_count += count

                # 处理支付方式
                if hasattr(recharge, 'payment_methods') and recharge.payment_methods:
                    for pm in recharge.payment_methods:
                        if isinstance(pm, dict) and 'method' in pm and 'amount' in pm:
                            # 处理字典格式的支付方式
                            payment_method = pm['method']
                            pm_amount = float(pm['amount'])
                        elif hasattr(pm, 'method') and hasattr(pm, 'amount'):
                            # 处理对象格式的支付方式
                            payment_method = pm.method
                            pm_amount = float(pm.amount)
                        else:
                            # 跳过无效的支付方式
                            continue

                        # 使用支付方式作为键
                        if payment_method in recharge_payment_methods:
                            recharge_payment_methods[payment_method] += pm_amount
                        else:
                            recharge_payment_methods[payment_method] = pm_amount

                        # 同时更新总的支付方式
                        if payment_method in payment_methods:
                            payment_methods[payment_method] += pm_amount
                        else:
                            payment_methods[payment_method] = pm_amount
                else:
                    # 如果没有支付方式，使用默认支付方式
                    default_method = "other"

                    if default_method in recharge_payment_methods:
                        recharge_payment_methods[default_method] += amount
                    else:
                        recharge_payment_methods[default_method] = amount

                    if default_method in payment_methods:
                        payment_methods[default_method] += amount
                    else:
                        payment_methods[default_method] = amount

        # 创建销售上报
        db_report = SalesReport(
            project_id=project_id,
            store_id=report_data.store_id,
            report_date=report_data.report_date,
            report_type=report_data.report_type,
            total_orders=report_data.total_orders,
            total_customers=report_data.total_customers,
            online_sales=online_sales,
            offline_sales=offline_sales,
            payment_methods=payment_methods,
            recharge_payment_methods=recharge_payment_methods,  # 添加充值/售卡支付方式
            recharge_amount=recharge_amount,                   # 添加充值总额
            card_sales_amount=card_sales_amount,               # 添加售卡总额
            recharge_count=recharge_count,                     # 添加充值笔数
            card_sales_count=card_sales_count,                 # 添加售卡数量
            product_categories=report_data.product_categories,
            hourly_sales=report_data.hourly_sales,
            channel_sales=channel_sales_data,                  # 添加渠道销售数据
            notes=report_data.notes,
            status="draft",
            created_by=user_id,
            updated_by=user_id
        )

        db.add(db_report)
        await db.flush()

        # 创建销售上报项
        for item_data in report_data.items:
            # 计算项目金额
            item_total = item_data.quantity * item_data.unit_price - item_data.discount_amount
            items_total_sales += item_total

            db_item = SalesReportItem(
                sales_report_id=db_report.id,
                product_id=item_data.product_id,
                product_name=item_data.product_name,
                product_code=item_data.product_code,
                product_category=item_data.product_category,
                product_unit=item_data.product_unit,
                quantity=item_data.quantity,
                unit_price=item_data.unit_price,
                discount_amount=item_data.discount_amount,
                total_amount=item_total,
                notes=item_data.notes
            )

            db.add(db_item)

        # 计算并更新总销售额：线上销售额 + 线下销售额 + 销售项总额
        # 注意：这里假设线上/线下销售额可能包含了销售项的金额，也可能是额外的销售额
        # 根据实际业务逻辑，可能需要调整计算方式
        total_sales = online_sales + offline_sales

        # 如果有销售项，且线上/线下销售额为0，则使用销售项总额作为总销售额
        if items_total_sales > 0 and total_sales == 0:
            total_sales = items_total_sales

        db_report.total_sales = total_sales

        await db.commit()
        await db.refresh(db_report)

        return db_report

    @staticmethod
    async def get_sales_reports(
        db: AsyncSession,
        project_id: uuid.UUID,
        skip: int = 0,
        limit: int = 100,
        store_id: Optional[uuid.UUID] = None,
        status: Optional[str] = None,
        report_type: Optional[str] = None,
        search: Optional[str] = None,
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None,
        sort_by: str = "report_date",
        sort_order: str = "desc"
    ) -> List[SalesReport]:
        """获取销售上报列表"""
        query = select(SalesReport).where(SalesReport.project_id == project_id)

        # 应用过滤条件
        if store_id:
            query = query.where(SalesReport.store_id == store_id)

        if status:
            query = query.where(SalesReport.status == status)

        if report_type:
            query = query.where(SalesReport.report_type == report_type)

        if search:
            search_term = f"%{search}%"
            query = query.where(
                or_(
                    SalesReport.notes.ilike(search_term)
                )
            )

        if start_date:
            query = query.where(SalesReport.report_date >= start_date)

        if end_date:
            query = query.where(SalesReport.report_date <= end_date)

        # 应用排序
        if sort_order.lower() == "asc":
            query = query.order_by(asc(getattr(SalesReport, sort_by)))
        else:
            query = query.order_by(desc(getattr(SalesReport, sort_by)))

        # 应用分页
        query = query.offset(skip).limit(limit)

        # 加载关联数据
        query = query.options(
            joinedload(SalesReport.store),
            joinedload(SalesReport.creator),
            joinedload(SalesReport.updater),
            joinedload(SalesReport.approver)
        )

        result = await db.execute(query)
        return result.scalars().all()

    @staticmethod
    async def count_sales_reports(
        db: AsyncSession,
        project_id: uuid.UUID,
        store_id: Optional[uuid.UUID] = None,
        status: Optional[str] = None,
        report_type: Optional[str] = None,
        search: Optional[str] = None,
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None
    ) -> int:
        """计算销售上报总数"""
        query = select(func.count(SalesReport.id)).where(SalesReport.project_id == project_id)

        # 应用过滤条件
        if store_id:
            query = query.where(SalesReport.store_id == store_id)

        if status:
            query = query.where(SalesReport.status == status)

        if report_type:
            query = query.where(SalesReport.report_type == report_type)

        if search:
            search_term = f"%{search}%"
            query = query.where(
                or_(
                    SalesReport.notes.ilike(search_term)
                )
            )

        if start_date:
            query = query.where(SalesReport.report_date >= start_date)

        if end_date:
            query = query.where(SalesReport.report_date <= end_date)

        result = await db.execute(query)
        return result.scalar_one()

    @staticmethod
    async def get_sales_report_by_id(
        db: AsyncSession,
        report_id: uuid.UUID
    ) -> Optional[SalesReport]:
        """根据ID获取销售上报"""
        query = select(SalesReport).where(SalesReport.id == report_id)
        query = query.options(
            joinedload(SalesReport.store),
            joinedload(SalesReport.creator),
            joinedload(SalesReport.updater),
            joinedload(SalesReport.approver),
            joinedload(SalesReport.items).joinedload(SalesReportItem.product)
        )

        result = await db.execute(query)
        # Call unique() on the result to handle collections from eager loading
        return result.unique().scalar_one_or_none()

    @staticmethod
    async def update_sales_report(
        db: AsyncSession,
        report_id: uuid.UUID,
        report_data: SalesReportUpdate,
        user_id: uuid.UUID
    ) -> Optional[SalesReport]:
        """更新销售上报"""
        query = select(SalesReport).where(SalesReport.id == report_id)
        result = await db.execute(query)
        db_report = result.scalar_one_or_none()

        if not db_report:
            return None

        # 检查状态
        if db_report.status != "draft":
            raise ValueError("只能修改草稿状态的销售上报")

        # 更新销售上报信息
        update_data = report_data.dict(exclude_unset=True)

        # 检查是否更新了线上或线下销售额
        online_sales_updated = 'online_sales' in update_data
        offline_sales_updated = 'offline_sales' in update_data
        total_sales_updated = 'total_sales' in update_data

        # 处理充值/售卡数据
        if hasattr(report_data, 'recharge_sales') and report_data.recharge_sales:
            # 获取现有的支付方式数据
            payment_methods = {}  # 重新初始化支付方式数据
            recharge_payment_methods = {}

            # 初始化充值/售卡总额和数量
            recharge_amount = 0.0
            card_sales_amount = 0.0
            recharge_count = 0
            card_sales_count = 0

            # 添加新的充值/售卡支付方式
            for recharge in report_data.recharge_sales:
                # 计算充值/售卡总额和数量
                amount = float(recharge.amount or 0)
                count = int(recharge.count or 1)

                if recharge.type == 'recharge':
                    recharge_amount += amount
                    recharge_count += count
                elif recharge.type == 'card':
                    card_sales_amount += amount
                    card_sales_count += count

                # 处理支付方式
                if hasattr(recharge, 'payment_methods') and recharge.payment_methods:
                    for pm in recharge.payment_methods:
                        if isinstance(pm, dict) and 'method' in pm and 'amount' in pm:
                            # 处理字典格式的支付方式
                            payment_method = pm['method']
                            pm_amount = float(pm['amount'])
                        elif hasattr(pm, 'method') and hasattr(pm, 'amount'):
                            # 处理对象格式的支付方式
                            payment_method = pm.method
                            pm_amount = float(pm.amount)
                        else:
                            # 跳过无效的支付方式
                            continue

                        # 使用支付方式作为键
                        if payment_method in recharge_payment_methods:
                            recharge_payment_methods[payment_method] += pm_amount
                        else:
                            recharge_payment_methods[payment_method] = pm_amount

                        # 同时更新总的支付方式
                        if payment_method in payment_methods:
                            payment_methods[payment_method] += pm_amount
                        else:
                            payment_methods[payment_method] = pm_amount
                else:
                    # 如果没有支付方式，使用默认支付方式
                    default_method = "other"

                    if default_method in recharge_payment_methods:
                        recharge_payment_methods[default_method] += amount
                    else:
                        recharge_payment_methods[default_method] = amount

                    if default_method in payment_methods:
                        payment_methods[default_method] += amount
                    else:
                        payment_methods[default_method] = amount

            # 更新充值/售卡总额和数量
            update_data['recharge_amount'] = recharge_amount
            update_data['card_sales_amount'] = card_sales_amount
            update_data['recharge_count'] = recharge_count
            update_data['card_sales_count'] = card_sales_count

            # 处理渠道销售数据
            channel_sales_data = {}

            if hasattr(report_data, 'channel_sales_list') and report_data.channel_sales_list:
                for channel in report_data.channel_sales_list:
                    if hasattr(channel, 'channel_id') and hasattr(channel, 'amount'):
                        channel_id = str(channel.channel_id)
                        amount = float(channel.amount or 0)

                        # 更新渠道销售数据
                        channel_sales_data[channel_id] = amount

                        # 处理渠道支付方式
                        if hasattr(channel, 'payment_methods') and channel.payment_methods:
                            for pm in channel.payment_methods:
                                if isinstance(pm, dict) and 'method' in pm and 'amount' in pm:
                                    # 处理字典格式的支付方式
                                    payment_method = pm['method']
                                    pm_amount = float(pm['amount'])
                                elif hasattr(pm, 'method') and hasattr(pm, 'amount'):
                                    # 处理对象格式的支付方式
                                    payment_method = pm.method
                                    pm_amount = float(pm.amount)
                                else:
                                    # 跳过无效的支付方式
                                    continue

                                # 更新总的支付方式
                                if payment_method in payment_methods:
                                    payment_methods[payment_method] += pm_amount
                                else:
                                    payment_methods[payment_method] = pm_amount

            # 更新渠道销售数据
            update_data['channel_sales'] = channel_sales_data

            # 更新支付方式数据
            update_data['payment_methods'] = payment_methods
            update_data['recharge_payment_methods'] = recharge_payment_methods

        # 应用更新
        for key, value in update_data.items():
            setattr(db_report, key, value)

        # 如果更新了线上或线下销售额，但没有明确更新总销售额，则重新计算总销售额
        if (online_sales_updated or offline_sales_updated) and not total_sales_updated:
            # 获取当前的线上和线下销售额
            online_sales = db_report.online_sales or 0.0
            offline_sales = db_report.offline_sales or 0.0

            # 计算销售项总额
            items_total = 0.0
            query = select(func.sum(SalesReportItem.total_amount)).where(
                SalesReportItem.sales_report_id == db_report.id
            )
            result = await db.execute(query)
            items_total = result.scalar_one_or_none() or 0.0

            # 计算总销售额
            total_sales = online_sales + offline_sales

            # 如果有销售项，且线上/线下销售额为0，则使用销售项总额作为总销售额
            if items_total > 0 and total_sales == 0:
                total_sales = items_total

            db_report.total_sales = total_sales

        db_report.updated_by = user_id
        db_report.updated_at = datetime.now(datetime.timezone.utc)

        await db.commit()
        await db.refresh(db_report)

        return db_report

    @staticmethod
    async def update_sales_report_status(
        db: AsyncSession,
        report_id: uuid.UUID,
        status_data: SalesReportStatusUpdate,
        user_id: uuid.UUID
    ) -> Optional[SalesReport]:
        """更新销售上报状态"""
        query = select(SalesReport).where(SalesReport.id == report_id)
        result = await db.execute(query)
        db_report = result.scalar_one_or_none()

        if not db_report:
            return None

        # 检查状态变更是否有效
        new_status = status_data.status
        current_status = db_report.status

        valid_transitions = {
            "draft": ["submitted", "draft"],
            "submitted": ["approved", "rejected", "draft"],
            "approved": ["approved"],
            "rejected": ["draft", "rejected"]
        }

        if new_status not in valid_transitions.get(current_status, []):
            raise ValueError(f"无法从 {current_status} 状态变更为 {new_status} 状态")

        # 更新状态
        db_report.status = new_status

        if new_status == "approved":
            db_report.approved_by = user_id
            db_report.approved_at = datetime.now(datetime.timezone.utc)
        elif new_status == "rejected":
            db_report.reject_reason = status_data.reject_reason

        db_report.updated_by = user_id
        db_report.updated_at = datetime.now(datetime.timezone.utc)

        await db.commit()
        await db.refresh(db_report)

        return db_report

    @staticmethod
    async def delete_sales_report(
        db: AsyncSession,
        report_id: uuid.UUID
    ) -> bool:
        """删除销售上报"""
        query = select(SalesReport).where(SalesReport.id == report_id)
        result = await db.execute(query)
        db_report = result.scalar_one_or_none()

        if not db_report:
            return False

        # 检查状态
        if db_report.status != "draft":
            raise ValueError("只能删除草稿状态的销售上报")

        await db.delete(db_report)
        await db.commit()

        return True

    @staticmethod
    async def add_sales_report_item(
        db: AsyncSession,
        report_id: uuid.UUID,
        item_data: Dict[str, Any],
        user_id: uuid.UUID
    ) -> Tuple[SalesReport, SalesReportItem]:
        """添加销售上报项"""
        # 获取销售上报
        query = select(SalesReport).where(SalesReport.id == report_id)
        result = await db.execute(query)
        db_report = result.scalar_one_or_none()

        if not db_report:
            return None, None

        # 检查状态
        if db_report.status != "draft":
            raise ValueError("只能修改草稿状态的销售上报")

        # 计算项目金额
        quantity = item_data.get("quantity", 0)
        unit_price = item_data.get("unit_price", 0)
        discount_amount = item_data.get("discount_amount", 0)

        item_total = quantity * unit_price - discount_amount

        # 创建销售上报项
        db_item = SalesReportItem(
            sales_report_id=report_id,
            product_id=item_data.get("product_id"),
            product_name=item_data.get("product_name"),
            product_code=item_data.get("product_code"),
            product_category=item_data.get("product_category"),
            product_unit=item_data.get("product_unit"),
            quantity=quantity,
            unit_price=unit_price,
            discount_amount=discount_amount,
            total_amount=item_total,
            notes=item_data.get("notes")
        )

        db.add(db_item)
        await db.flush()

        # 更新销售总额
        db_report.total_sales += item_total

        db_report.updated_by = user_id
        db_report.updated_at = datetime.now(datetime.timezone.utc)

        await db.commit()
        await db.refresh(db_report)
        await db.refresh(db_item)

        return db_report, db_item

    @staticmethod
    async def update_sales_report_item(
        db: AsyncSession,
        item_id: uuid.UUID,
        item_data: Dict[str, Any],
        user_id: uuid.UUID
    ) -> Tuple[SalesReport, SalesReportItem]:
        """更新销售上报项"""
        # 获取销售上报项
        query = select(SalesReportItem).where(SalesReportItem.id == item_id)
        result = await db.execute(query)
        db_item = result.scalar_one_or_none()

        if not db_item:
            return None, None

        # 获取销售上报
        query = select(SalesReport).where(SalesReport.id == db_item.sales_report_id)
        result = await db.execute(query)
        db_report = result.scalar_one_or_none()

        if not db_report:
            return None, None

        # 检查状态
        if db_report.status != "draft":
            raise ValueError("只能修改草稿状态的销售上报")

        # 计算原项目金额
        old_total = db_item.total_amount

        # 更新销售上报项
        for key, value in item_data.items():
            setattr(db_item, key, value)

        # 计算新项目金额
        quantity = db_item.quantity
        unit_price = db_item.unit_price
        discount_amount = db_item.discount_amount

        new_total = quantity * unit_price - discount_amount
        db_item.total_amount = new_total

        # 更新销售总额
        db_report.total_sales = db_report.total_sales - old_total + new_total

        db_report.updated_by = user_id
        db_report.updated_at = datetime.now(datetime.timezone.utc)

        await db.commit()
        await db.refresh(db_report)
        await db.refresh(db_item)

        return db_report, db_item

    @staticmethod
    async def delete_sales_report_item(
        db: AsyncSession,
        item_id: uuid.UUID,
        user_id: uuid.UUID
    ) -> Optional[SalesReport]:
        """删除销售上报项"""
        # 获取销售上报项
        query = select(SalesReportItem).where(SalesReportItem.id == item_id)
        result = await db.execute(query)
        db_item = result.scalar_one_or_none()

        if not db_item:
            return None

        # 获取销售上报
        query = select(SalesReport).where(SalesReport.id == db_item.sales_report_id)
        result = await db.execute(query)
        db_report = result.scalar_one_or_none()

        if not db_report:
            return None

        # 检查状态
        if db_report.status != "draft":
            raise ValueError("只能修改草稿状态的销售上报")

        # 更新销售总额
        db_report.total_sales -= db_item.total_amount

        db_report.updated_by = user_id
        db_report.updated_at = datetime.now(datetime.timezone.utc)

        # 删除销售上报项
        await db.delete(db_item)

        await db.commit()
        await db.refresh(db_report)

        return db_report

    @staticmethod
    async def get_sales_report_with_details(
        db: AsyncSession,
        report_id: uuid.UUID
    ) -> Optional[Dict[str, Any]]:
        """获取销售上报详情，包括关联信息"""
        db_report = await SalesReportService.get_sales_report_by_id(db, report_id)

        if not db_report:
            return None

        # 合并销售和充值/售卡的支付方式数据
        combined_payment_methods = {}

        # 获取支付方式列表
        payment_methods_query = select(PaymentMethod).where(PaymentMethod.project_id == db_report.project_id)
        result = await db.execute(payment_methods_query)
        payment_methods_list = result.scalars().all()

        # 创建支付方式映射
        payment_method_map = {}
        for method in payment_methods_list:
            key = method.code or method.name
            payment_method_map[key] = method.name

        # 处理销售支付方式
        if db_report.payment_methods:
            # 计算总销售额
            total_sales_amount = float(db_report.total_sales or 0)

            # 如果有支付方式数据，计算每个支付方式占总销售额的比例
            total_payment_amount = sum(float(amount) for amount in db_report.payment_methods.values())

            for method_name, amount in db_report.payment_methods.items():
                # 提取基本支付方式名称
                base_method_name = method_name
                # 尝试从支付方式列表中获取名称
                display_name = payment_method_map.get(base_method_name, base_method_name)

                # 计算该支付方式的销售金额
                payment_amount = float(amount)

                # 如果总支付金额与总销售额不一致，按比例分配总销售额
                if total_payment_amount > 0 and total_sales_amount > 0 and total_payment_amount != total_sales_amount:
                    payment_ratio = payment_amount / total_payment_amount
                    sales_amount = total_sales_amount * payment_ratio
                else:
                    sales_amount = payment_amount

                if base_method_name not in combined_payment_methods:
                    combined_payment_methods[base_method_name] = {
                        "method_id": base_method_name,
                        "method_name": display_name,
                        "sales_amount": sales_amount,
                        "recharge_amount": 0.0,
                        "total_amount": sales_amount
                    }
                else:
                    combined_payment_methods[base_method_name]["sales_amount"] += sales_amount
                    combined_payment_methods[base_method_name]["total_amount"] += sales_amount

        # 处理充值/售卡数据，确保前端期望的格式
        recharge_sales_data = []

        # 从数据库中获取充值/售卡总额和数量
        recharge_amount = float(db_report.recharge_amount or 0)
        card_sales_amount = float(db_report.card_sales_amount or 0)
        recharge_count = int(db_report.recharge_count or 0)
        card_sales_count = int(db_report.card_sales_count or 0)

        # 如果没有充值/售卡数据，但有支付方式数据，尝试从支付方式中提取
        if (recharge_amount == 0 and card_sales_amount == 0) and db_report.recharge_payment_methods:
            # 计算总金额
            total_recharge_amount = sum(float(amount) for amount in db_report.recharge_payment_methods.values())
            if total_recharge_amount > 0:
                # 假设这是会员充值
                recharge_amount = total_recharge_amount
                recharge_count = 1  # 至少有一笔

                # 更新数据库中的值
                db_report.recharge_amount = recharge_amount
                db_report.recharge_count = recharge_count
                await db.commit()
                await db.refresh(db_report)

        # 如果有充值金额，添加充值数据
        if recharge_amount > 0:
            payment_methods_list = []
            # 如果有充值支付方式数据，添加到充值数据中
            if db_report.recharge_payment_methods:
                for method, amount in db_report.recharge_payment_methods.items():
                    payment_methods_list.append({
                        "method": method,
                        "amount": float(amount)
                    })

            recharge_sales_data.append({
                "type": "recharge",
                "amount": recharge_amount,
                "count": recharge_count,
                "description": "会员充值",
                "payment_methods": payment_methods_list
            })

        # 如果有售卡金额，添加售卡数据
        if card_sales_amount > 0:
            recharge_sales_data.append({
                "type": "card",
                "amount": card_sales_amount,
                "count": card_sales_count,
                "description": "储值卡销售"
            })

        # 使用充值/售卡支付方式字段
        if db_report.recharge_payment_methods:
            # 处理每个支付方式
            for method_name, amount in db_report.recharge_payment_methods.items():
                amount_float = float(amount)

                # 直接使用支付方式名称
                base_method_name = method_name

                # 尝试从支付方式列表中获取显示名称
                display_name = payment_method_map.get(base_method_name, base_method_name)

                # 合并到支付方式统计
                if base_method_name not in combined_payment_methods:
                    combined_payment_methods[base_method_name] = {
                        "method_id": base_method_name,
                        "method_name": display_name,
                        "sales_amount": 0.0,
                        "recharge_amount": amount_float,
                        "total_amount": amount_float
                    }
                else:
                    combined_payment_methods[base_method_name]["recharge_amount"] += amount_float
                    combined_payment_methods[base_method_name]["total_amount"] += amount_float

        # 如果没有充值/售卡支付方式字段，则使用数据库中的充值/售卡字段
        if not db_report.recharge_payment_methods:
            recharge_amount = float(db_report.recharge_amount or 0)
            card_sales_amount = float(db_report.card_sales_amount or 0)
            recharge_count = int(db_report.recharge_count or 0)
            card_sales_count = int(db_report.card_sales_count or 0)

        # 将合并后的支付方式数据转换为列表
        combined_payment_methods_list = list(combined_payment_methods.values())

        # 获取渠道列表，用于丰富渠道销售数据的显示
        channels_query = select(SalesChannel).where(SalesChannel.project_id == db_report.project_id)
        result = await db.execute(channels_query)
        channels = result.scalars().all()

        # 创建渠道ID到名称的映射
        channel_map = {str(channel.id): channel.name for channel in channels}

        # 构建响应数据
        report_data = {
            "id": db_report.id,
            "project_id": db_report.project_id,
            "store_id": db_report.store_id,
            "report_date": db_report.report_date,
            "report_type": db_report.report_type,
            "total_sales": db_report.total_sales,
            "total_orders": db_report.total_orders,
            "total_customers": db_report.total_customers,
            "online_sales": db_report.online_sales,
            "offline_sales": db_report.offline_sales,
            "raw_payment_methods": db_report.payment_methods,  # 原始支付方式数据
            "payment_methods": combined_payment_methods_list,  # 使用合并后的支付方式数据
            "recharge_sales": recharge_sales_data,    # 添加充值/售卡数据
            "recharge_amount": recharge_amount,       # 添加充值总额
            "card_sales_amount": card_sales_amount,   # 添加售卡总额
            "recharge_count": recharge_count,         # 添加充值笔数
            "card_sales_count": card_sales_count,     # 添加售卡数量
            "product_categories": db_report.product_categories,
            "hourly_sales": db_report.hourly_sales,
            "channel_sales": db_report.channel_sales,  # 保持原始渠道销售数据格式
            "status": db_report.status,
            "notes": db_report.notes,
            "reject_reason": db_report.reject_reason,
            "created_by": db_report.created_by,
            "updated_by": db_report.updated_by,
            "approved_by": db_report.approved_by,
            "approved_at": db_report.approved_at,
            "created_at": db_report.created_at,
            "updated_at": db_report.updated_at,

            # 关联信息
            "store_name": db_report.store.name if db_report.store else None,
            "creator_name": db_report.creator.full_name or db_report.creator.username if db_report.creator else None,
            "updater_name": db_report.updater.full_name or db_report.updater.username if db_report.updater else None,
            "approver_name": db_report.approver.full_name or db_report.approver.username if db_report.approver else None,

            # 销售上报项
            "items": []
        }

        # 添加销售上报项
        for item in db_report.items:
            item_data = {
                "id": item.id,
                "sales_report_id": item.sales_report_id,
                "product_id": item.product_id,
                "product_name": item.product_name,
                "product_code": item.product_code,
                "product_category": item.product_category,
                "product_unit": item.product_unit,
                "quantity": item.quantity,
                "unit_price": item.unit_price,
                "discount_amount": item.discount_amount,
                "total_amount": item.total_amount,
                "notes": item.notes,
                "created_at": item.created_at,
                "updated_at": item.updated_at
            }

            report_data["items"].append(item_data)

        return report_data

    @staticmethod
    async def get_sales_statistics(
        db: AsyncSession,
        project_id: uuid.UUID,
        start_date: datetime,
        end_date: datetime,
        store_id: Optional[uuid.UUID] = None,
        channel_id: Optional[uuid.UUID] = None,
        group_by: str = "month"
    ) -> List[Dict[str, Any]]:
        """获取销售统计数据"""
        # 构建基础查询
        # 使用CASE WHEN表达式来处理total_sales为0但online_sales或offline_sales不为0的情况
        # 在SQLAlchemy 2.0中，case()函数的用法发生了变化，不再接受列表作为whens参数
        total_sales_expr = func.sum(
            case(
                # 当total_sales为0且(online_sales或offline_sales不为0)时，使用online_sales+offline_sales
                (and_(
                    SalesReport.total_sales == 0,
                    or_(
                        SalesReport.online_sales > 0,
                        SalesReport.offline_sales > 0
                    )
                ), SalesReport.online_sales + SalesReport.offline_sales),
                # 否则使用total_sales
                else_=SalesReport.total_sales
            )
        ).label("total_sales")

        # 计算充值售卡总额 - 使用数据库中的字段
        recharge_total_expr = func.sum(
            func.coalesce(SalesReport.recharge_amount, 0) +
            func.coalesce(SalesReport.card_sales_amount, 0)
        ).label("recharge_total")

        query = select(
            func.date_trunc(group_by, SalesReport.report_date).label(group_by),
            total_sales_expr,
            func.sum(SalesReport.online_sales).label("online_sales"),
            func.sum(SalesReport.offline_sales).label("offline_sales"),
            func.sum(SalesReport.total_orders).label("total_orders"),
            func.sum(SalesReport.total_customers).label("total_customers"),
            recharge_total_expr
        ).where(
            SalesReport.project_id == project_id,
            SalesReport.report_date >= start_date,
            SalesReport.report_date <= end_date
        )

        # 应用过滤条件
        if store_id:
            query = query.where(SalesReport.store_id == store_id)

        # 按渠道过滤（需要根据实际数据结构调整）
        if channel_id:
            # 这里假设渠道ID存储在channel_sales字段中
            # 实际实现可能需要根据数据结构调整
            pass

        # 分组和排序
        query = query.group_by(text(group_by)).order_by(text(group_by))

        result = await db.execute(query)
        statistics = result.fetchall()

        # 处理结果
        statistics_data = []
        for row in statistics:
            online_sales = float(row[2]) if row[2] else 0.0
            offline_sales = float(row[3]) if row[3] else 0.0

            # 计算总销售额，如果total_sales为0但online_sales或offline_sales不为0，则使用它们的总和
            total_sales = float(row[1]) if row[1] else 0.0
            if total_sales == 0 and (online_sales > 0 or offline_sales > 0):
                total_sales = online_sales + offline_sales

            # 获取报告日期
            report_date = row[0]
            formatted_date = report_date.strftime("%Y-%m") if group_by == "month" else report_date.strftime("%Y-%m-%d")

            # 从查询结果中获取充值售卡总额
            recharge_total = float(row[6]) if row[6] else 0.0

            data = {
                group_by: formatted_date,
                "total_sales": total_sales,
                "online_sales": online_sales,
                "offline_sales": offline_sales,
                "recharge_total": recharge_total,  # 添加充值售卡总额
                "total_orders": int(row[4]) if row[4] else 0,
                "total_customers": int(row[5]) if row[5] else 0
            }
            statistics_data.append(data)

        # 获取按渠道统计数据
        channel_stats = await SalesReportService.get_channel_statistics(
            db=db,
            project_id=project_id,
            start_date=start_date,
            end_date=end_date,
            store_id=store_id
        )

        # 获取按支付方式统计数据
        payment_stats = await SalesReportService.get_payment_statistics(
            db=db,
            project_id=project_id,
            start_date=start_date,
            end_date=end_date,
            store_id=store_id
        )

        # 合并数据
        for data in statistics_data:
            data["channel_stats"] = channel_stats
            data["payment_stats"] = payment_stats

        return statistics_data

    @staticmethod
    async def get_channel_statistics(
        db: AsyncSession,
        project_id: uuid.UUID,
        start_date: datetime,
        end_date: datetime,
        store_id: Optional[uuid.UUID] = None
    ) -> List[Dict[str, Any]]:
        """获取按渠道统计数据"""
        # 获取渠道列表
        channels_query = select(SalesChannel).where(SalesChannel.project_id == project_id)
        result = await db.execute(channels_query)
        channels = result.scalars().all()

        # 初始化渠道统计数据
        channel_stats = {}

        # 如果有渠道数据，初始化统计
        if channels:
            for channel in channels:
                channel_id = str(channel.id)
                channel_stats[channel_id] = {
                    "channel_id": channel_id,
                    "channel_name": channel.name,
                    "amount": 0
                }
        else:
            # 默认渠道
            default_channels = {
                "online": "线上",
                "offline": "线下",
                "wechat": "微信小程序",
                "app": "APP",
                "other": "其他"
            }
            for code, name in default_channels.items():
                channel_stats[code] = {
                    "channel_id": code,
                    "channel_name": name,
                    "amount": 0
                }

        # 添加线上线下渠道（如果不存在）
        if not any(c["channel_name"].lower() == "线上" or "online" in c["channel_name"].lower() for c in channel_stats.values()):
            channel_stats["online"] = {
                "channel_id": "online",
                "channel_name": "线上",
                "amount": 0
            }

        if not any(c["channel_name"].lower() == "线下" or "offline" in c["channel_name"].lower() for c in channel_stats.values()):
            channel_stats["offline"] = {
                "channel_id": "offline",
                "channel_name": "线下",
                "amount": 0
            }

        # 查询线上销售额
        online_query = select(func.sum(SalesReport.online_sales)).where(
            SalesReport.project_id == project_id,
            SalesReport.report_date >= start_date,
            SalesReport.report_date <= end_date
        )

        if store_id:
            online_query = online_query.where(SalesReport.store_id == store_id)

        result = await db.execute(online_query)
        online_amount = float(result.scalar_one_or_none() or 0)

        # 查询线下销售额
        offline_query = select(func.sum(SalesReport.offline_sales)).where(
            SalesReport.project_id == project_id,
            SalesReport.report_date >= start_date,
            SalesReport.report_date <= end_date
        )

        if store_id:
            offline_query = offline_query.where(SalesReport.store_id == store_id)

        result = await db.execute(offline_query)
        offline_amount = float(result.scalar_one_or_none() or 0)

        # 更新线上线下销售额
        for channel in channel_stats.values():
            if channel["channel_name"].lower() == "线上" or "online" in channel["channel_name"].lower():
                channel["amount"] = online_amount
            elif channel["channel_name"].lower() == "线下" or "offline" in channel["channel_name"].lower():
                channel["amount"] = offline_amount

        # 转换为列表并返回
        return list(channel_stats.values())

    @staticmethod
    async def get_payment_statistics(
        db: AsyncSession,
        project_id: uuid.UUID,
        start_date: datetime,
        end_date: datetime,
        store_id: Optional[uuid.UUID] = None
    ) -> List[Dict[str, Any]]:
        """获取按支付方式统计数据"""
        # 获取支付方式列表
        payment_methods_query = select(PaymentMethod).where(PaymentMethod.project_id == project_id)
        result = await db.execute(payment_methods_query)
        payment_methods = result.scalars().all()

        # 初始化支付方式统计数据
        payment_stats = {}

        # 如果有支付方式数据，初始化统计
        if payment_methods:
            for method in payment_methods:
                method_code = method.code or method.name
                payment_stats[method_code] = {
                    "method": method_code,
                    "method_name": method.name,
                    "amount": 0
                }
        else:
            # 默认支付方式
            default_methods = {
                "cash": "现金",
                "wechat": "微信支付",
                "alipay": "支付宝",
                "card": "刷卡",
                "member": "会员余额",
                "gift_card": "礼品卡",
                "other": "其他"
            }
            for code, name in default_methods.items():
                payment_stats[code] = {
                    "method": code,
                    "method_name": name,
                    "amount": 0
                }

        # 查询所有销售上报
        query = select(SalesReport).where(
            SalesReport.project_id == project_id,
            SalesReport.report_date >= start_date,
            SalesReport.report_date <= end_date
        )

        if store_id:
            query = query.where(SalesReport.store_id == store_id)

        result = await db.execute(query)
        reports = result.scalars().all()

        # 处理每个销售上报的支付方式数据
        for report in reports:
            # 处理销售支付方式
            if report.payment_methods:
                for method, amount in report.payment_methods.items():
                    if method not in payment_stats:
                        # 如果支付方式不在列表中，添加它
                        payment_stats[method] = {
                            "method": method,
                            "method_name": method,  # 使用方法代码作为名称
                            "amount": 0
                        }

                    # 累加金额
                    payment_stats[method]["amount"] += float(amount)

            # 处理充值/售卡支付方式
            if report.recharge_payment_methods:
                for method, amount in report.recharge_payment_methods.items():
                    if method not in payment_stats:
                        # 如果支付方式不在列表中，添加它
                        payment_stats[method] = {
                            "method": method,
                            "method_name": method,  # 使用方法代码作为名称
                            "amount": 0
                        }

                    # 累加金额
                    payment_stats[method]["amount"] += float(amount)

        # 转换为列表并返回
        return list(payment_stats.values())
