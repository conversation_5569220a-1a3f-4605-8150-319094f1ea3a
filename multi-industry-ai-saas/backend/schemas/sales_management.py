from typing import Optional, Dict, Any, List
from pydantic import BaseModel, Field
import uuid
from datetime import datetime

# 销售渠道模型
class SalesChannelBase(BaseModel):
    name: str
    code: Optional[str] = None
    type: str = "offline"  # online, offline, other
    description: Optional[str] = None
    is_active: bool = True
    config: Optional[Dict[str, Any]] = None


class SalesChannelCreate(SalesChannelBase):
    pass


class SalesChannelUpdate(BaseModel):
    name: Optional[str] = None
    code: Optional[str] = None
    type: Optional[str] = None
    description: Optional[str] = None
    is_active: Optional[bool] = None
    config: Optional[Dict[str, Any]] = None


class SalesChannelResponse(SalesChannelBase):
    id: uuid.UUID
    project_id: uuid.UUID
    created_at: datetime
    updated_at: datetime

    class Config:
        model_config = {"from_attributes": True}


# 支付方式模型
class PaymentMethodBase(BaseModel):
    name: str
    code: Optional[str] = None
    icon: Optional[str] = None
    description: Optional[str] = None
    is_active: bool = True
    is_default: bool = False
    sort_order: float = 0
    config: Optional[Dict[str, Any]] = None


class PaymentMethodCreate(PaymentMethodBase):
    pass


class PaymentMethodUpdate(BaseModel):
    name: Optional[str] = None
    code: Optional[str] = None
    icon: Optional[str] = None
    description: Optional[str] = None
    is_active: Optional[bool] = None
    is_default: Optional[bool] = None
    sort_order: Optional[float] = None
    config: Optional[Dict[str, Any]] = None


class PaymentMethodResponse(PaymentMethodBase):
    id: uuid.UUID
    project_id: uuid.UUID
    created_at: datetime
    updated_at: datetime

    class Config:
        model_config = {"from_attributes": True}


# 列表响应模型
class SalesChannelListResponse(BaseModel):
    items: List[SalesChannelResponse]
    total: int


class PaymentMethodListResponse(BaseModel):
    items: List[PaymentMethodResponse]
    total: int
