#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
AI 嵌入 Schema
"""

from typing import Optional, List, Dict, Any, Union
from datetime import datetime
import uuid
from pydantic import BaseModel, Field, validator

class AIEmbeddingRequest(BaseModel):
    """AI 嵌入请求 Schema"""
    input: Union[str, List[str]] = Field(..., description="输入文本，可以是单个字符串或字符串列表")
    model_id: Optional[uuid.UUID] = Field(None, description="模型 ID，如果不指定则使用默认模型")
    config_id: Optional[uuid.UUID] = Field(None, description="配置 ID，如果不指定则使用默认配置")
    dimensions: Optional[int] = Field(None, gt=0, description="嵌入维度，仅部分模型支持")
    user_data: Optional[Dict[str, Any]] = Field(None, description="用户数据，用于日志和监控")

class AIEmbeddingData(BaseModel):
    """AI 嵌入数据 Schema"""
    index: int = Field(..., description="嵌入索引")
    object: str = Field(..., description="对象类型")
    embedding: List[float] = Field(..., description="嵌入向量")

class AIEmbeddingUsage(BaseModel):
    """AI 嵌入使用情况 Schema"""
    prompt_tokens: int = Field(..., description="提示 token 数")
    total_tokens: int = Field(..., description="总 token 数")

class AIEmbeddingResponse(BaseModel):
    """AI 嵌入响应 Schema"""
    object: str = Field(..., description="对象类型")
    data: List[AIEmbeddingData] = Field(..., description="嵌入数据列表")
    model: str = Field(..., description="模型名称")
    usage: Optional[AIEmbeddingUsage] = Field(None, description="使用情况")

class AIEmbeddingErrorResponse(BaseModel):
    """AI 嵌入错误响应 Schema"""
    success: bool = Field(False, description="是否成功")
    message: str = Field(..., description="错误信息")
    error: Optional[Dict[str, Any]] = Field(None, description="错误详情")
