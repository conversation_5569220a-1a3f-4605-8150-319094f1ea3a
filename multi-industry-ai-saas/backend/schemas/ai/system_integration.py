#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
系统 AI 整合设置 Schema
"""

from typing import Optional
from pydantic import BaseModel, Field
import uuid

class SystemAIIntegrationBase(BaseModel):
    """系统AI整合设置基础Schema"""
    default_chat_model_id: Optional[uuid.UUID] = Field(None, description="默认聊天模型ID")
    default_vision_model_id: Optional[uuid.UUID] = Field(None, description="默认视觉模型ID") 
    default_audio_model_id: Optional[uuid.UUID] = Field(None, description="默认语音模型ID")
    default_embedding_model_id: Optional[uuid.UUID] = Field(None, description="默认嵌入模型ID")
    
    # 模型配置参数
    chat_temperature: float = Field(0.7, description="聊天模型温度参数", ge=0, le=2)
    chat_max_tokens: Optional[int] = Field(None, description="聊天模型最大token数", gt=0)
    vision_temperature: float = Field(0.7, description="视觉模型温度参数", ge=0, le=2)
    vision_max_tokens: Optional[int] = Field(None, description="视觉模型最大token数", gt=0)
    
    # 服务可用性控制
    chat_enabled: bool = Field(True, description="启用聊天功能")
    vision_enabled: bool = Field(True, description="启用视觉功能")
    audio_enabled: bool = Field(True, description="启用语音功能")
    embedding_enabled: bool = Field(True, description="启用嵌入功能")
    
    # 通用配置
    enable_fallback: bool = Field(True, description="启用模型回退机制")
    request_timeout: int = Field(30, description="请求超时时间(秒)", ge=5, le=300)
    max_retries: int = Field(3, description="最大重试次数", ge=0, le=10)

class SystemAIIntegrationCreate(SystemAIIntegrationBase):
    """创建系统AI整合设置Schema"""
    pass

class SystemAIIntegrationUpdate(BaseModel):
    """更新系统AI整合设置Schema"""
    default_chat_model_id: Optional[uuid.UUID] = None
    default_vision_model_id: Optional[uuid.UUID] = None
    default_audio_model_id: Optional[uuid.UUID] = None
    default_embedding_model_id: Optional[uuid.UUID] = None
    
    # 模型配置参数
    chat_temperature: Optional[float] = Field(None, ge=0, le=2)
    chat_max_tokens: Optional[int] = Field(None, gt=0)
    vision_temperature: Optional[float] = Field(None, ge=0, le=2)
    vision_max_tokens: Optional[int] = Field(None, gt=0)
    
    chat_enabled: Optional[bool] = None
    vision_enabled: Optional[bool] = None
    audio_enabled: Optional[bool] = None
    embedding_enabled: Optional[bool] = None
    
    enable_fallback: Optional[bool] = None
    request_timeout: Optional[int] = Field(None, ge=5, le=300)
    max_retries: Optional[int] = Field(None, ge=0, le=10)

class SystemAIIntegrationResponse(SystemAIIntegrationBase):
    """系统AI整合设置响应Schema"""
    id: uuid.UUID
    tenant_id: Optional[uuid.UUID] = None
    project_id: Optional[uuid.UUID] = None
    created_at: Optional[str] = None
    updated_at: Optional[str] = None
    
    # 模型名称字段（便于显示）
    default_chat_model_name: Optional[str] = None
    default_vision_model_name: Optional[str] = None
    default_audio_model_name: Optional[str] = None
    default_embedding_model_name: Optional[str] = None
    
    class Config:
        from_attributes = True 