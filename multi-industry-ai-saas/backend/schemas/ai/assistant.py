#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
AI 助手 Schema
"""

from typing import Optional, List, Dict, Any, Union
from datetime import datetime
import uuid
from pydantic import BaseModel, Field, validator

class AIAssistantToolBase(BaseModel):
    """AI 助手工具基础 Schema"""
    tenant_id: uuid.UUID = Field(..., description="租户 ID")
    project_id: Optional[uuid.UUID] = Field(None, description="项目 ID，为空表示租户级工具")
    name: str = Field(..., description="工具名称")
    description: Optional[str] = Field(None, description="描述")
    type: str = Field(..., description="工具类型：function, retrieval, code_interpreter")
    function_name: Optional[str] = Field(None, description="函数名称")
    function_description: Optional[str] = Field(None, description="函数描述")
    parameters_schema: Optional[Dict[str, Any]] = Field(None, description="参数模式")
    implementation: Optional[str] = Field(None, description="实现代码")
    status: str = Field("active", description="状态：active, inactive")

class AIAssistantToolCreate(AIAssistantToolBase):
    """创建 AI 助手工具 Schema"""
    pass

class AIAssistantToolUpdate(BaseModel):
    """更新 AI 助手工具 Schema"""
    name: Optional[str] = Field(None, description="工具名称")
    description: Optional[str] = Field(None, description="描述")
    function_name: Optional[str] = Field(None, description="函数名称")
    function_description: Optional[str] = Field(None, description="函数描述")
    parameters_schema: Optional[Dict[str, Any]] = Field(None, description="参数模式")
    implementation: Optional[str] = Field(None, description="实现代码")
    status: Optional[str] = Field(None, description="状态：active, inactive")

class AIAssistantToolInDB(AIAssistantToolBase):
    """数据库中的 AI 助手工具 Schema"""
    id: uuid.UUID
    is_builtin: bool = Field(..., description="是否内置工具")
    created_by: Optional[uuid.UUID] = Field(None, description="创建者 ID")
    created_at: datetime
    updated_at: Optional[datetime] = None

    # 关联数据
    creator_name: Optional[str] = None

    class Config:
        model_config = {"from_attributes": True}

class AIAssistantToolResponse(BaseModel):
    """AI 助手工具响应 Schema"""
    success: bool = True
    message: Optional[str] = None
    data: Optional[AIAssistantToolInDB] = None

class AIAssistantToolListResponse(BaseModel):
    """AI 助手工具列表响应 Schema"""
    success: bool = True
    message: Optional[str] = None
    data: List[AIAssistantToolInDB] = []
    total: int = 0
    page: int = 1
    page_size: int = 20

class AIAssistantBase(BaseModel):
    """AI 助手基础 Schema"""
    tenant_id: Optional[uuid.UUID] = Field(None, description="租户 ID")
    project_id: Optional[uuid.UUID] = Field(None, description="项目 ID，为空表示租户级助手")
    name: str = Field(..., description="助手名称")
    description: Optional[str] = Field(None, description="描述")
    avatar_url: Optional[str] = Field(None, description="头像 URL")
    instructions: str = Field(..., description="助手指令")
    model_id: Optional[uuid.UUID] = Field(None, description="模型 ID")
    config_id: Optional[uuid.UUID] = Field(None, description="配置 ID")
    knowledge_base_ids: Optional[List[uuid.UUID]] = Field(None, description="知识库 ID 列表")
    tool_ids: Optional[List[uuid.UUID]] = Field(None, description="工具 ID 列表")
    mcp_tools: Optional[List[uuid.UUID]] = Field(None, description="MCP工具 ID 列表")
    capabilities: Optional[Dict[str, Any]] = Field(None, description="助手能力配置")
    temperature: Optional[float] = Field(0.7, description="温度参数")
    max_tokens: Optional[int] = Field(None, description="最大 token 数")
    top_p: Optional[float] = Field(None, description="核心采样参数")
    frequency_penalty: Optional[float] = Field(None, description="频率惩罚")
    status: str = Field("active", description="状态：active, inactive")
    is_public: bool = Field(False, description="是否公开")
    assistant_config: Optional[Dict[str, Any]] = Field(None, description="助手配置")
    welcome_message: Optional[str] = Field(None, description="欢迎消息")
    published_roles: Optional[List[str]] = Field(None, description="发布到的角色列表")

class AIAssistantCreate(AIAssistantBase):
    """创建 AI 助手 Schema"""
    pass

class AIAssistantUpdate(BaseModel):
    """更新 AI 助手 Schema"""
    name: Optional[str] = Field(None, description="助手名称")
    description: Optional[str] = Field(None, description="描述")
    avatar_url: Optional[str] = Field(None, description="头像 URL")
    instructions: Optional[str] = Field(None, description="助手指令")
    model_id: Optional[uuid.UUID] = Field(None, description="模型 ID")
    config_id: Optional[uuid.UUID] = Field(None, description="配置 ID")
    knowledge_base_ids: Optional[List[uuid.UUID]] = Field(None, description="知识库 ID 列表")
    tool_ids: Optional[List[uuid.UUID]] = Field(None, description="工具 ID 列表")
    mcp_tools: Optional[List[uuid.UUID]] = Field(None, description="MCP工具 ID 列表")
    capabilities: Optional[Dict[str, Any]] = Field(None, description="助手能力配置")
    temperature: Optional[float] = Field(None, description="温度参数")
    max_tokens: Optional[int] = Field(None, description="最大 token 数")
    top_p: Optional[float] = Field(None, description="核心采样参数")
    frequency_penalty: Optional[float] = Field(None, description="频率惩罚")
    status: Optional[str] = Field(None, description="状态：active, inactive")
    is_public: Optional[bool] = Field(None, description="是否公开")
    assistant_config: Optional[Dict[str, Any]] = Field(None, description="助手配置")
    welcome_message: Optional[str] = Field(None, description="欢迎消息")
    published_roles: Optional[List[str]] = Field(None, description="发布到的角色列表")

class AIAssistantInDB(AIAssistantBase):
    """数据库中的 AI 助手 Schema"""
    id: uuid.UUID
    created_by: Optional[uuid.UUID] = Field(None, description="创建者 ID")
    created_at: datetime
    updated_at: Optional[datetime] = None

    # 关联数据
    model_name: Optional[str] = None
    model_display_name: Optional[str] = None
    provider_name: Optional[str] = None
    config_name: Optional[str] = None
    creator_name: Optional[str] = None
    avatar: Optional[str] = None  # 兼容前端字段

    class Config:
        model_config = {"from_attributes": True}

class AIAssistantResponse(BaseModel):
    """AI 助手响应 Schema"""
    success: bool = True
    message: Optional[str] = None
    data: Optional[AIAssistantInDB] = None

class AIAssistantListResponse(BaseModel):
    """AI 助手列表响应 Schema"""
    success: bool = True
    message: Optional[str] = None
    data: List[AIAssistantInDB] = []
    total: int = 0
    page: int = 1
    page_size: int = 20

class AIAssistantThreadBase(BaseModel):
    """AI 助手对话线程基础 Schema"""
    assistant_id: uuid.UUID = Field(..., description="助手 ID")
    user_id: uuid.UUID = Field(..., description="用户 ID")
    title: Optional[str] = Field(None, description="对话标题")
    status: str = Field("active", description="状态：active, archived")
    thread_config: Optional[Dict[str, Any]] = Field(None, description="线程配置")

class AIAssistantThreadCreate(AIAssistantThreadBase):
    """创建 AI 助手对话线程 Schema"""
    pass

class AIAssistantThreadUpdate(BaseModel):
    """更新 AI 助手对话线程 Schema"""
    title: Optional[str] = Field(None, description="对话标题")
    status: Optional[str] = Field(None, description="状态：active, archived")
    thread_config: Optional[Dict[str, Any]] = Field(None, description="线程配置")

class AIAssistantThreadInDB(AIAssistantThreadBase):
    """数据库中的 AI 助手对话线程 Schema"""
    id: uuid.UUID
    created_at: datetime
    updated_at: Optional[datetime] = None
    last_message_at: Optional[datetime] = None

    # 关联数据
    assistant_name: Optional[str] = None
    user_name: Optional[str] = None
    message_count: Optional[int] = None

    class Config:
        model_config = {"from_attributes": True}

class AIAssistantThreadResponse(BaseModel):
    """AI 助手对话线程响应 Schema"""
    success: bool = True
    message: Optional[str] = None
    data: Optional[AIAssistantThreadInDB] = None

class AIAssistantThreadListResponse(BaseModel):
    """AI 助手对话线程列表响应 Schema"""
    success: bool = True
    message: Optional[str] = None
    data: List[AIAssistantThreadInDB] = []
    total: int = 0
    page: int = 1
    page_size: int = 20

class AIAssistantMessageContent(BaseModel):
    """AI 助手消息内容 Schema"""
    type: str = Field(..., description="内容类型：text, image, file")
    text: Optional[str] = Field(None, description="文本内容")
    image_url: Optional[str] = Field(None, description="图片 URL")
    file_id: Optional[uuid.UUID] = Field(None, description="文件 ID")

class AIAssistantMessageBase(BaseModel):
    """AI 助手消息基础 Schema"""
    thread_id: uuid.UUID = Field(..., description="线程 ID")
    role: str = Field(..., description="角色：user, assistant, system, tool")
    content: Optional[Union[str, List[AIAssistantMessageContent]]] = Field(None, description="消息内容")
    content_type: str = Field("text", description="内容类型：text, image, file")
    file_ids: Optional[List[uuid.UUID]] = Field(None, description="文件 ID 列表")
    parent_id: Optional[uuid.UUID] = Field(None, description="父消息 ID")
    message_metadata: Optional[Dict[str, Any]] = Field(None, description="消息元数据")

class AIAssistantMessageCreate(AIAssistantMessageBase):
    """创建 AI 助手消息 Schema"""
    pass

class AIAssistantMessageInDB(AIAssistantMessageBase):
    """数据库中的 AI 助手消息 Schema"""
    id: uuid.UUID
    created_at: datetime

    class Config:
        model_config = {"from_attributes": True}

class AIAssistantMessageResponse(BaseModel):
    """AI 助手消息响应 Schema"""
    success: bool = True
    message: Optional[str] = None
    data: Optional[AIAssistantMessageInDB] = None

class AIAssistantMessageListResponse(BaseModel):
    """AI 助手消息列表响应 Schema"""
    success: bool = True
    message: Optional[str] = None
    data: List[AIAssistantMessageInDB] = []
    total: int = 0
    page: int = 1
    page_size: int = 20

class AIAssistantChatRequest(BaseModel):
    """AI 助手聊天请求 Schema"""
    thread_id: Optional[uuid.UUID] = Field(None, description="线程 ID，如果为空则创建新线程")
    assistant_id: uuid.UUID = Field(..., description="助手 ID")
    content: Union[str, List[AIAssistantMessageContent]] = Field(..., description="消息内容")
    content_type: str = Field("text", description="内容类型：text, image, file")
    file_ids: Optional[List[uuid.UUID]] = Field(None, description="文件 ID 列表")

class AIAssistantChatResponse(BaseModel):
    """AI 助手聊天响应 Schema"""
    success: bool = True
    message: Optional[str] = None
    thread_id: uuid.UUID
    assistant_id: uuid.UUID
    assistant_name: str
    response: str
    created_at: datetime
