#!/usr/bin/env python
# -*- coding: utf-8 -*-

from typing import List, Optional, Dict, Any
from datetime import datetime
from uuid import UUID
from pydantic import BaseModel, Field, validator

# 采购订单项基础模型
class PurchaseOrderItemBase(BaseModel):
    product_id: Optional[UUID] = None
    supplier_id: Optional[UUID] = None
    category_id: Optional[UUID] = None
    product_name: str
    product_code: Optional[str] = None
    product_unit: Optional[str] = None
    product_specification: Optional[str] = None
    quantity: float
    unit_price: float
    discount_rate: float = 0.0
    tax_rate: float = 0.0
    notes: Optional[str] = None

# 创建采购订单项请求模型
class PurchaseOrderItemCreate(PurchaseOrderItemBase):
    pass

# 更新采购订单项请求模型
class PurchaseOrderItemUpdate(BaseModel):
    product_id: Optional[UUID] = None
    supplier_id: Optional[UUID] = None
    category_id: Optional[UUID] = None
    product_name: Optional[str] = None
    product_code: Optional[str] = None
    product_unit: Optional[str] = None
    product_specification: Optional[str] = None
    quantity: Optional[float] = None
    unit_price: Optional[float] = None
    discount_rate: Optional[float] = None
    tax_rate: Optional[float] = None
    notes: Optional[str] = None

# 采购订单项响应模型
class PurchaseOrderItemResponse(PurchaseOrderItemBase):
    id: UUID
    purchase_order_id: UUID
    total_amount: float
    received_quantity: float = 0.0
    created_at: datetime
    updated_at: datetime
    
    # 关联信息
    supplier_name: Optional[str] = None
    category_name: Optional[str] = None

    class Config:
        model_config = {"from_attributes": True}

# 采购订单基础模型
class PurchaseOrderBase(BaseModel):
    warehouse_id: Optional[UUID] = None
    order_date: datetime = Field(default_factory=datetime.utcnow)
    expected_delivery_date: Optional[datetime] = None
    status: str = "draft"
    payment_method: Optional[str] = None
    notes: Optional[str] = None

# 创建采购订单请求模型
class PurchaseOrderCreate(PurchaseOrderBase):
    items: List[PurchaseOrderItemCreate]

# 更新采购订单请求模型
class PurchaseOrderUpdate(BaseModel):
    warehouse_id: Optional[UUID] = None
    order_date: Optional[datetime] = None
    expected_delivery_date: Optional[datetime] = None
    status: Optional[str] = None
    payment_method: Optional[str] = None
    notes: Optional[str] = None

# 更新采购订单状态请求模型
class PurchaseOrderStatusUpdate(BaseModel):
    status: str
    actual_delivery_date: Optional[datetime] = None
    notes: Optional[str] = None

# 更新采购订单支付状态请求模型
class PurchaseOrderPaymentUpdate(BaseModel):
    payment_status: str
    payment_method: Optional[str] = None
    payment_date: Optional[datetime] = None
    notes: Optional[str] = None

# 采购订单响应模型
class PurchaseOrderResponse(PurchaseOrderBase):
    id: UUID
    order_number: str
    project_id: UUID
    actual_delivery_date: Optional[datetime] = None
    total_amount: float
    discount_amount: float
    tax_amount: float
    shipping_fee: float
    final_amount: float
    payment_status: str
    payment_date: Optional[datetime] = None
    created_by: Optional[UUID] = None
    updated_by: Optional[UUID] = None
    created_at: datetime
    updated_at: datetime

    # 关联信息
    warehouse_name: Optional[str] = None
    creator_name: Optional[str] = None
    updater_name: Optional[str] = None

    items: List[PurchaseOrderItemResponse] = []

    class Config:
        model_config = {"from_attributes": True}

# 采购订单列表响应模型
class PurchaseOrderListResponse(BaseModel):
    items: List[PurchaseOrderResponse]
    total: int
    skip: int
    limit: int

# 通用上传响应模型
class UploadResponse(BaseModel):
    success: bool
    message: str
    data: Optional[Dict[str, Any]] = None

# 分拨目标模型
class DistributionTarget(BaseModel):
    type: str  # 'store' 或 'warehouse'
    target_id: UUID
    percentage: float = 100.0
    items: Optional[List[Dict[str, Any]]] = None

# 采购项目模型
class PurchaseItemCreate(BaseModel):
    product_id: Optional[UUID] = None
    product_name: str
    product_code: Optional[str] = None
    product_unit: Optional[str] = None
    product_specification: Optional[str] = None
    quantity: float
    unit_price: float = 0.0
    discount_rate: float = 0.0
    tax_rate: float = 0.0
    notes: Optional[str] = None

# 分拨项目模型
class DistributionItemCreate(BaseModel):
    product_id: Optional[UUID] = None
    product_name: str
    product_code: Optional[str] = None
    product_unit: Optional[str] = None
    product_specification: Optional[str] = None
    quantity: float
    unit_price: float = 0.0
    notes: Optional[str] = None

# 预览上传请求模型
class PreviewUploadRequest(BaseModel):
    upload_type: str  # 'both', 'purchase', 'distribution'
    supplier_id: Optional[UUID] = None
    warehouse_id: Optional[UUID] = None
    distribution_mode: Optional[str] = None  # 'direct', 'partial'
    distribution_targets: Optional[List[DistributionTarget]] = None

# 确认上传请求模型
class ConfirmUploadRequest(BaseModel):
    upload_type: str  # 'both', 'purchase', 'distribution'
    supplier_id: Optional[UUID] = None
    warehouse_id: Optional[UUID] = None
    distribution_mode: Optional[str] = None  # 'direct', 'partial'
    distribution_targets: Optional[List[DistributionTarget]] = None
