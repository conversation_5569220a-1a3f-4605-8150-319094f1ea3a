#!/usr/bin/env python
# -*- coding: utf-8 -*-

from typing import List, Optional, Dict, Any, Union
from datetime import datetime, date
from uuid import UUID
from pydantic import BaseModel, Field

# 基础仪表盘模型
class DashboardBase(BaseModel):
    last_updated: datetime = Field(default_factory=datetime.utcnow)

# AI推荐模型
class DashboardAIRecommendation(BaseModel):
    title: str
    content: str
    type: str
    action_items: Optional[List[str]] = None
    priority: Optional[str] = None
    created_at: datetime = Field(default_factory=datetime.utcnow)

# AI推荐响应模型
class DashboardAIRecommendationResponse(BaseModel):
    recommendations: List[DashboardAIRecommendation]

# 销售趋势数据点
class SalesTrendPoint(BaseModel):
    date: str
    value: float
    comparison_value: Optional[float] = None

# 类别分布数据点
class CategoryDistributionPoint(BaseModel):
    name: str
    value: float
    percentage: float

# 支付方式分布数据点
class PaymentMethodDistributionPoint(BaseModel):
    name: str
    value: float
    percentage: float

# 门店性能数据点
class StorePerformancePoint(BaseModel):
    id: UUID
    name: str
    sales: float
    orders: int
    customers: Optional[int] = None
    growth: Optional[float] = None

# 产品性能数据点
class ProductPerformancePoint(BaseModel):
    id: UUID
    name: str
    sales: float
    quantity: int
    profit: Optional[float] = None
    growth: Optional[float] = None

# 统计卡片数据
class StatisticCard(BaseModel):
    value: float
    change: float
    is_increase: bool

# 项目管理仪表盘响应模型
class ProjectDashboardResponse(DashboardBase):
    total_sales: float
    total_orders: int
    total_users: int
    total_stores: int
    ai_usage: int
    sales_growth: float
    order_growth: float
    user_growth: float

    today_sales: StatisticCard
    today_orders: StatisticCard
    today_users: StatisticCard
    today_ai_usage: StatisticCard

    sales_trend: List[SalesTrendPoint]
    category_distribution: List[CategoryDistributionPoint]
    store_performance: List[StorePerformancePoint]
    recent_activities: List[Dict[str, Any]]
    ai_recommendations: List[DashboardAIRecommendation]

# 渠道销售数据点
class ChannelSalesPoint(BaseModel):
    name: str
    value: float

# 充值售卡数据
class RechargeData(BaseModel):
    recharge_amount: float
    card_sales_amount: float
    recharge_count: int
    card_sales_count: int

# 运营仪表盘响应模型
class OperationsDashboardResponse(DashboardBase):
    total_sales: float
    total_orders: int
    total_customers: int
    average_order_value: float
    sales_growth: float
    order_growth: float
    customer_growth: float

    today_sales: StatisticCard
    today_orders: StatisticCard
    today_customers: StatisticCard
    today_average_order: StatisticCard

    sales_trend: List[SalesTrendPoint]
    category_distribution: List[CategoryDistributionPoint]
    payment_distribution: List[PaymentMethodDistributionPoint]
    store_performance: List[StorePerformancePoint]
    product_performance: List[ProductPerformancePoint]
    channel_sales: List[ChannelSalesPoint]
    recharge_data: RechargeData
    ai_recommendations: List[DashboardAIRecommendation]

# 财务仪表盘响应模型
class FinanceDashboardResponse(DashboardBase):
    total_revenue: float
    total_cost: float
    gross_profit: float
    profit_margin: float
    revenue_growth: float
    cost_growth: float
    profit_growth: float

    monthly_revenue: StatisticCard
    monthly_cost: StatisticCard
    monthly_profit: StatisticCard
    monthly_margin: StatisticCard

    revenue_trend: List[SalesTrendPoint]
    cost_trend: List[SalesTrendPoint]
    profit_trend: List[SalesTrendPoint]
    category_profit: List[CategoryDistributionPoint]
    store_profit: List[StorePerformancePoint]
    payment_reconciliation: Dict[str, Any]
    profit_by_product: List[Dict[str, Any]]
    ai_recommendations: List[DashboardAIRecommendation]

# 库存项警告
class InventoryAlert(BaseModel):
    id: UUID
    product_id: UUID
    product_name: str
    product_code: Optional[str] = None
    current_quantity: int
    threshold_quantity: int
    warehouse_id: UUID
    warehouse_name: str
    alert_type: str  # low_stock, excess_stock, slow_moving
    days_without_movement: Optional[int] = None
    suggested_action: Optional[str] = None

# 仓储仪表盘响应模型
class WarehouseDashboardResponse(DashboardBase):
    total_inventory: int
    total_value: float
    low_stock_count: int
    excess_stock_count: int
    slow_moving_count: int
    turnover_rate: float

    inventory_by_warehouse: List[Dict[str, Any]]
    inventory_by_category: List[CategoryDistributionPoint]
    inventory_alerts: List[InventoryAlert]
    recent_movements: List[Dict[str, Any]]
    inventory_trend: List[Dict[str, Any]]
    ai_recommendations: List[DashboardAIRecommendation]

# 采购订单状态统计
class PurchaseOrderStatusStats(BaseModel):
    draft: int
    confirmed: int
    received: int
    cancelled: int
    total: int

# 采购订单支付状态统计
class PurchaseOrderPaymentStats(BaseModel):
    unpaid: int
    partial: int
    paid: int
    total: int

# 供应商性能
class SupplierPerformance(BaseModel):
    id: UUID
    name: str
    total_orders: int
    total_amount: float
    on_time_delivery_rate: float
    quality_score: Optional[float] = None
    average_lead_time: Optional[int] = None

# 采购仪表盘响应模型
class PurchaseDashboardResponse(DashboardBase):
    total_purchase_amount: float
    total_orders: int
    average_order_value: float
    pending_orders: int

    monthly_purchase: StatisticCard
    monthly_orders: StatisticCard

    purchase_trend: List[SalesTrendPoint]
    category_distribution: List[CategoryDistributionPoint]
    order_status: PurchaseOrderStatusStats
    payment_status: PurchaseOrderPaymentStats
    supplier_performance: List[SupplierPerformance]
    recent_orders: List[Dict[str, Any]]
    ai_recommendations: List[DashboardAIRecommendation]
