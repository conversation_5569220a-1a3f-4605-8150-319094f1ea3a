from typing import Optional, Dict, Any, List, Union
from pydantic import BaseModel, UUID4
from datetime import datetime

# 基础配置模型
class SystemConfigBase(BaseModel):
    config_type: str
    config_key: str
    config_value: Dict[str, Any]

# 创建配置请求模型
class SystemConfigCreate(SystemConfigBase):
    project_id: UUID4

# 更新配置请求模型
class SystemConfigUpdate(BaseModel):
    config_value: Dict[str, Any]

# 配置响应模型
class SystemConfig(SystemConfigBase):
    id: UUID4
    project_id: UUID4
    created_at: datetime
    updated_at: Optional[datetime] = None

    class Config:
        orm_mode = True

# 批量配置请求模型
class SystemConfigBulkUpdate(BaseModel):
    configs: List[Dict[str, Any]]

# 主题设置模型
class ThemeSettings(BaseModel):
    mode: str
    primaryColor: str
    borderRadius: int
    compactMode: bool
    customFont: bool
    fontFamily: str

# 通知设置模型
class NotificationSettings(BaseModel):
    emailNotification: bool
    smsNotification: bool
    pushNotification: bool
    notificationFrequency: str
    dailyDigest: bool
    soundEnabled: bool

# 安全设置模型
class SecuritySettings(BaseModel):
    passwordExpiration: int
    twoFactorAuth: bool
    loginAttempts: int
    sessionTimeout: int
    ipRestriction: bool

# 常规设置模型
class GeneralSettings(BaseModel):
    language: str
    timezone: str
    dateFormat: str
    timeFormat: str

# 第三方登录设置模型
class ThirdPartyLoginSettings(BaseModel):
    wechat_work: Dict[str, Any]
    dingtalk: Dict[str, Any]
    feishu: Dict[str, Any]
    wechat: Dict[str, Any]

# AI设置模型
class AISettings(BaseModel):
    enabled: bool = True  # AI功能总开关
    cache_duration: str = "1hour"  # AI缓存时长: 10min, 1hour, 6hour
    insights_enabled: bool = True  # AI洞察功能
    suggestions_enabled: bool = True  # AI建议功能
    auto_analysis: bool = False  # 自动分析
    analysis_frequency: str = "daily"  # 分析频率: realtime, hourly, daily, weekly
    max_requests_per_hour: int = 100  # 每小时最大请求数
    enable_data_learning: bool = False  # 是否启用数据学习
    privacy_mode: bool = True  # 隐私模式，不保存敏感数据
