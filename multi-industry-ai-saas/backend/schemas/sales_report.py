#!/usr/bin/env python
# -*- coding: utf-8 -*-

from typing import List, Optional, Dict, Any
from datetime import datetime, date
from uuid import UUID
from pydantic import BaseModel, Field

# 销售上报项基础模型
class SalesReportItemBase(BaseModel):
    product_id: Optional[UUID] = None
    product_name: str
    product_code: Optional[str] = None
    product_category: Optional[str] = None
    product_unit: Optional[str] = None
    quantity: float
    unit_price: float
    discount_amount: float = 0.0
    notes: Optional[str] = None

# 创建销售上报项请求模型
class SalesReportItemCreate(SalesReportItemBase):
    pass

# 更新销售上报项请求模型
class SalesReportItemUpdate(BaseModel):
    product_id: Optional[UUID] = None
    product_name: Optional[str] = None
    product_code: Optional[str] = None
    product_category: Optional[str] = None
    product_unit: Optional[str] = None
    quantity: Optional[float] = None
    unit_price: Optional[float] = None
    discount_amount: Optional[float] = None
    notes: Optional[str] = None

# 销售上报项响应模型
class SalesReportItemResponse(SalesReportItemBase):
    id: UUID
    sales_report_id: UUID
    total_amount: float
    created_at: datetime
    updated_at: datetime

    class Config:
        model_config = {"from_attributes": True}

# 支付方式基础模型
class PaymentMethodBase(BaseModel):
    method: str
    amount: float

# 充值/售卡基础模型
class RechargeSaleBase(BaseModel):
    type: str  # "recharge" 或 "card"
    amount: float
    count: Optional[int] = 0
    description: Optional[str] = None
    payment_methods: Optional[List[Dict[str, Any]]] = None

# 充值/售卡响应模型
class RechargeSaleResponse(RechargeSaleBase):
    pass

# 支付方式响应模型
class PaymentMethodResponse(BaseModel):
    method_id: str
    method_name: str
    sales_amount: float = 0.0
    recharge_amount: float = 0.0
    total_amount: float = 0.0

# 销售上报基础模型
class ChannelSaleBase(BaseModel):
    channel_id: UUID
    amount: float
    payment_methods: Optional[List[Dict[str, Any]]] = None

class SalesReportBase(BaseModel):
    store_id: UUID
    report_date: datetime = Field(default_factory=datetime.utcnow)
    report_type: str = "daily"
    total_sales: float = 0.0
    total_orders: int = 0
    total_customers: int = 0
    online_sales: float = 0.0
    offline_sales: float = 0.0
    payment_methods: Any = None  # 可以是Dict[str, float]或List[PaymentMethodResponse]
    product_categories: Optional[Dict[str, float]] = None
    hourly_sales: Optional[Dict[str, float]] = None
    channel_sales: Optional[Dict[str, float]] = None
    notes: Optional[str] = None

# 创建销售上报请求模型
class SalesReportCreate(SalesReportBase):
    items: List[SalesReportItemCreate]
    recharge_sales: Optional[List[RechargeSaleBase]] = None
    channel_sales: Optional[List[ChannelSaleBase]] = None

# 更新销售上报请求模型
class SalesReportUpdate(BaseModel):
    store_id: Optional[UUID] = None
    report_date: Optional[datetime] = None
    report_type: Optional[str] = None
    total_sales: Optional[float] = None
    total_orders: Optional[int] = None
    total_customers: Optional[int] = None
    online_sales: Optional[float] = None
    offline_sales: Optional[float] = None
    payment_methods: Optional[Dict[str, float]] = None
    product_categories: Optional[Dict[str, float]] = None
    hourly_sales: Optional[Dict[str, float]] = None
    channel_sales: Optional[Dict[str, float]] = None
    notes: Optional[str] = None
    recharge_sales: Optional[List[RechargeSaleBase]] = None
    channel_sales_list: Optional[List[ChannelSaleBase]] = None

# 更新销售上报状态请求模型
class SalesReportStatusUpdate(BaseModel):
    status: str
    reject_reason: Optional[str] = None

# 销售上报响应模型
class SalesReportResponse(SalesReportBase):
    id: UUID
    project_id: UUID
    status: str
    reject_reason: Optional[str] = None
    created_by: Optional[UUID] = None
    updated_by: Optional[UUID] = None
    approved_by: Optional[UUID] = None
    approved_at: Optional[datetime] = None
    created_at: datetime
    updated_at: datetime

    # 关联信息
    store_name: Optional[str] = None
    creator_name: Optional[str] = None
    updater_name: Optional[str] = None
    approver_name: Optional[str] = None

    # 前端期望的字段
    payment_methods: List[PaymentMethodResponse] = []
    recharge_sales: List[RechargeSaleResponse] = []
    recharge_amount: float = 0.0
    card_sales_amount: float = 0.0
    recharge_count: int = 0
    card_sales_count: int = 0

    items: List[SalesReportItemResponse] = []

    class Config:
        model_config = {"from_attributes": True}

# 销售上报列表响应模型
class SalesReportListResponse(BaseModel):
    items: List[SalesReportResponse]
    total: int
    page: int
    size: int
    pages: int

# 销售统计项模型
class SalesReportStatisticsItem(BaseModel):
    month: str
    total_sales: float = 0.0
    online_sales: float = 0.0
    offline_sales: float = 0.0
    recharge_total: float = 0.0
    total_orders: int = 0
    total_customers: int = 0

# 销售统计响应模型
class SalesReportStatisticsResponse(BaseModel):
    items: List[SalesReportStatisticsItem]
    total: int
