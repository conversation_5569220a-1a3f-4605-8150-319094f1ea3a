#!/usr/bin/env python
# -*- coding: utf-8 -*-

import logging
logger = logging.getLogger(__name__)
logger.info("api/project/user_profile.py loaded")

from fastapi import APIRouter, Depends, HTTPException, UploadFile, File, Form, status, Query
from fastapi.responses import JSONResponse, RedirectResponse, HTMLResponse
from sqlalchemy.orm import Session
from typing import List, Optional, Dict, Any
from datetime import datetime
import os
import shutil
import uuid
import json
import secrets
from sqlalchemy import select

from db.database import get_db
from core.auth import get_current_user
from api.deps import get_current_project
from models.user import User, UserActivity, ThirdPartyAccount
from models.project import Project
from schemas.user import UserProfileResponse, UserProfileUpdate, PasswordChange, ThirdPartyAccountCreate, ThirdPartyAccountResponse
from services.system_config import SystemConfigService
from core.config import settings

router = APIRouter()

@router.get("/profile", response_model=UserProfileResponse)
async def get_user_profile(
    current_user: User = Depends(get_current_user),
    project: Project = Depends(get_current_project),
    db: Session = Depends(get_db)
):
    """获取当前用户的个人资料"""
    result = await db.execute(select(User).where(User.id == current_user.id))
    user = result.scalars().first()
    if not user:
        raise HTTPException(status_code=404, detail="用户不存在")

    # 获取用户活动记录
    result = await db.execute(
        select(UserActivity).where(
            UserActivity.user_id == user.id,
            UserActivity.project_id == project.id
        ).order_by(UserActivity.created_at.desc()).limit(10)
    )
    activities = result.scalars().all()

    # 获取第三方账号绑定信息
    result = await db.execute(
        select(ThirdPartyAccount).where(ThirdPartyAccount.user_id == user.id)
    )
    third_party_accounts = result.scalars().all()

    # 序列化活动记录
    activities_data = []
    for activity in activities:
        activities_data.append({
            "id": str(activity.id),
            "activity_type": activity.activity_type,
            "description": activity.description,
            "created_at": activity.created_at.isoformat() if activity.created_at else None
        })
    
    # 序列化第三方账号
    third_party_data = []
    for account in third_party_accounts:
        third_party_data.append({
            "id": str(account.id),
            "platform": account.platform,
            "platform_user_id": account.platform_user_id,
            "platform_username": account.platform_username,
            "avatar_url": account.avatar_url,
            "created_at": account.created_at.isoformat() if account.created_at else None
        })

    return {
        "success": True,
        "data": {
            "id": str(user.id),
            "username": user.username,
            "email": user.email,
            "phone": user.phone or "",
            "role": user.role or "",
            "department": getattr(user, 'department', ''),
            "avatar_url": user.avatar_url or "",
            "created_at": user.created_at.isoformat() if user.created_at else None,
            "last_login_at": user.last_login.isoformat() if user.last_login else None,
            "activities": activities_data,
            "third_party_accounts": third_party_data
        }
    }

@router.put("/profile", response_model=Dict[str, Any])
async def update_user_profile(
    profile_update: UserProfileUpdate,
    current_user: User = Depends(get_current_user),
    project: Project = Depends(get_current_project),
    db: Session = Depends(get_db)
):
    """更新用户个人资料"""
    result = await db.execute(select(User).where(User.id == current_user.id))
    user = result.scalars().first()
    if not user:
        raise HTTPException(status_code=404, detail="用户不存在")
    for field, value in profile_update.dict(exclude_unset=True).items():
        setattr(user, field, value)
    activity = UserActivity(
        user_id=user.id,
        project_id=project.id,
        activity_type="profile_update",
        description="更新了个人资料",
        created_at=datetime.now()
    )
    db.add(activity)
    await db.commit()
    await db.refresh(user)
    return {
        "success": True,
        "message": "个人资料更新成功",
        "data": {
            "id": str(user.id),
            "username": user.username,
            "email": user.email,
            "phone": user.phone or "",
            "role": user.role or "",
            "department": getattr(user, 'department', ''),
            "avatar_url": user.avatar_url or "",
            "updated_at": user.updated_at
        }
    }

@router.post("/avatar", response_model=Dict[str, Any])
async def upload_avatar(
    file: UploadFile = File(...),
    current_user: User = Depends(get_current_user),
    project: Project = Depends(get_current_project),
    db: Session = Depends(get_db)
):
    """上传用户头像"""
    result = await db.execute(select(User).where(User.id == current_user.id))
    user = result.scalars().first()
    if not user:
        raise HTTPException(status_code=404, detail="用户不存在")
    upload_dir = os.path.join("uploads", "avatars")
    os.makedirs(upload_dir, exist_ok=True)
    file_extension = os.path.splitext(file.filename)[1]
    unique_filename = f"{uuid.uuid4()}{file_extension}"
    file_path = os.path.join(upload_dir, unique_filename)
    with open(file_path, "wb") as buffer:
        shutil.copyfileobj(file.file, buffer)
    avatar_url = f"/uploads/avatars/{unique_filename}"
    user.avatar_url = avatar_url
    activity = UserActivity(
        user_id=user.id,
        project_id=project.id,
        activity_type="avatar_update",
        description="更新了头像",
        created_at=datetime.now()
    )
    db.add(activity)
    await db.commit()
    return {
        "success": True,
        "message": "头像上传成功",
        "url": avatar_url
    }

@router.put("/password", response_model=Dict[str, Any])
async def change_password(
    password_change: PasswordChange,
    current_user: User = Depends(get_current_user),
    project: Project = Depends(get_current_project),
    db: Session = Depends(get_db)
):
    """修改用户密码"""
    result = await db.execute(select(User).where(User.id == current_user.id))
    user = result.scalars().first()
    if not user:
        raise HTTPException(status_code=404, detail="用户不存在")
    if not user.verify_password(password_change.currentPassword):
        raise HTTPException(status_code=400, detail="当前密码不正确")
    if password_change.newPassword != password_change.confirmPassword:
        raise HTTPException(status_code=400, detail="新密码与确认密码不一致")
    user.password_hash = User.get_password_hash(password_change.newPassword)
    activity = UserActivity(
        user_id=user.id,
        project_id=project.id,
        activity_type="password_change",
        description="修改了密码",
        created_at=datetime.now()
    )
    db.add(activity)
    await db.commit()
    return {
        "success": True,
        "message": "密码修改成功"
    }

@router.get("/activities", response_model=Dict[str, Any])
async def get_user_activities(
    current_user: User = Depends(get_current_user),
    project: Project = Depends(get_current_project),
    db: Session = Depends(get_db),
    limit: int = 20,
    offset: int = 0
):
    """获取用户活动记录"""
    result = await db.execute(select(UserActivity).where(
        UserActivity.user_id == current_user.id,
        UserActivity.project_id == project.id
    ).order_by(UserActivity.created_at.desc()).offset(offset).limit(limit))
    activities = result.scalars().all()
    count_result = await db.execute(select(UserActivity).where(
        UserActivity.user_id == current_user.id,
        UserActivity.project_id == project.id
    ))
    total = len(count_result.scalars().all())
    # 序列化活动记录
    activities_data = []
    for activity in activities:
        activities_data.append({
            "id": str(activity.id),
            "activity_type": activity.activity_type,
            "description": activity.description,
            "created_at": activity.created_at.isoformat() if activity.created_at else None
        })
    
    return {
        "success": True,
        "data": activities_data,
        "total": total,
        "limit": limit,
        "offset": offset
    }

@router.get("/bindings", response_model=Dict[str, Any])
async def get_third_party_accounts(
    current_user: User = Depends(get_current_user),
    project: Project = Depends(get_current_project),
    db: Session = Depends(get_db)
):
    """获取用户绑定的第三方账号"""
    result = await db.execute(select(ThirdPartyAccount).where(
        ThirdPartyAccount.user_id == current_user.id
    ))
    accounts = result.scalars().all()
    
    # 格式化返回数据
    accounts_data = []
    for account in accounts:
        accounts_data.append({
            "id": str(account.id),
            "platform": account.platform,
            "platform_user_id": account.platform_user_id,
            "platform_username": account.platform_username,
            "avatar_url": account.avatar_url,
            "created_at": account.created_at.isoformat() if account.created_at else None,
            "updated_at": account.updated_at.isoformat() if account.updated_at else None
        })
    
    return {
        "success": True,
        "data": accounts_data
    }

@router.post("/bind/{platform}", response_model=Dict[str, Any])
async def bind_third_party_account(
    platform: str,
    code: str,
    current_user: User = Depends(get_current_user),
    project: Project = Depends(get_current_project),
    db: Session = Depends(get_db)
):
    """绑定第三方账号"""
    valid_platforms = ["wechat", "dingtalk", "feishu", "wecom"]
    if platform not in valid_platforms:
        raise HTTPException(status_code=400, detail="不支持的平台类型")
    result = await db.execute(select(ThirdPartyAccount).where(
        ThirdPartyAccount.user_id == current_user.id,
        ThirdPartyAccount.platform == platform
    ))
    existing_account = result.scalars().first()
    platform_user_id = f"mock_{platform}_user_{uuid.uuid4()}"
    platform_username = f"Mock {platform.capitalize()} User"
    avatar_url = None
    if existing_account:
        existing_account.platform_user_id = platform_user_id
        existing_account.platform_username = platform_username
        existing_account.avatar_url = avatar_url
        existing_account.updated_at = datetime.now()
        account_id = existing_account.id
        message = f"更新{platform}账号绑定成功"
    else:
        new_account = ThirdPartyAccount(
            id=uuid.uuid4(),
            user_id=current_user.id,
            platform=platform,
            platform_user_id=platform_user_id,
            platform_username=platform_username,
            avatar_url=avatar_url,
            created_at=datetime.now(),
            updated_at=datetime.now()
        )
        db.add(new_account)
        account_id = new_account.id
        message = f"绑定{platform}账号成功"
    activity = UserActivity(
        user_id=current_user.id,
        project_id=project.id,
        activity_type="third_party_bind",
        description=f"绑定了{platform}账号",
        created_at=datetime.now()
    )
    db.add(activity)
    await db.commit()
    return {
        "success": True,
        "message": message,
        "data": {
            "id": str(account_id),
            "platform": platform,
            "platform_user_id": platform_user_id,
            "platform_username": platform_username
        }
    }

@router.delete("/bind/{account_id}", response_model=Dict[str, Any])
async def unbind_third_party_account(
    account_id: uuid.UUID,
    current_user: User = Depends(get_current_user),
    project: Project = Depends(get_current_project),
    db: Session = Depends(get_db)
):
    """解绑第三方账号"""
    result = await db.execute(select(ThirdPartyAccount).where(
        ThirdPartyAccount.id == account_id,
        ThirdPartyAccount.user_id == current_user.id
    ))
    account = result.scalars().first()
    if not account:
        raise HTTPException(status_code=404, detail="第三方账号不存在或不属于当前用户")
    
    platform = account.platform
    
    # 同时清理User表中的第三方账号字段
    result = await db.execute(select(User).where(User.id == current_user.id))
    user = result.scalars().first()
    if user:
        if platform == "dingtalk":
            user.dingtalk_union_id = None
            user.dingtalk_user_id = None
        elif platform == "wechat":
            user.wechat_openid = None
            user.wechat_unionid = None
        user.updated_at = datetime.utcnow()
    
    # 删除ThirdPartyAccount记录
    await db.delete(account)
    
    # 记录解绑活动
    activity = UserActivity(
        user_id=current_user.id,
        project_id=project.id,
        activity_type="third_party_unbind",
        description=f"解绑了{platform}账号",
        created_at=datetime.utcnow()
    )
    db.add(activity)
    await db.commit()
    
    return {
        "success": True,
        "message": f"解绑{platform}账号成功"
    }

@router.get("/bind-url", response_model=Dict[str, Any])
async def get_bind_url(
    platform: str = Query(..., description="第三方平台类型"),
    current_user: User = Depends(get_current_user),
    project: Project = Depends(get_current_project),
    db: Session = Depends(get_db)
):
    """获取第三方账号绑定的授权URL"""
    valid_platforms = ["wechat", "dingtalk"]
    if platform not in valid_platforms:
        raise HTTPException(status_code=400, detail="不支持的平台类型")
    
    try:
        # 获取第三方登录配置
        configs = await SystemConfigService.get_configs_by_type(db, project.id, "third_party_login")
        config_dict = {config.config_key: config.config_value for config in configs}
        platform_config = config_dict.get(platform, {})
        
        if not platform_config.get("enabled", False):
            raise HTTPException(status_code=400, detail=f"{platform}绑定未启用")
        
        # 生成state参数（用于绑定标识）
        state = f"{str(uuid.uuid4())}:{project.id}:{current_user.id}:bind"
        
        if platform == "dingtalk":
            app_key = platform_config.get("app_key")
            redirect_uri = platform_config.get("redirect_uri")
            if not app_key or not redirect_uri:
                raise HTTPException(status_code=400, detail="钉钉配置不完整")
            
            # 使用配置中的统一回调URL
            callback_url = redirect_uri
            
            # 构建钉钉授权URL（根据官方文档添加必需的prompt参数）
            auth_url = (
                f"https://login.dingtalk.com/oauth2/auth?"
                f"response_type=code&"
                f"scope=openid&"
                f"client_id={app_key}&"
                f"redirect_uri={callback_url}&"
                f"state={state}&"
                f"prompt=consent"
            )
            
        elif platform == "wechat":
            app_id = platform_config.get("app_id")
            if not app_id:
                raise HTTPException(status_code=400, detail="微信配置不完整")
            
            # 构建微信绑定回调URL - 直接使用HTTP协议
            callback_url = f"http://saas.houshanai.com/api/project/{project.id}/users/bind-callback"
            
            # 构建微信授权URL
            auth_url = (
                f"https://open.weixin.qq.com/connect/qrconnect?"
                f"response_type=code&"
                f"scope=snsapi_login&"
                f"appid={app_id}&"
                f"redirect_uri={callback_url}&"
                f"state={state}#wechat_redirect"
            )
        
        return {
            "success": True,
            "data": {
                "platform": platform,
                "auth_url": auth_url,
                "state": state.split(':')[0]  # 只返回UUID部分给前端
            }
        }
        
    except Exception as e:
        logger.error(f"获取{platform}绑定URL失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取绑定URL失败: {str(e)}")

@router.get("/bind-callback")
async def bind_callback(
    code: str = Query(..., description="授权码"),
    authCode: str = Query(None, description="授权码（兼容性参数）"),
    state: str = Query(..., description="状态参数"),
    project: Project = Depends(get_current_project),
    db: Session = Depends(get_db)
):
    """处理第三方账号绑定回调"""
    try:
        # 使用code或authCode，优先使用code
        auth_code = code or authCode
        if not auth_code:
            error_html = """
            <!DOCTYPE html>
            <html>
            <head>
                <title>绑定失败</title>
                <meta charset="utf-8">
            </head>
            <body>
                <script>
                    if (window.opener) {
                        window.opener.postMessage({
                            type: 'BIND_ERROR',
                            error: 'missing_code',
                            message: '缺少授权码'
                        }, '*');
                        window.close();
                    } else {
                        window.location.href = '/user/profile?bind_error=missing_code';
                    }
                </script>
                <div style="text-align: center; padding: 50px; font-family: Arial, sans-serif;">
                    <h2>❌ 绑定失败</h2>
                    <p>缺少授权码，请重试。</p>
                </div>
            </body>
            </html>
            """
            return HTMLResponse(content=error_html, status_code=400)
        
        # 解析state参数
        state_parts = state.split(':')
        if len(state_parts) != 4 or state_parts[3] != 'bind':
            error_html = """
            <!DOCTYPE html>
            <html>
            <head>
                <title>绑定失败</title>
                <meta charset="utf-8">
            </head>
            <body>
                <script>
                    if (window.opener) {
                        window.opener.postMessage({
                            type: 'BIND_ERROR',
                            error: 'invalid_state',
                            message: '无效的状态参数'
                        }, '*');
                        window.close();
                    } else {
                        window.location.href = '/user/profile?bind_error=invalid_state';
                    }
                </script>
                <div style="text-align: center; padding: 50px; font-family: Arial, sans-serif;">
                    <h2>❌ 绑定失败</h2>
                    <p>无效的状态参数，请重试。</p>
                </div>
            </body>
            </html>
            """
            return HTMLResponse(content=error_html, status_code=400)
        
        _, project_id, user_id, action = state_parts
        
        if project_id != str(project.id):
            error_html = """
            <!DOCTYPE html>
            <html>
            <head>
                <title>绑定失败</title>
                <meta charset="utf-8">
            </head>
            <body>
                <script>
                    if (window.opener) {
                        window.opener.postMessage({
                            type: 'BIND_ERROR',
                            error: 'project_mismatch',
                            message: '项目不匹配'
                        }, '*');
                        window.close();
                    } else {
                        window.location.href = '/user/profile?bind_error=project_mismatch';
                    }
                </script>
                <div style="text-align: center; padding: 50px; font-family: Arial, sans-serif;">
                    <h2>❌ 绑定失败</h2>
                    <p>项目不匹配，请重试。</p>
                </div>
            </body>
            </html>
            """
            return HTMLResponse(content=error_html, status_code=400)
        
        # 获取用户
        result = await db.execute(select(User).where(User.id == uuid.UUID(user_id)))
        user = result.scalars().first()
        if not user:
            error_html = """
            <!DOCTYPE html>
            <html>
            <head>
                <title>绑定失败</title>
                <meta charset="utf-8">
            </head>
            <body>
                <script>
                    if (window.opener) {
                        window.opener.postMessage({
                            type: 'BIND_ERROR',
                            error: 'user_not_found',
                            message: '用户不存在'
                        }, '*');
                        window.close();
                    } else {
                        window.location.href = '/user/profile?bind_error=user_not_found';
                    }
                </script>
                <div style="text-align: center; padding: 50px; font-family: Arial, sans-serif;">
                    <h2>❌ 绑定失败</h2>
                    <p>用户不存在，请重试。</p>
                </div>
            </body>
            </html>
            """
            return HTMLResponse(content=error_html, status_code=404)
        
        # 根据state参数中的平台信息或通过调用API来确定平台类型
        # 这里我们需要根据实际的授权码来确定平台并获取用户信息
        platform = None
        user_info = None
        
        # 获取第三方登录配置
        configs = await SystemConfigService.get_configs_by_type(db, project.id, "third_party_login")
        config_dict = {config.config_key: config.config_value for config in configs}
        
        # 尝试钉钉API
        dingtalk_config = config_dict.get("dingtalk", {})
        if dingtalk_config.get("enabled", False):
            try:
                app_key = dingtalk_config.get("app_key")
                app_secret = dingtalk_config.get("app_secret")
                
                logger.info(f"钉钉配置: enabled={dingtalk_config.get('enabled')}, app_key={app_key[:10] if app_key else None}...")
                
                if app_key and app_secret:
                    # 导入钉钉API工具
                    from plugins.dingtalk.utils.dingtalk_api import DingTalkAPI
                    dingtalk_api = DingTalkAPI(app_key, app_secret)
                    
                    logger.info(f"开始调用钉钉API获取用户信息，auth_code: {auth_code[:10]}...")
                    user_info = await dingtalk_api.get_user_info_by_code(auth_code)
                    
                    if user_info:
                        platform = "dingtalk"
                        logger.info(f"成功通过钉钉API获取用户信息: {user_info}")
                    else:
                        logger.warning(f"钉钉API返回空用户信息")
                else:
                    logger.warning(f"钉钉配置不完整: app_key={bool(app_key)}, app_secret={bool(app_secret)}")
            except Exception as e:
                logger.error(f"钉钉API调用失败: {str(e)}", exc_info=True)
        else:
            logger.info(f"钉钉未启用或配置不存在: {dingtalk_config}")
        
        # 如果钉钉失败，尝试微信API
        if not platform:
            wechat_config = config_dict.get("wechat", {})
            if wechat_config.get("enabled", False):
                try:
                    # 这里可以添加微信API调用逻辑
                    # wechat_user_info = await get_wechat_user_info(auth_code)
                    pass
                except Exception as e:
                    logger.warning(f"微信API调用失败: {str(e)}")
        
        # 如果无法确定平台，返回错误
        if not platform or not user_info:
            logger.error(f"无法确定平台类型或获取用户信息，auth_code: {auth_code}")
            error_html = """
            <!DOCTYPE html>
            <html>
            <head>
                <title>绑定失败</title>
                <meta charset="utf-8">
            </head>
            <body>
                <script>
                    if (window.opener) {
                        window.opener.postMessage({
                            type: 'BIND_ERROR',
                            error: 'platform_unknown',
                            message: '无法确定平台类型或获取用户信息'
                        }, '*');
                        window.close();
                    } else {
                        window.location.href = '/user/profile?bind_error=platform_unknown';
                    }
                </script>
                <div style="text-align: center; padding: 50px; font-family: Arial, sans-serif;">
                    <h2>❌ 绑定失败</h2>
                    <p>无法确定平台类型或获取用户信息，请重试。</p>
                </div>
            </body>
            </html>
            """
            return HTMLResponse(content=error_html, status_code=400)
        
        # 检查是否已存在绑定记录
        result = await db.execute(select(ThirdPartyAccount).where(
            ThirdPartyAccount.user_id == user.id,
            ThirdPartyAccount.platform == platform
        ))
        existing_account = result.scalars().first()
        
        # 从API响应中提取用户信息
        platform_user_id = user_info.get("openid") or user_info.get("unionid") or user_info.get("userid")
        platform_username = user_info.get("nick") or user_info.get("name") or user_info.get("nickname", f"{platform}用户")
        avatar_url = user_info.get("avatarUrl") or user_info.get("avatar")
        
        if existing_account:
            # 更新现有绑定
            existing_account.platform_user_id = platform_user_id
            existing_account.platform_username = platform_username
            existing_account.avatar_url = avatar_url
            existing_account.updated_at = datetime.now()
        else:
            # 创建新的绑定记录
            new_account = ThirdPartyAccount(
                id=uuid.uuid4(),
                user_id=user.id,
                platform=platform,
                platform_user_id=platform_user_id,
                platform_username=platform_username,
                avatar_url=avatar_url,
                created_at=datetime.now(),
                updated_at=datetime.now()
            )
            db.add(new_account)
        
        # 添加活动记录
        activity = UserActivity(
            user_id=user.id,
            project_id=project.id,
            activity_type="third_party_bind",
            description=f"绑定了{platform}账号",
            created_at=datetime.now()
        )
        db.add(activity)
        await db.commit()
        
        # 重定向到前端页面，并传递成功信息
        # 对于弹窗绑定，返回一个HTML页面来关闭弹窗并通知父页面
        success_html = f"""
        <!DOCTYPE html>
        <html>
        <head>
            <title>绑定成功</title>
            <meta charset="utf-8">
        </head>
        <body>
            <script>
                // 通知父页面绑定成功
                if (window.opener) {{
                    window.opener.postMessage({{
                        type: 'BIND_SUCCESS',
                        platform: '{platform}',
                        data: {{
                            platform_user_id: '{platform_user_id}',
                            platform_username: '{platform_username}',
                            avatar_url: '{avatar_url or ""}'
                        }}
                    }}, '*');
                    window.close();
                }} else {{
                    // 如果不是弹窗，则重定向到用户资料页面
                    window.location.href = '/user/profile?bind_success=true&platform={platform}';
                }}
            </script>
            <div style="text-align: center; padding: 50px; font-family: Arial, sans-serif;">
                <h2>✅ {platform}账号绑定成功！</h2>
                <p>正在关闭窗口...</p>
                <p>如果窗口没有自动关闭，请手动关闭此页面。</p>
            </div>
        </body>
        </html>
        """
        
        return HTMLResponse(content=success_html, status_code=200)
        
    except Exception as e:
        logger.error(f"绑定回调处理失败: {str(e)}")
        error_html = f"""
        <!DOCTYPE html>
        <html>
        <head>
            <title>绑定失败</title>
            <meta charset="utf-8">
        </head>
        <body>
            <script>
                if (window.opener) {{
                    window.opener.postMessage({{
                        type: 'BIND_ERROR',
                        error: 'callback_failed',
                        message: '绑定回调处理失败: {str(e)}'
                    }}, '*');
                    window.close();
                }} else {{
                    window.location.href = '/user/profile?bind_error=callback_failed';
                }}
            </script>
            <div style="text-align: center; padding: 50px; font-family: Arial, sans-serif;">
                <h2>❌ 绑定失败</h2>
                <p>绑定回调处理失败，请重试。</p>
                <p style="color: #666; font-size: 12px;">错误详情: {str(e)}</p>
            </div>
        </body>
        </html>
        """
        return HTMLResponse(content=error_html, status_code=500)
