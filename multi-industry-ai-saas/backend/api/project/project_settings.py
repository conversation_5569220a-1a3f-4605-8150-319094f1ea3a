#!/usr/bin/env python
# -*- coding: utf-8 -*-

import logging
from typing import Dict, Any, Optional
from fastapi import APIRouter, Depends, HTTPException, status, Path
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from sqlalchemy import update, and_
import uuid

from api.deps import get_db
from models.project import Project
from models.project_user import ProjectUser
from models.user import User
from core.auth import get_current_user
from schemas.project import ProjectSettingsUpdate

# 配置日志
logger = logging.getLogger(__name__)

# 创建路由器
router = APIRouter()


@router.get("/settings", response_model=Dict[str, Any])
async def get_project_settings(
    project_id: uuid.UUID = Path(...),
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user),
):
    """
    获取项目设置
    """
    try:
        logger.info(f"开始获取项目设置，项目ID: {project_id}")

        # 直接使用SQL查询项目，避免ORM模型关系问题
        try:
            from sqlalchemy import text
            result = await db.execute(
                text("SELECT id, tenant_id, name, settings FROM projects WHERE id = :project_id"),
                {"project_id": str(project_id)}
            )
            project = result.fetchone()
        except Exception as db_error:
            logger.error(f"查询项目数据库错误: {db_error}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"查询项目数据库错误: {str(db_error)}",
            )

        if not project:
            logger.warning(f"项目不存在，项目ID: {project_id}")
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="未找到项目信息",
            )

        # 检查权限
        if not current_user.is_system_admin and not current_user.is_service_provider_admin:
            if not current_user.tenant_id or str(current_user.tenant_id) != str(project.tenant_id):
                logger.warning(f"用户 {current_user.id} 尝试访问不属于其租户的项目设置")
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail="没有权限访问此项目设置",
                )

            # 如果不是租户管理员，检查是否有项目权限
            if not current_user.is_tenant_admin:
                try:
                    result = await db.execute(
                        text("""
                            SELECT id FROM project_users
                            WHERE project_id = :project_id AND user_id = :user_id
                        """),
                        {"project_id": str(project_id), "user_id": str(current_user.id)}
                    )
                    project_user = result.fetchone()
                except Exception as db_error:
                    logger.error(f"查询项目用户关系数据库错误: {db_error}")
                    raise HTTPException(
                        status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                        detail=f"查询用户权限时发生错误: {str(db_error)}",
                    )

                if not project_user:
                    logger.warning(f"用户 {current_user.id} 没有项目 {project_id} 的访问权限")
                    raise HTTPException(
                        status_code=status.HTTP_403_FORBIDDEN,
                        detail="没有权限访问此项目设置",
                    )

        # 检查项目设置是否为None
        if project.settings is None:
            logger.info(f"项目 {project_id} 的设置为None，返回空字典")
            return {}

        # 返回项目设置
        logger.info(f"成功获取项目 {project_id} 的设置")
        return project.settings
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取项目设置失败，未预期的错误: {e}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取项目设置失败: {str(e)}",
        )


@router.put("/settings", response_model=Dict[str, Any])
async def update_project_settings(
    project_id: uuid.UUID = Path(...),
    settings: ProjectSettingsUpdate = None,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user),
):
    """
    更新项目设置
    """
    try:
        logger.info(f"开始更新项目设置，项目ID: {project_id}")

        # 直接使用SQL查询项目，避免ORM模型关系问题
        try:
            from sqlalchemy import text
            result = await db.execute(
                text("SELECT id, tenant_id, name, settings FROM projects WHERE id = :project_id"),
                {"project_id": str(project_id)}
            )
            project = result.fetchone()
        except Exception as db_error:
            logger.error(f"查询项目数据库错误: {db_error}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"查询项目数据库错误: {str(db_error)}",
            )

        if not project:
            logger.warning(f"项目不存在，项目ID: {project_id}")
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="未找到项目信息",
            )

        # 检查权限
        if not current_user.is_system_admin and not current_user.is_service_provider_admin:
            if not current_user.tenant_id or str(current_user.tenant_id) != str(project.tenant_id):
                logger.warning(f"用户 {current_user.id} 尝试更新不属于其租户的项目设置")
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail="没有权限更新此项目设置",
                )

            # 如果不是租户管理员，检查是否有项目管理权限
            if not current_user.is_tenant_admin:
                try:
                    result = await db.execute(
                        text("""
                            SELECT id FROM project_users
                            WHERE project_id = :project_id AND user_id = :user_id AND is_admin = true
                        """),
                        {"project_id": str(project_id), "user_id": str(current_user.id)}
                    )
                    project_admin = result.fetchone()
                except Exception as db_error:
                    logger.error(f"查询项目管理员关系数据库错误: {db_error}")
                    raise HTTPException(
                        status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                        detail=f"查询用户权限时发生错误: {str(db_error)}",
                    )

                if not project_admin:
                    logger.warning(f"用户 {current_user.id} 没有项目 {project_id} 的管理权限")
                    raise HTTPException(
                        status_code=status.HTTP_403_FORBIDDEN,
                        detail="没有权限更新此项目设置",
                    )

        # 更新项目设置
        current_settings = project.settings or {}
        new_settings = settings.dict(exclude_unset=True)

        # 合并设置
        for key, value in new_settings.items():
            if value is not None:
                if key in current_settings and isinstance(current_settings[key], dict) and isinstance(value, dict):
                    # 如果是嵌套字典，递归合并
                    current_settings[key].update(value)
                else:
                    # 否则直接替换
                    current_settings[key] = value

        # 更新数据库
        try:
            await db.execute(
                text("""
                    UPDATE projects
                    SET settings = :settings
                    WHERE id = :project_id
                """),
                {
                    "project_id": str(project_id),
                    "settings": current_settings
                }
            )
            await db.commit()
            logger.info(f"项目设置更新成功: {project_id}")
        except Exception as db_error:
            await db.rollback()
            logger.error(f"更新项目设置数据库错误: {db_error}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"更新项目设置数据库错误: {str(db_error)}",
            )

        # 重新查询项目
        try:
            result = await db.execute(
                text("SELECT settings FROM projects WHERE id = :project_id"),
                {"project_id": str(project_id)}
            )
            updated_project = result.fetchone()

            if not updated_project:
                logger.warning(f"更新后项目不存在，项目ID: {project_id}")
                return current_settings

            return updated_project.settings or {}
        except Exception as e:
            logger.error(f"重新查询项目失败，未预期的错误: {e}", exc_info=True)
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"重新查询项目失败: {str(e)}",
            )
    except HTTPException:
        raise
    except Exception as e:
        await db.rollback()
        logger.error(f"更新项目设置失败，未预期的错误: {e}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"更新项目设置失败: {str(e)}",
        )
