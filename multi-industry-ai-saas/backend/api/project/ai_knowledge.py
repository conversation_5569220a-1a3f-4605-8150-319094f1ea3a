#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
项目级 AI 知识库管理 API
"""

import logging
import uuid
from typing import Optional, List, Dict, Any
from fastapi import APIRouter, Depends, HTTPException, Query, Path, status, UploadFile, File, Form
from sqlalchemy.ext.asyncio import AsyncSession

from db.database import get_db
from models.project import Project
from core.auth import get_current_user
from api.deps import get_current_project
from services.ai import AIKnowledgeService
from schemas.ai import (
    AIKnowledgeBaseCreate,
    AIKnowledgeBaseUpdate,
    AIKnowledgeBaseResponse,
    AIKnowledgeBaseListResponse,
    AIKnowledgeDocumentCreate,
    AIKnowledgeDocumentUpdate,
    AIKnowledgeDocumentResponse,
    AIKnowledgeDocumentListResponse,
)

logger = logging.getLogger(__name__)
router = APIRouter()

@router.get("/ai/knowledge/bases", response_model=AIKnowledgeBaseListResponse)
async def list_knowledge_bases(
    project: Project = Depends(get_current_project),
    db: AsyncSession = Depends(get_db),
    skip: int = Query(0, ge=0),
    limit: int = Query(20, ge=1, le=100),
    industry_type: Optional[str] = Query(None),
    kb_status: Optional[str] = Query(None, alias="status"),
):
    """
    获取 AI 知识库列表
    """
    try:
        knowledge_bases, total = await AIKnowledgeService.get_knowledge_bases(
            db=db,
            skip=skip,
            limit=limit,
            tenant_id=project.tenant_id,
            project_id=project.id,
            industry_type=industry_type,
            status=kb_status,
        )

        return {
            "success": True,
            "message": "获取 AI 知识库列表成功",
            "data": knowledge_bases,
            "total": total,
            "page": skip // limit + 1,
            "page_size": limit,
        }
    except Exception as e:
        logger.error(f"获取 AI 知识库列表失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取 AI 知识库列表失败: {str(e)}",
        )

@router.get("/ai/knowledge/bases/{knowledge_base_id}", response_model=AIKnowledgeBaseResponse)
async def get_knowledge_base(
    knowledge_base_id: uuid.UUID = Path(...),
    project: Project = Depends(get_current_project),
    db: AsyncSession = Depends(get_db),
):
    """
    获取 AI 知识库详情
    """
    try:
        knowledge_base = await AIKnowledgeService.get_knowledge_base(db=db, knowledge_base_id=knowledge_base_id)

        if not knowledge_base:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"AI 知识库 ID '{knowledge_base_id}' 不存在",
            )

        # 检查是否有权限访问该知识库
        if knowledge_base.project_id != project.id and knowledge_base.tenant_id != project.tenant_id:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="无权访问该 AI 知识库",
            )

        return {
            "success": True,
            "message": "获取 AI 知识库详情成功",
            "data": knowledge_base,
        }
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取 AI 知识库详情失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取 AI 知识库详情失败: {str(e)}",
        )

@router.post("/ai/knowledge/bases", response_model=AIKnowledgeBaseResponse, status_code=status.HTTP_201_CREATED)
async def create_knowledge_base(
    knowledge_base_data: AIKnowledgeBaseCreate,
    project: Project = Depends(get_current_project),
    current_user: dict = Depends(get_current_user),
    db: AsyncSession = Depends(get_db),
):
    """
    创建 AI 知识库

    注意：只有项目管理员才能创建 AI 知识库
    """
    try:
        # 检查权限
        if not current_user.is_system_admin and not current_user.is_tenant_admin:
            # 检查是否为项目管理员
            if not any(pu.is_admin for pu in project.project_users if pu.user_id == current_user.id):
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail="只有项目管理员才能创建 AI 知识库",
                )

        # 设置项目 ID 和租户 ID
        knowledge_base_data.project_id = project.id
        knowledge_base_data.tenant_id = project.tenant_id

        knowledge_base = await AIKnowledgeService.create_knowledge_base(db=db, knowledge_base_data=knowledge_base_data)

        return {
            "success": True,
            "message": "创建 AI 知识库成功",
            "data": knowledge_base,
        }
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e),
        )
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"创建 AI 知识库失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"创建 AI 知识库失败: {str(e)}",
        )

@router.put("/ai/knowledge/bases/{knowledge_base_id}", response_model=AIKnowledgeBaseResponse)
async def update_knowledge_base(
    knowledge_base_id: uuid.UUID = Path(...),
    knowledge_base_data: AIKnowledgeBaseUpdate = None,
    project: Project = Depends(get_current_project),
    current_user: dict = Depends(get_current_user),
    db: AsyncSession = Depends(get_db),
):
    """
    更新 AI 知识库

    注意：只有项目管理员才能更新 AI 知识库
    """
    try:
        # 获取知识库
        knowledge_base = await AIKnowledgeService.get_knowledge_base(db=db, knowledge_base_id=knowledge_base_id)

        if not knowledge_base:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"AI 知识库 ID '{knowledge_base_id}' 不存在",
            )

        # 检查是否有权限更新该知识库
        if knowledge_base.project_id != project.id:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="无权更新该 AI 知识库",
            )

        # 检查权限
        if not current_user.is_system_admin and not current_user.is_tenant_admin:
            # 检查是否为项目管理员
            if not any(pu.is_admin for pu in project.project_users if pu.user_id == current_user.id):
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail="只有项目管理员才能更新 AI 知识库",
                )

        knowledge_base = await AIKnowledgeService.update_knowledge_base(
            db=db,
            knowledge_base_id=knowledge_base_id,
            knowledge_base_data=knowledge_base_data,
        )

        return {
            "success": True,
            "message": "更新 AI 知识库成功",
            "data": knowledge_base,
        }
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e),
        )
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"更新 AI 知识库失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"更新 AI 知识库失败: {str(e)}",
        )

@router.delete("/ai/knowledge/bases/{knowledge_base_id}", response_model=AIKnowledgeBaseResponse)
async def delete_knowledge_base(
    knowledge_base_id: uuid.UUID = Path(...),
    project: Project = Depends(get_current_project),
    current_user: dict = Depends(get_current_user),
    db: AsyncSession = Depends(get_db),
):
    """
    删除 AI 知识库

    注意：只有项目管理员才能删除 AI 知识库
    """
    try:
        # 获取知识库
        knowledge_base = await AIKnowledgeService.get_knowledge_base(db=db, knowledge_base_id=knowledge_base_id)

        if not knowledge_base:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"AI 知识库 ID '{knowledge_base_id}' 不存在",
            )

        # 检查是否有权限删除该知识库
        if knowledge_base.project_id != project.id:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="无权删除该 AI 知识库",
            )

        # 检查权限
        if not current_user.is_system_admin and not current_user.is_tenant_admin:
            # 检查是否为项目管理员
            if not any(pu.is_admin for pu in project.project_users if pu.user_id == current_user.id):
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail="只有项目管理员才能删除 AI 知识库",
                )

        success = await AIKnowledgeService.delete_knowledge_base(db=db, knowledge_base_id=knowledge_base_id)

        if not success:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"AI 知识库 ID '{knowledge_base_id}' 不存在",
            )

        return {
            "success": True,
            "message": "删除 AI 知识库成功",
        }
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"删除 AI 知识库失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"删除 AI 知识库失败: {str(e)}",
        )

@router.get("/ai/knowledge/bases/{knowledge_base_id}/documents", response_model=AIKnowledgeDocumentListResponse)
async def list_documents(
    knowledge_base_id: uuid.UUID = Path(...),
    project: Project = Depends(get_current_project),
    db: AsyncSession = Depends(get_db),
    skip: int = Query(0, ge=0),
    limit: int = Query(20, ge=1, le=100),
    embedding_status: Optional[str] = Query(None),
):
    """
    获取 AI 知识文档列表
    """
    try:
        # 获取知识库
        knowledge_base = await AIKnowledgeService.get_knowledge_base(db=db, knowledge_base_id=knowledge_base_id)

        if not knowledge_base:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"AI 知识库 ID '{knowledge_base_id}' 不存在",
            )

        # 检查是否有权限访问该知识库
        if knowledge_base.project_id != project.id and knowledge_base.tenant_id != project.tenant_id:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="无权访问该 AI 知识库",
            )

        documents, total = await AIKnowledgeService.get_documents(
            db=db,
            knowledge_base_id=knowledge_base_id,
            skip=skip,
            limit=limit,
            embedding_status=embedding_status,
        )

        return {
            "success": True,
            "message": "获取 AI 知识文档列表成功",
            "data": documents,
            "total": total,
            "page": skip // limit + 1,
            "page_size": limit,
        }
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取 AI 知识文档列表失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取 AI 知识文档列表失败: {str(e)}",
        )

@router.get("/ai/documents/{document_id}", response_model=AIKnowledgeDocumentResponse)
async def get_document(
    document_id: uuid.UUID = Path(...),
    project: Project = Depends(get_current_project),
    db: AsyncSession = Depends(get_db),
):
    """
    获取 AI 知识文档详情
    """
    try:
        document = await AIKnowledgeService.get_document(db=db, document_id=document_id)

        if not document:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"AI 知识文档 ID '{document_id}' 不存在",
            )

        # 获取知识库
        knowledge_base = await AIKnowledgeService.get_knowledge_base(db=db, knowledge_base_id=document.knowledge_base_id)

        if not knowledge_base:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"AI 知识库 ID '{document.knowledge_base_id}' 不存在",
            )

        # 检查是否有权限访问该知识库
        if knowledge_base.project_id != project.id and knowledge_base.tenant_id != project.tenant_id:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="无权访问该 AI 知识库",
            )

        return {
            "success": True,
            "message": "获取 AI 知识文档详情成功",
            "data": document,
        }
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取 AI 知识文档详情失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取 AI 知识文档详情失败: {str(e)}",
        )

@router.post("/ai/knowledge/bases/{knowledge_base_id}/documents", response_model=AIKnowledgeDocumentResponse, status_code=status.HTTP_201_CREATED)
async def create_document(
    knowledge_base_id: uuid.UUID = Path(...),
    document_data: AIKnowledgeDocumentCreate = None,
    project: Project = Depends(get_current_project),
    current_user: dict = Depends(get_current_user),
    db: AsyncSession = Depends(get_db),
):
    """
    创建 AI 知识文档
    """
    try:
        # 获取知识库
        knowledge_base = await AIKnowledgeService.get_knowledge_base(db=db, knowledge_base_id=knowledge_base_id)

        if not knowledge_base:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"AI 知识库 ID '{knowledge_base_id}' 不存在",
            )

        # 检查是否有权限访问该知识库
        if knowledge_base.project_id != project.id:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="无权访问该 AI 知识库",
            )

        # 设置知识库 ID
        document_data.knowledge_base_id = knowledge_base_id

        document = await AIKnowledgeService.create_document(db=db, document_data=document_data)

        return {
            "success": True,
            "message": "创建 AI 知识文档成功",
            "data": document,
        }
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e),
        )
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"创建 AI 知识文档失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"创建 AI 知识文档失败: {str(e)}",
        )

@router.put("/ai/documents/{document_id}", response_model=AIKnowledgeDocumentResponse)
async def update_document(
    document_id: uuid.UUID = Path(...),
    document_data: AIKnowledgeDocumentUpdate = None,
    project: Project = Depends(get_current_project),
    current_user: dict = Depends(get_current_user),
    db: AsyncSession = Depends(get_db),
):
    """
    更新 AI 知识文档
    """
    try:
        # 获取文档
        document = await AIKnowledgeService.get_document(db=db, document_id=document_id)

        if not document:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"AI 知识文档 ID '{document_id}' 不存在",
            )

        # 获取知识库
        knowledge_base = await AIKnowledgeService.get_knowledge_base(db=db, knowledge_base_id=document.knowledge_base_id)

        if not knowledge_base:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"AI 知识库 ID '{document.knowledge_base_id}' 不存在",
            )

        # 检查是否有权限访问该知识库
        if knowledge_base.project_id != project.id:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="无权访问该 AI 知识库",
            )

        document = await AIKnowledgeService.update_document(
            db=db,
            document_id=document_id,
            document_data=document_data,
        )

        return {
            "success": True,
            "message": "更新 AI 知识文档成功",
            "data": document,
        }
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e),
        )
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"更新 AI 知识文档失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"更新 AI 知识文档失败: {str(e)}",
        )

@router.delete("/ai/documents/{document_id}", response_model=AIKnowledgeDocumentResponse)
async def delete_document(
    document_id: uuid.UUID = Path(...),
    project: Project = Depends(get_current_project),
    current_user: dict = Depends(get_current_user),
    db: AsyncSession = Depends(get_db),
):
    """
    删除 AI 知识文档
    """
    try:
        # 获取文档
        document = await AIKnowledgeService.get_document(db=db, document_id=document_id)

        if not document:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"AI 知识文档 ID '{document_id}' 不存在",
            )

        # 获取知识库
        knowledge_base = await AIKnowledgeService.get_knowledge_base(db=db, knowledge_base_id=document.knowledge_base_id)

        if not knowledge_base:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"AI 知识库 ID '{document.knowledge_base_id}' 不存在",
            )

        # 检查是否有权限访问该知识库
        if knowledge_base.project_id != project.id:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="无权访问该 AI 知识库",
            )

        success = await AIKnowledgeService.delete_document(db=db, document_id=document_id)

        if not success:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"AI 知识文档 ID '{document_id}' 不存在",
            )

        return {
            "success": True,
            "message": "删除 AI 知识文档成功",
        }
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"删除 AI 知识文档失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"删除 AI 知识文档失败: {str(e)}",
        )

@router.post("/ai/knowledge/bases/{knowledge_base_id}/upload", response_model=AIKnowledgeDocumentResponse)
async def upload_knowledge_document(
    knowledge_base_id: uuid.UUID = Path(...),
    file: UploadFile = File(...),
    title: str = Form(...),
    description: Optional[str] = Form(None),
    project: Project = Depends(get_current_project),
    current_user: dict = Depends(get_current_user),
    db: AsyncSession = Depends(get_db),
):
    """
    上传 AI 知识库文档
    """
    try:
        # 获取知识库
        knowledge_base = await AIKnowledgeService.get_knowledge_base(db=db, knowledge_base_id=knowledge_base_id)

        if not knowledge_base:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"AI 知识库 ID '{knowledge_base_id}' 不存在",
            )

        # 检查是否有权限访问该知识库
        if knowledge_base.project_id != project.id:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="无权访问该 AI 知识库",
            )

        # 使用项目统一上传模块上传文件
        # 为 AI 知识库文档创建专门的文件夹路径
        folder_path = f"/ai/knowledge/{knowledge_base_id}"

        # 上传文件
        from services.storage_service import StorageService
        upload_result = await StorageService.upload_file(
            db=db,
            file=file,
            project_id=project.id,
            tenant_id=project.tenant_id,
            user_id=current_user.id,
            folder_path=folder_path,
            description=description,
            is_public=False
        )

        if not upload_result["success"]:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"文件上传失败: {upload_result.get('message', '未知错误')}",
            )

        # 创建知识库文档
        file_data = upload_result["data"]
        document_data = {
            "knowledge_base_id": knowledge_base_id,
            "title": title,
            "description": description,
            "file_id": file_data["id"],
            "file_name": file_data["name"],
            "file_size": file_data["size"],
            "file_type": file_data["mime_type"],
            "status": "pending"
        }

        # 创建文档
        document = await AIKnowledgeService.create_document(
            db=db,
            document_data=document_data
        )

        return {
            "success": True,
            "message": "上传 AI 知识库文档成功",
            "data": document,
        }
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"上传 AI 知识库文档失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"上传 AI 知识库文档失败: {str(e)}",
        )

@router.post("/ai/documents/{document_id}/process", response_model=AIKnowledgeDocumentResponse)
async def process_document(
    document_id: uuid.UUID = Path(...),
    chunk_size: int = Query(1000, ge=100, le=5000),
    chunk_overlap: int = Query(200, ge=0, le=1000),
    project: Project = Depends(get_current_project),
    current_user: dict = Depends(get_current_user),
    db: AsyncSession = Depends(get_db),
):
    """
    处理文档，生成文本块和嵌入向量
    """
    try:
        # 获取文档
        document = await AIKnowledgeService.get_document(db=db, document_id=document_id)

        if not document:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"AI 知识文档 ID '{document_id}' 不存在",
            )

        # 获取知识库
        knowledge_base = await AIKnowledgeService.get_knowledge_base(db=db, knowledge_base_id=document.knowledge_base_id)

        if not knowledge_base:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"AI 知识库 ID '{document.knowledge_base_id}' 不存在",
            )

        # 检查是否有权限访问该知识库
        if knowledge_base.project_id != project.id:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="无权访问该 AI 知识库",
            )

        # 处理文档
        await AIKnowledgeService.process_document(
            db=db,
            document_id=document_id,
            user_id=current_user.id,
            chunk_size=chunk_size,
            chunk_overlap=chunk_overlap,
        )

        # 获取更新后的文档
        document = await AIKnowledgeService.get_document(db=db, document_id=document_id)

        return {
            "success": True,
            "message": "处理 AI 知识文档成功",
            "data": document,
        }
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e),
        )
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"处理 AI 知识文档失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"处理 AI 知识文档失败: {str(e)}",
        )
