from typing import Optional, List
import uuid
from fastapi import APIRouter, Depends, HTTPException, Query, Path, status
from sqlalchemy.ext.asyncio import AsyncSession

from api.deps import get_db, get_current_user, get_current_project
from models.user import User
from models.project import Project
from schemas.sales_management import (
    SalesChannelCreate,
    SalesChannelUpdate,
    SalesChannelResponse,
    SalesChannelListResponse,
    PaymentMethodCreate,
    PaymentMethodUpdate,
    PaymentMethodResponse,
    PaymentMethodListResponse
)
from services.sales_management import SalesChannelService, PaymentMethodService

router = APIRouter()

# 销售渠道API
@router.get("/channels", response_model=SalesChannelListResponse)
async def get_sales_channels(
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=1000),
    search: Optional[str] = None,
    type: Optional[str] = None,
    is_active: Optional[bool] = None,
    project: Project = Depends(get_current_project),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    获取销售渠道列表
    """
    try:
        # 获取销售渠道
        channels = await SalesChannelService.get_sales_channels(
            db=db,
            project_id=project.id,
            skip=skip,
            limit=limit,
            search=search,
            type=type,
            is_active=is_active
        )
        
        # 获取销售渠道总数
        total = await SalesChannelService.count_sales_channels(
            db=db,
            project_id=project.id,
            search=search,
            type=type,
            is_active=is_active
        )
        
        return {
            "items": channels,
            "total": total
        }
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取销售渠道列表失败: {str(e)}"
        )

@router.post("/channels", response_model=SalesChannelResponse, status_code=status.HTTP_201_CREATED)
async def create_sales_channel(
    channel_data: SalesChannelCreate,
    project: Project = Depends(get_current_project),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    创建销售渠道
    """
    try:
        # 创建销售渠道
        channel = await SalesChannelService.create_sales_channel(
            db=db,
            channel_data=channel_data.dict(),
            project_id=project.id
        )
        
        return channel
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"创建销售渠道失败: {str(e)}"
        )

@router.get("/channels/{channel_id}", response_model=SalesChannelResponse)
async def get_sales_channel(
    channel_id: uuid.UUID = Path(...),
    project: Project = Depends(get_current_project),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    获取销售渠道详情
    """
    try:
        # 获取销售渠道
        channel = await SalesChannelService.get_sales_channel_by_id(db, channel_id)
        if not channel:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="销售渠道不存在"
            )
        
        # 检查销售渠道是否属于当前项目
        if channel.project_id != project.id:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="无权访问此销售渠道"
            )
        
        return channel
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取销售渠道详情失败: {str(e)}"
        )

@router.put("/channels/{channel_id}", response_model=SalesChannelResponse)
async def update_sales_channel(
    channel_data: SalesChannelUpdate,
    channel_id: uuid.UUID = Path(...),
    project: Project = Depends(get_current_project),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    更新销售渠道
    """
    try:
        # 获取销售渠道
        channel = await SalesChannelService.get_sales_channel_by_id(db, channel_id)
        if not channel:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="销售渠道不存在"
            )
        
        # 检查销售渠道是否属于当前项目
        if channel.project_id != project.id:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="无权修改此销售渠道"
            )
        
        # 更新销售渠道
        updated_channel = await SalesChannelService.update_sales_channel(
            db=db,
            channel_id=channel_id,
            channel_data=channel_data.dict(exclude_unset=True)
        )
        
        return updated_channel
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"更新销售渠道失败: {str(e)}"
        )

@router.delete("/channels/{channel_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_sales_channel(
    channel_id: uuid.UUID = Path(...),
    project: Project = Depends(get_current_project),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    删除销售渠道
    """
    try:
        # 获取销售渠道
        channel = await SalesChannelService.get_sales_channel_by_id(db, channel_id)
        if not channel:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="销售渠道不存在"
            )
        
        # 检查销售渠道是否属于当前项目
        if channel.project_id != project.id:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="无权删除此销售渠道"
            )
        
        # 删除销售渠道
        success = await SalesChannelService.delete_sales_channel(db, channel_id)
        if not success:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="删除销售渠道失败"
            )
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"删除销售渠道失败: {str(e)}"
        )

# 支付方式API
@router.get("/payment-methods", response_model=PaymentMethodListResponse)
async def get_payment_methods(
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=1000),
    search: Optional[str] = None,
    is_active: Optional[bool] = None,
    is_default: Optional[bool] = None,
    project: Project = Depends(get_current_project),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    获取支付方式列表
    """
    try:
        # 获取支付方式
        payment_methods = await PaymentMethodService.get_payment_methods(
            db=db,
            project_id=project.id,
            skip=skip,
            limit=limit,
            search=search,
            is_active=is_active,
            is_default=is_default
        )
        
        # 获取支付方式总数
        total = await PaymentMethodService.count_payment_methods(
            db=db,
            project_id=project.id,
            search=search,
            is_active=is_active,
            is_default=is_default
        )
        
        return {
            "items": payment_methods,
            "total": total
        }
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取支付方式列表失败: {str(e)}"
        )

@router.post("/payment-methods", response_model=PaymentMethodResponse, status_code=status.HTTP_201_CREATED)
async def create_payment_method(
    payment_method_data: PaymentMethodCreate,
    project: Project = Depends(get_current_project),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    创建支付方式
    """
    try:
        # 创建支付方式
        payment_method = await PaymentMethodService.create_payment_method(
            db=db,
            payment_method_data=payment_method_data.dict(),
            project_id=project.id
        )
        
        return payment_method
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"创建支付方式失败: {str(e)}"
        )

@router.get("/payment-methods/{payment_method_id}", response_model=PaymentMethodResponse)
async def get_payment_method(
    payment_method_id: uuid.UUID = Path(...),
    project: Project = Depends(get_current_project),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    获取支付方式详情
    """
    try:
        # 获取支付方式
        payment_method = await PaymentMethodService.get_payment_method_by_id(db, payment_method_id)
        if not payment_method:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="支付方式不存在"
            )
        
        # 检查支付方式是否属于当前项目
        if payment_method.project_id != project.id:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="无权访问此支付方式"
            )
        
        return payment_method
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取支付方式详情失败: {str(e)}"
        )

@router.put("/payment-methods/{payment_method_id}", response_model=PaymentMethodResponse)
async def update_payment_method(
    payment_method_data: PaymentMethodUpdate,
    payment_method_id: uuid.UUID = Path(...),
    project: Project = Depends(get_current_project),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    更新支付方式
    """
    try:
        # 获取支付方式
        payment_method = await PaymentMethodService.get_payment_method_by_id(db, payment_method_id)
        if not payment_method:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="支付方式不存在"
            )
        
        # 检查支付方式是否属于当前项目
        if payment_method.project_id != project.id:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="无权修改此支付方式"
            )
        
        # 更新支付方式
        updated_payment_method = await PaymentMethodService.update_payment_method(
            db=db,
            payment_method_id=payment_method_id,
            payment_method_data=payment_method_data.dict(exclude_unset=True)
        )
        
        return updated_payment_method
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"更新支付方式失败: {str(e)}"
        )

@router.delete("/payment-methods/{payment_method_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_payment_method(
    payment_method_id: uuid.UUID = Path(...),
    project: Project = Depends(get_current_project),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    删除支付方式
    """
    try:
        # 获取支付方式
        payment_method = await PaymentMethodService.get_payment_method_by_id(db, payment_method_id)
        if not payment_method:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="支付方式不存在"
            )
        
        # 检查支付方式是否属于当前项目
        if payment_method.project_id != project.id:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="无权删除此支付方式"
            )
        
        # 删除支付方式
        success = await PaymentMethodService.delete_payment_method(db, payment_method_id)
        if not success:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="删除支付方式失败"
            )
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"删除支付方式失败: {str(e)}"
        )
