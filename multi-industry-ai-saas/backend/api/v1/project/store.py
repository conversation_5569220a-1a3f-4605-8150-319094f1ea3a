from typing import Dict, Any, List, Optional
from fastapi import APIRouter, Depends, HTTPException, status, Query, Path, Body
from sqlalchemy.orm import Session, selectinload
from uuid import UUID
from sqlalchemy import select
from pydantic import BaseModel, UUID4

from db.database import get_db
from core.auth import get_current_user
from api.deps import get_current_project
from services.store import StoreService, StoreCategoryService, StoreRegionService, ProjectUserStoreService
from models.user import User
from schemas.store import (
    Store, StoreCreate, StoreUpdate, StoreList,
    StoreCategory, StoreCategoryCreate, StoreCategoryUpdate,
    StoreRegion, StoreRegionCreate, StoreRegionUpdate, StoreDetail, StoreRegionSimple
)
from models.project import Project
from models.project_user import ProjectUser
from models.store import StoreRegion as StoreRegionModel

router = APIRouter()

# 门店分类相关路由
@router.get("/store-categories", response_model=List[StoreCategory])
async def get_store_categories(
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=1000),
    project: Project = Depends(get_current_project),
    db: Session = Depends(get_db)
):
    """获取门店分类列表"""
    return await StoreCategoryService.get_categories(db, project.id, skip, limit)

@router.get("/store-categories/{category_id}", response_model=StoreCategory)
async def get_store_category(
    category_id: UUID = Path(...),
    project: Project = Depends(get_current_project),
    db: Session = Depends(get_db)
):
    """获取门店分类详情"""
    category = await StoreCategoryService.get_category_by_id(db, category_id)
    if not category or category.project_id != project.id:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="门店分类不存在"
        )
    return category

@router.post("/store-categories", response_model=StoreCategory, status_code=status.HTTP_201_CREATED)
async def create_store_category(
    category: StoreCategoryCreate,
    project: Project = Depends(get_current_project),
    db: Session = Depends(get_db)
):
    """创建门店分类"""
    # 检查分类编码是否已存在
    existing_category = await StoreCategoryService.get_category_by_code(db, project.id, category.code)
    if existing_category:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="门店分类编码已存在"
        )

    # 确保项目ID正确
    category.project_id = project.id

    return await StoreCategoryService.create_category(db, project.id, category)

@router.put("/store-categories/{category_id}", response_model=StoreCategory)
async def update_store_category(
    category_update: StoreCategoryUpdate,
    category_id: UUID = Path(...),
    project: Project = Depends(get_current_project),
    db: Session = Depends(get_db)
):
    """更新门店分类"""
    # 检查分类是否存在
    category = await StoreCategoryService.get_category_by_id(db, category_id)
    if not category or category.project_id != project.id:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="门店分类不存在"
        )

    # 检查分类编码是否已存在
    if category_update.code != category.code:
        existing_category = await StoreCategoryService.get_category_by_code(db, project.id, category_update.code)
        if existing_category:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="门店分类编码已存在"
            )

    updated_category = await StoreCategoryService.update_category(db, project.id, category_id, category_update)
    if not updated_category:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="更新门店分类失败"
        )
    return updated_category

@router.delete("/store-categories/{category_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_store_category(
    category_id: UUID = Path(...),
    project: Project = Depends(get_current_project),
    db: Session = Depends(get_db)
):
    """删除门店分类"""
    # 检查分类是否存在
    category = await StoreCategoryService.get_category_by_id(db, category_id)
    if not category or category.project_id != project.id:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="门店分类不存在"
        )

    success = await StoreCategoryService.delete_category(db, project.id, category_id)
    if not success:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="无法删除已被使用的门店分类"
        )
    return None

# 门店区域相关路由
@router.get("/store-regions", response_model=List[StoreRegionSimple])
async def get_store_regions(
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=1000),
    project: Project = Depends(get_current_project),
    db: Session = Depends(get_db)
):
    """获取门店区域列表"""
    return await StoreRegionService.get_regions(db, project.id, skip, limit)

@router.get("/store-regions/{region_id}", response_model=StoreRegionSimple)
async def get_store_region(
    region_id: UUID = Path(...),
    project: Project = Depends(get_current_project),
    db: Session = Depends(get_db)
):
    """获取门店区域详情"""
    region = await StoreRegionService.get_region_by_id(db, region_id)
    if not region or region.project_id != project.id:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="门店区域不存在"
        )
    return region

@router.post("/store-regions", response_model=StoreRegionSimple, status_code=status.HTTP_201_CREATED)
async def create_store_region(
    region: StoreRegionCreate,
    project: Project = Depends(get_current_project),
    db: Session = Depends(get_db)
):
    """创建门店区域"""
    # 检查区域编码是否已存在
    existing_region = await StoreRegionService.get_region_by_code(db, project.id, region.code)
    if existing_region:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="门店区域编码已存在"
        )

    # 强制赋值，确保 project_id 一致
    region.project_id = project.id

    # 如果指定了父级区域ID，检查父级区域是否存在
    if region.parent_id:
        parent_region = await StoreRegionService.get_region_by_id(db, region.parent_id)
        if not parent_region or parent_region.project_id != project.id:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="父级区域不存在"
            )
        # 注意：在创建新区域时，不需要检查循环引用，因为新区域还没有ID
        # 循环引用检查只在更新时需要

    # 创建区域（用SQLAlchemy模型）
    db_region = StoreRegionModel(
        project_id=project.id,
        parent_id=region.parent_id,
        name=region.name,
        code=region.code,
        description=region.description
    )
    db.add(db_region)
    await db.commit()
    await db.refresh(db_region)
    
    return db_region

@router.put("/store-regions/{region_id}", response_model=StoreRegionSimple)
async def update_store_region(
    region_update: StoreRegionUpdate,
    region_id: UUID = Path(...),
    project: Project = Depends(get_current_project),
    db: Session = Depends(get_db)
):
    """更新门店区域"""
    # 检查区域是否存在
    region = await StoreRegionService.get_region_by_id(db, region_id)
    if not region or region.project_id != project.id:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="门店区域不存在"
        )

    # 检查区域编码是否已存在
    if region_update.code != region.code:
        existing_region = await StoreRegionService.get_region_by_code(db, project.id, region_update.code)
        if existing_region:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="门店区域编码已存在"
            )

    # 如果指定了父级区域ID，检查父级区域是否存在
    if region_update.parent_id:
        # 不能将自己设为自己的父级
        if region_update.parent_id == region_id:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="不能将区域自身设为父级区域"
            )

        parent_region = await StoreRegionService.get_region_by_id(db, region_update.parent_id)
        if not parent_region or parent_region.project_id != project.id:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="父级区域不存在"
            )

        # 检查是否形成循环引用 - 不能将自己的子区域设为自己的父级
        # 获取所有子区域ID
        child_regions = await StoreRegionService.get_child_regions(db, region_id)
        child_ids = [child.id for child in child_regions]
        if region_update.parent_id in child_ids:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="不能将当前区域的子区域设为其父级区域"
            )

    # 更新区域
    region.name = region_update.name
    region.code = region_update.code
    region.description = region_update.description
    region.parent_id = region_update.parent_id

    await db.commit()
    await db.refresh(region)
    return region

@router.delete("/store-regions/{region_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_store_region(
    region_id: UUID = Path(...),
    project: Project = Depends(get_current_project),
    db: Session = Depends(get_db)
):
    """删除门店区域"""
    # 检查区域是否存在
    region = await StoreRegionService.get_region_by_id(db, region_id)
    if not region or region.project_id != project.id:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="门店区域不存在"
        )

    success = await StoreRegionService.delete_region(db, region_id)
    if not success:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="无法删除已被使用的门店区域"
        )
    return None

# 门店相关路由
@router.get("", response_model=StoreList)
async def get_stores(
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=1000),
    category_id: Optional[UUID] = None,
    region_id: Optional[UUID] = None,
    status: Optional[str] = None,
    project: Project = Depends(get_current_project),
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """获取门店列表"""
    # 检查用户是否是项目管理员或租户管理员
    is_admin = False

    # 查询用户在项目中的角色
    result = await db.execute(
        select(ProjectUser).where(
            ProjectUser.user_id == current_user.id,
            ProjectUser.project_id == project.id
        )
    )
    project_user = result.scalars().first()

    if project_user:
        is_admin = project_user.is_admin or current_user.is_tenant_admin

    # 如果是管理员，返回所有门店
    if is_admin:
        stores = await StoreService.get_stores(
            db, project.id, skip, limit, category_id, region_id, status
        )
        total = await StoreService.count_stores(
            db, project.id, category_id, region_id, status
        )
    else:
        # 如果不是管理员，只返回与用户关联的门店
        stores = await StoreService.get_user_stores(
            db, project.id, current_user.id, skip, limit, category_id, region_id, status
        )
        total = await StoreService.count_user_stores(
            db, project.id, current_user.id, category_id, region_id, status
        )

    # 为每个门店添加负责人信息
    store_items = []
    for store in stores:
        # 获取负责人信息
        manager_user = await ProjectUserStoreService.get_manager_user(db, store.id)
        
        # 创建门店数据字典
        store_data = store.__dict__.copy()
        store_data['category'] = store.category
        store_data['region'] = store.region
        
        # 添加负责人姓名
        if manager_user:
            store_data['manager'] = manager_user.full_name or manager_user.username
        
        store_items.append(store_data)

    return {
        "items": store_items,
        "total": total,
        "page": skip // limit + 1,
        "size": limit,
        "pages": (total + limit - 1) // limit
    }

@router.get("/{store_id}", response_model=StoreDetail)
async def get_store(
    store_id: UUID = Path(...),
    project: Project = Depends(get_current_project),
    db: Session = Depends(get_db)
):
    """获取门店详情"""
    store = await StoreService.get_store_by_id(db, store_id)
    if not store or store.project_id != project.id:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="门店不存在"
        )

    manager_project_user_id = await ProjectUserStoreService.get_manager_project_user_id(db, store_id)
    
    # 获取负责人信息
    manager_user = await ProjectUserStoreService.get_manager_user(db, store_id)
    manager_name = None
    if manager_user:
        manager_name = manager_user.full_name or manager_user.username

    # 手动构造响应字典，因为 FastAPI 不能自动合并 ORM 对象和额外字段
    store_data = store.__dict__.copy() # 获取 ORM 对象的基础属性
    # Pydantic orm_mode 需要关联对象，手动添加
    store_data['category'] = store.category
    store_data['region'] = store.region
    store_data['manager_user_id'] = manager_project_user_id
    # 添加负责人姓名
    if manager_name:
        store_data['manager'] = manager_name

    # 使用 StoreDetail 进行验证和返回
    return StoreDetail.parse_obj(store_data)

class StoreCreateWithManager(BaseModel):
    name: str
    code: str
    address: Optional[str] = None
    phone: Optional[str] = None
    manager: Optional[str] = None
    latitude: Optional[float] = None
    longitude: Optional[float] = None
    business_hours: Optional[str] = None
    status: str = "active"
    category_id: Optional[UUID4] = None
    region_id: Optional[UUID4] = None
    settings: Optional[Dict[str, Any]] = None
    manager_user_id: Optional[UUID4] = None

@router.post("", response_model=Store, status_code=status.HTTP_201_CREATED)
async def create_store(
    data: StoreCreateWithManager,
    project: Project = Depends(get_current_project),
    db: Session = Depends(get_db)
):
    """创建门店"""
    # 检查门店编码是否已存在
    existing_store = await StoreService.get_store_by_code(db, project.id, data.code)
    if existing_store:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="门店编码已存在"
        )
    # 构造 StoreCreate
    store = StoreCreate(
        name=data.name,
        code=data.code,
        address=data.address,
        phone=data.phone,
        manager=data.manager,
        latitude=data.latitude,
        longitude=data.longitude,
        business_hours=data.business_hours,
        status=data.status,
        category_id=data.category_id,
        region_id=data.region_id,
        settings=data.settings,
        project_id=project.id
    )
    # 分类、区域校验同原逻辑
    if store.category_id:
        category = await StoreCategoryService.get_category_by_id(db, store.category_id)
        if not category or category.project_id != project.id:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="门店分类不存在"
            )
    if store.region_id:
        region = await StoreRegionService.get_region_by_id(db, store.region_id)
        if not region or region.project_id != project.id:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="门店区域不存在"
            )
    db_store = await StoreService.create_store(db, store)

    # 如果提供了负责人ID，则设置负责人
    if data.manager_user_id:
        # 首先尝试作为project_user_id查找
        result = await db.execute(
            select(ProjectUser).where(
                ProjectUser.id == data.manager_user_id,
                ProjectUser.project_id == project.id
            )
        )
        project_user = result.scalars().first()
        
        # 如果没找到，再尝试作为user_id查找
        if not project_user:
            result = await db.execute(
                select(ProjectUser).where(
                    ProjectUser.user_id == data.manager_user_id,
                    ProjectUser.project_id == project.id
                )
            )
            project_user = result.scalars().first()
        
        if not project_user:
            raise HTTPException(status_code=400, detail="负责人用户未加入该项目")
        await ProjectUserStoreService.set_manager(db, db_store.id, project_user.id)

    return db_store

class StoreUpdateWithManager(BaseModel):
    name: str
    code: str
    address: Optional[str] = None
    phone: Optional[str] = None
    manager: Optional[str] = None
    latitude: Optional[float] = None
    longitude: Optional[float] = None
    business_hours: Optional[str] = None
    status: str = "active"
    category_id: Optional[UUID4] = None
    region_id: Optional[UUID4] = None
    settings: Optional[Dict[str, Any]] = None
    manager_user_id: Optional[UUID4] = None

@router.put("/{store_id}", response_model=Store)
async def update_store(
    data: StoreUpdateWithManager,
    store_id: UUID = Path(...),
    project: Project = Depends(get_current_project),
    db: Session = Depends(get_db)
):
    """更新门店"""
    # 检查门店是否存在
    store = await StoreService.get_store_by_id(db, store_id)
    if not store or store.project_id != project.id:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="门店不存在"
        )

    # 构造 StoreUpdate 对象
    store_update = StoreUpdate(
        name=data.name,
        code=data.code,
        address=data.address,
        phone=data.phone,
        manager=data.manager,
        latitude=data.latitude,
        longitude=data.longitude,
        business_hours=data.business_hours,
        status=data.status,
        category_id=data.category_id,
        region_id=data.region_id,
        settings=data.settings
    )

    # 检查门店编码是否已存在
    if store_update.code != store.code:
        existing_store = await StoreService.get_store_by_code(db, project.id, store_update.code)
        if existing_store:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="门店编码已存在"
            )

    # 如果指定了分类ID，检查分类是否存在
    if store_update.category_id:
        category = await StoreCategoryService.get_category_by_id(db, store_update.category_id)
        if not category or category.project_id != project.id:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="门店分类不存在"
            )

    # 如果指定了区域ID，检查区域是否存在
    if store_update.region_id:
        region = await StoreRegionService.get_region_by_id(db, store_update.region_id)
        if not region or region.project_id != project.id:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="门店区域不存在"
            )

    db_store = await StoreService.update_store(db, store_id, store_update)
    
    # 如果提供了manager_user_id，则设置管理员
    if data.manager_user_id:
        # 首先尝试作为project_user_id查找
        result = await db.execute(
            select(ProjectUser).where(
                ProjectUser.id == data.manager_user_id,
                ProjectUser.project_id == project.id
            )
        )
        project_user = result.scalars().first()
        
        # 如果没找到，再尝试作为user_id查找
        if not project_user:
            result = await db.execute(
                select(ProjectUser).where(
                    ProjectUser.user_id == data.manager_user_id,
                    ProjectUser.project_id == project.id
                )
            )
            project_user = result.scalars().first()
        
        if not project_user:
            raise HTTPException(status_code=400, detail="负责人用户未加入该项目")
        await ProjectUserStoreService.set_manager(db, store_id, project_user.id)
    
    return db_store

@router.delete("/{store_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_store(
    store_id: UUID = Path(...),
    project: Project = Depends(get_current_project),
    db: Session = Depends(get_db)
):
    """删除门店"""
    # 检查门店是否存在
    store = await StoreService.get_store_by_id(db, store_id)
    if not store or store.project_id != project.id:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="门店不存在"
        )

    success = await StoreService.delete_store(db, store_id)
    if not success:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="删除门店失败"
        )
    return None
