#!/usr/bin/env python
# -*- coding: utf-8 -*-

import logging
import uuid
from typing import Optional, List, Dict, Any
from fastapi import APIRouter, Depends, HTTPException, Query, Path, status
from sqlalchemy.ext.asyncio import AsyncSession
from datetime import datetime, date, timedelta

from db.database import get_db
from models.project import Project
from models.user import User
from api.deps import get_current_project, get_current_user
from services.dashboard import DashboardService
from schemas.dashboard import (
    ProjectDashboardResponse,
    OperationsDashboardResponse,
    FinanceDashboardResponse,
    WarehouseDashboardResponse,
    PurchaseDashboardResponse,
    DashboardAIRecommendationResponse
)

router = APIRouter(tags=["仪表盘"])
logger = logging.getLogger(__name__)

# 项目管理仪表盘
@router.get("/dashboard/project", response_model=ProjectDashboardResponse)
async def get_project_dashboard(
    start_date: Optional[date] = Query(None, description="开始日期"),
    end_date: Optional[date] = Query(None, description="结束日期"),
    project: Project = Depends(get_current_project),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    获取项目管理仪表盘数据
    """
    try:
        # 如果未指定日期范围，默认为最近7天
        if not start_date:
            end_date = date.today()
            start_date = end_date - timedelta(days=6)
        elif not end_date:
            end_date = start_date + timedelta(days=6)

        # 转换为datetime
        start_datetime = datetime.combine(start_date, datetime.min.time())
        end_datetime = datetime.combine(end_date, datetime.max.time())

        # 获取仪表盘数据
        dashboard_data = await DashboardService.get_project_dashboard(
            db=db,
            project_id=project.id,
            start_date=start_datetime,
            end_date=end_datetime
        )

        return dashboard_data
    except Exception as e:
        logger.error(f"获取项目管理仪表盘数据失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取项目管理仪表盘数据失败: {str(e)}"
        )

# 运营仪表盘
@router.get("/dashboard/operations", response_model=OperationsDashboardResponse)
async def get_operations_dashboard(
    start_date: Optional[date] = Query(None, description="开始日期"),
    end_date: Optional[date] = Query(None, description="结束日期"),
    store_id: Optional[uuid.UUID] = Query(None, description="门店ID"),
    project: Project = Depends(get_current_project),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    获取运营仪表盘数据
    """
    try:
        # 如果未指定日期范围，默认为最近7天
        if not start_date:
            end_date = date.today()
            start_date = end_date - timedelta(days=6)
        elif not end_date:
            end_date = start_date + timedelta(days=6)

        # 转换为datetime
        start_datetime = datetime.combine(start_date, datetime.min.time())
        end_datetime = datetime.combine(end_date, datetime.max.time())

        # 获取仪表盘数据
        dashboard_data = await DashboardService.get_operations_dashboard(
            db=db,
            project_id=project.id,
            start_date=start_datetime,
            end_date=end_datetime,
            store_id=store_id
        )

        return dashboard_data
    except Exception as e:
        logger.error(f"获取运营仪表盘数据失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取运营仪表盘数据失败: {str(e)}"
        )

# 财务仪表盘
@router.get("/dashboard/finance", response_model=FinanceDashboardResponse)
async def get_finance_dashboard(
    start_date: Optional[date] = Query(None, description="开始日期"),
    end_date: Optional[date] = Query(None, description="结束日期"),
    store_id: Optional[uuid.UUID] = Query(None, description="门店ID"),
    project: Project = Depends(get_current_project),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    获取财务仪表盘数据
    """
    try:
        # 如果未指定日期范围，默认为最近30天
        if not start_date:
            end_date = date.today()
            start_date = end_date - timedelta(days=29)
        elif not end_date:
            end_date = start_date + timedelta(days=29)

        # 转换为datetime
        start_datetime = datetime.combine(start_date, datetime.min.time())
        end_datetime = datetime.combine(end_date, datetime.max.time())

        # 获取仪表盘数据
        dashboard_data = await DashboardService.get_finance_dashboard(
            db=db,
            project_id=project.id,
            start_date=start_datetime,
            end_date=end_datetime,
            store_id=store_id
        )

        return dashboard_data
    except Exception as e:
        logger.error(f"获取财务仪表盘数据失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取财务仪表盘数据失败: {str(e)}"
        )

# 仓储仪表盘
@router.get("/dashboard/warehouse", response_model=WarehouseDashboardResponse)
async def get_warehouse_dashboard(
    warehouse_id: Optional[uuid.UUID] = Query(None, description="仓库ID"),
    project: Project = Depends(get_current_project),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    获取仓储仪表盘数据
    """
    try:
        # 获取仪表盘数据
        dashboard_data = await DashboardService.get_warehouse_dashboard(
            db=db,
            project_id=project.id,
            warehouse_id=warehouse_id
        )

        return dashboard_data
    except Exception as e:
        logger.error(f"获取仓储仪表盘数据失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取仓储仪表盘数据失败: {str(e)}"
        )

# 采购仪表盘
@router.get("/dashboard/purchase", response_model=PurchaseDashboardResponse)
async def get_purchase_dashboard(
    start_date: Optional[date] = Query(None, description="开始日期"),
    end_date: Optional[date] = Query(None, description="结束日期"),
    supplier_id: Optional[uuid.UUID] = Query(None, description="供应商ID"),
    project: Project = Depends(get_current_project),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    获取采购仪表盘数据
    """
    try:
        # 如果未指定日期范围，默认为最近30天
        if not start_date:
            end_date = date.today()
            start_date = end_date - timedelta(days=29)
        elif not end_date:
            end_date = start_date + timedelta(days=29)

        # 转换为datetime
        start_datetime = datetime.combine(start_date, datetime.min.time())
        end_datetime = datetime.combine(end_date, datetime.max.time())

        # 获取仪表盘数据
        dashboard_data = await DashboardService.get_purchase_dashboard(
            db=db,
            project_id=project.id,
            start_date=start_datetime,
            end_date=end_datetime,
            supplier_id=supplier_id
        )

        return dashboard_data
    except Exception as e:
        logger.error(f"获取采购仪表盘数据失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取采购仪表盘数据失败: {str(e)}"
        )

# 门店仪表盘
@router.get("/dashboard/store", response_model=OperationsDashboardResponse)
async def get_store_dashboard(
    start_date: Optional[date] = Query(None, description="开始日期"),
    end_date: Optional[date] = Query(None, description="结束日期"),
    store_id: Optional[uuid.UUID] = Query(None, description="门店ID"),
    project: Project = Depends(get_current_project),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    获取门店仪表盘数据
    """
    try:
        # 如果未指定日期范围，默认为最近7天
        if not start_date:
            end_date = date.today()
            start_date = end_date - timedelta(days=6)
        elif not end_date:
            end_date = start_date + timedelta(days=6)

        # 转换为datetime
        start_datetime = datetime.combine(start_date, datetime.min.time())
        end_datetime = datetime.combine(end_date, datetime.max.time())

        # 获取仪表盘数据
        dashboard_data = await DashboardService.get_store_dashboard(
            db=db,
            project_id=project.id,
            start_date=start_datetime,
            end_date=end_datetime,
            store_id=store_id
        )

        return dashboard_data
    except Exception as e:
        logger.error(f"获取门店仪表盘数据失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取门店仪表盘数据失败: {str(e)}"
        )

# 获取仪表盘AI推荐
@router.get("/dashboard/{dashboard_type}/ai-recommendations", response_model=DashboardAIRecommendationResponse)
async def get_dashboard_ai_recommendations(
    dashboard_type: str = Path(..., description="仪表盘类型: project, operations, finance, warehouse, purchase, store"),
    project: Project = Depends(get_current_project),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    获取仪表盘AI推荐
    """
    try:
        # 验证仪表盘类型
        valid_types = ["project", "operations", "finance", "warehouse", "purchase"]
        if dashboard_type not in valid_types:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"无效的仪表盘类型: {dashboard_type}，有效类型: {', '.join(valid_types)}"
            )

        # 获取AI推荐
        recommendations = await DashboardService.get_dashboard_ai_recommendations(
            db=db,
            project_id=project.id,
            dashboard_type=dashboard_type
        )

        return {"recommendations": recommendations}
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取仪表盘AI推荐失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取仪表盘AI推荐失败: {str(e)}"
        )
