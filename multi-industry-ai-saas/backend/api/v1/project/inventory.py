from typing import List, Optional, Dict, Any
from fastapi import APIRouter, Depends, HTTPException, Query, Path, UploadFile, File, Form, status
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from sqlalchemy import func, and_, or_
import uuid
import logging
from datetime import datetime, timedelta
import pandas as pd
import io
import json

from db.database import get_db
from models.inventory import InventoryItem, InventoryHistory, InventoryTransfer, InventoryTransferItem, InventoryCheck, InventoryCheckItem
from models.product import Product
from models.warehouse import Warehouse
from models.user import User
from schemas.inventory import (
    InventoryItemCreate, InventoryItemUpdate, InventoryItemResponse, InventoryItemListResponse,
    InventoryHistoryListResponse, InventoryTransferCreate, InventoryTransferUpdate,
    InventoryTransferResponse, InventoryTransferListResponse, InventoryCheckCreate,
    InventoryCheckUpdate, InventoryCheckResponse, InventoryCheckListResponse,
    InventoryImportResponse, InventoryImportConfirm, InventoryImportConfirmResponse
)
from api.deps import get_current_user, get_current_project_id, get_current_tenant_id

logger = logging.getLogger(__name__)
router = APIRouter()

# 库存项管理API
@router.get("/items", response_model=InventoryItemListResponse)
async def get_inventory_items(
    warehouse_id: Optional[uuid.UUID] = None,
    category_id: Optional[uuid.UUID] = None,
    is_low_stock: Optional[bool] = None,
    is_slow_moving: Optional[bool] = None,
    search: Optional[str] = None,
    skip: int = 0,
    limit: int = 100,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user),
    project_id: uuid.UUID = Depends(get_current_project_id),
    tenant_id: Optional[uuid.UUID] = Depends(get_current_tenant_id)
):
    """
    获取库存项列表
    """
    try:
        # 构建查询条件
        conditions = [
            InventoryItem.project_id == project_id
        ]

        if warehouse_id:
            conditions.append(InventoryItem.warehouse_id == warehouse_id)

        if is_low_stock:
            conditions.append(InventoryItem.quantity < InventoryItem.min_quantity)

        if is_slow_moving:
            conditions.append(InventoryItem.is_slow_moving == True)

        if search:
            # 联合查询产品名称和编码
            conditions.append(
                or_(
                    Product.name.ilike(f"%{search}%"),
                    Product.sku.ilike(f"%{search}%")
                )
            )

        if category_id:
            conditions.append(Product.category_id == category_id)

        # 构建查询
        query = (
            select(
                InventoryItem,
                Product.name.label("product_name"),
                Product.sku.label("product_sku"),
                Warehouse.name.label("warehouse_name")
            )
            .join(Product, InventoryItem.product_id == Product.id)
            .join(Warehouse, InventoryItem.warehouse_id == Warehouse.id)
            .where(and_(*conditions))
            .order_by(Product.name)
            .offset(skip)
            .limit(limit)
        )

        # 执行查询
        result = await db.execute(query)
        items = result.all()

        # 获取总数
        count_query = select(func.count()).select_from(
            select(InventoryItem)
            .join(Product, InventoryItem.product_id == Product.id)
            .where(and_(*conditions))
            .subquery()
        )
        result = await db.execute(count_query)
        total = result.scalar_one()

        # 构建响应数据
        inventory_items = []
        for item in items:
            inventory_item = item.InventoryItem
            inventory_item_dict = {
                **inventory_item.__dict__,
                "product_name": item.product_name,
                "product_sku": item.product_sku,
                "warehouse_name": item.warehouse_name
            }
            inventory_items.append(inventory_item_dict)

        return {
            "success": True,
            "items": inventory_items,
            "total": total,
            "page": skip // limit + 1 if limit > 0 else 1,
            "size": limit,
            "pages": (total + limit - 1) // limit if limit > 0 else 1
        }
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取库存项列表失败: {str(e)}"
        )


@router.get("/items/{item_id}", response_model=InventoryItemResponse)
async def get_inventory_item(
    item_id: uuid.UUID = Path(...),
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user),
    project_id: uuid.UUID = Depends(get_current_project_id)
):
    """
    获取单个库存项详情
    """
    try:
        # 构建查询
        query = (
            select(
                InventoryItem,
                Product.name.label("product_name"),
                Product.sku.label("product_sku"),
                Warehouse.name.label("warehouse_name")
            )
            .join(Product, InventoryItem.product_id == Product.id)
            .join(Warehouse, InventoryItem.warehouse_id == Warehouse.id)
            .where(
                InventoryItem.id == item_id,
                InventoryItem.project_id == project_id
            )
        )

        # 执行查询
        result = await db.execute(query)
        item = result.first()

        if not item:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="库存项不存在"
            )

        # 构建响应数据
        inventory_item = item.InventoryItem
        inventory_item_dict = {
            **inventory_item.__dict__,
            "product_name": item.product_name,
            "product_sku": item.product_sku,
            "warehouse_name": item.warehouse_name
        }

        return {
            "success": True,
            "data": inventory_item_dict
        }
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取库存项详情失败: {str(e)}"
        )


@router.post("/items", response_model=InventoryItemResponse)
async def create_inventory_item(
    item: InventoryItemCreate,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user),
    project_id: uuid.UUID = Depends(get_current_project_id),
    tenant_id: Optional[uuid.UUID] = Depends(get_current_tenant_id)
):
    """
    创建库存项
    """
    try:
        # 检查产品是否存在
        product_query = select(Product).where(
            Product.id == item.product_id,
            Product.project_id == project_id
        )
        result = await db.execute(product_query)
        product = result.scalar_one_or_none()

        if not product:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="产品不存在"
            )

        # 检查仓库是否存在
        warehouse_query = select(Warehouse).where(
            Warehouse.id == item.warehouse_id,
            Warehouse.project_id == project_id
        )
        result = await db.execute(warehouse_query)
        warehouse = result.scalar_one_or_none()

        if not warehouse:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="仓库不存在"
            )

        # 检查是否已存在相同的库存项
        existing_query = select(InventoryItem).where(
            InventoryItem.product_id == item.product_id,
            InventoryItem.warehouse_id == item.warehouse_id,
            InventoryItem.project_id == project_id
        )
        result = await db.execute(existing_query)
        existing_item = result.scalar_one_or_none()

        if existing_item:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="该产品在此仓库中已有库存记录"
            )

        # 创建新库存项
        new_item = InventoryItem(
            id=uuid.uuid4(),
            tenant_id=tenant_id,
            project_id=project_id,
            product_id=item.product_id,
            warehouse_id=item.warehouse_id,
            quantity=item.quantity,
            min_quantity=item.min_quantity,
            max_quantity=item.max_quantity,
            is_slow_moving=item.is_slow_moving,
            last_movement_date=datetime.now() if item.quantity > 0 else None,
            created_by=current_user.id,
            updated_by=current_user.id
        )

        db.add(new_item)
        await db.commit()
        await db.refresh(new_item)

        # 如果初始库存大于0，创建历史记录
        if item.quantity > 0:
            history = InventoryHistory(
                id=uuid.uuid4(),
                inventory_item_id=new_item.id,
                tenant_id=tenant_id,
                project_id=project_id,
                action_type="add",
                previous_quantity=0,
                new_quantity=item.quantity,
                change_amount=item.quantity,
                notes="初始库存",
                created_by=current_user.id
            )
            db.add(history)
            await db.commit()

        # 构建响应数据
        query = (
            select(
                InventoryItem,
                Product.name.label("product_name"),
                Product.sku.label("product_sku"),
                Warehouse.name.label("warehouse_name")
            )
            .join(Product, InventoryItem.product_id == Product.id)
            .join(Warehouse, InventoryItem.warehouse_id == Warehouse.id)
            .where(InventoryItem.id == new_item.id)
        )

        result = await db.execute(query)
        item = result.first()

        inventory_item = item.InventoryItem
        inventory_item_dict = {
            **inventory_item.__dict__,
            "product_name": item.product_name,
            "product_sku": item.product_sku,
            "warehouse_name": item.warehouse_name
        }

        return {
            "success": True,
            "message": "库存项创建成功",
            "data": inventory_item_dict
        }
    except HTTPException:
        raise
    except Exception as e:
        await db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"创建库存项失败: {str(e)}"
        )


@router.put("/items/{item_id}", response_model=InventoryItemResponse)
async def update_inventory_item(
    item_id: uuid.UUID,
    item_update: InventoryItemUpdate,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user),
    project_id: uuid.UUID = Depends(get_current_project_id)
):
    """
    更新库存项
    """
    try:
        # 查询库存项
        query = select(InventoryItem).where(
            InventoryItem.id == item_id,
            InventoryItem.project_id == project_id
        )
        result = await db.execute(query)
        existing_item = result.scalar_one_or_none()

        if not existing_item:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="库存项不存在"
            )

        # 检查是否更新了数量
        quantity_changed = item_update.quantity is not None and item_update.quantity != existing_item.quantity
        old_quantity = existing_item.quantity

        # 更新字段
        update_data = item_update.dict(exclude_unset=True)

        for key, value in update_data.items():
            setattr(existing_item, key, value)

        existing_item.updated_by = current_user.id
        existing_item.updated_at = datetime.now()

        # 如果数量变更，更新最后移动日期
        if quantity_changed:
            existing_item.last_movement_date = datetime.now()

        await db.commit()
        await db.refresh(existing_item)

        # 如果数量变更，创建历史记录
        if quantity_changed:
            history = InventoryHistory(
                id=uuid.uuid4(),
                inventory_item_id=existing_item.id,
                tenant_id=existing_item.tenant_id,
                project_id=existing_item.project_id,
                action_type="update",
                previous_quantity=old_quantity,
                new_quantity=existing_item.quantity,
                change_amount=existing_item.quantity - old_quantity,
                notes="手动更新库存",
                created_by=current_user.id
            )
            db.add(history)
            await db.commit()

        # 构建响应数据
        query = (
            select(
                InventoryItem,
                Product.name.label("product_name"),
                Product.sku.label("product_sku"),
                Warehouse.name.label("warehouse_name")
            )
            .join(Product, InventoryItem.product_id == Product.id)
            .join(Warehouse, InventoryItem.warehouse_id == Warehouse.id)
            .where(InventoryItem.id == item_id)
        )

        result = await db.execute(query)
        item = result.first()

        inventory_item = item.InventoryItem
        inventory_item_dict = {
            **inventory_item.__dict__,
            "product_name": item.product_name,
            "product_sku": item.product_sku,
            "warehouse_name": item.warehouse_name
        }

        return {
            "success": True,
            "message": "库存项更新成功",
            "data": inventory_item_dict
        }
    except HTTPException:
        raise
    except Exception as e:
        await db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"更新库存项失败: {str(e)}"
        )


@router.delete("/items/{item_id}", response_model=InventoryItemResponse)
async def delete_inventory_item(
    item_id: uuid.UUID,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user),
    project_id: uuid.UUID = Depends(get_current_project_id)
):
    """
    删除库存项
    """
    try:
        # 查询库存项
        query = select(InventoryItem).where(
            InventoryItem.id == item_id,
            InventoryItem.project_id == project_id
        )
        result = await db.execute(query)
        item = result.scalar_one_or_none()

        if not item:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="库存项不存在"
            )

        # 删除库存项
        await db.delete(item)
        await db.commit()

        return {
            "success": True,
            "message": "库存项删除成功"
        }
    except HTTPException:
        raise
    except Exception as e:
        await db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"删除库存项失败: {str(e)}"
        )


@router.get("/items/{item_id}/history", response_model=InventoryHistoryListResponse)
async def get_inventory_history(
    item_id: uuid.UUID,
    skip: int = 0,
    limit: int = 100,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user),
    project_id: uuid.UUID = Depends(get_current_project_id)
):
    """
    获取库存项历史记录
    """
    try:
        # 检查库存项是否存在
        item_query = select(InventoryItem).where(
            InventoryItem.id == item_id,
            InventoryItem.project_id == project_id
        )
        result = await db.execute(item_query)
        item = result.scalar_one_or_none()

        if not item:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="库存项不存在"
            )

        # 查询历史记录
        query = (
            select(
                InventoryHistory,
                User.name.label("operator_name")
            )
            .outerjoin(User, InventoryHistory.created_by == User.id)
            .where(InventoryHistory.inventory_item_id == item_id)
            .order_by(InventoryHistory.created_at.desc())
            .offset(skip)
            .limit(limit)
        )

        result = await db.execute(query)
        history_items = result.all()

        # 获取总数
        count_query = select(func.count()).where(
            InventoryHistory.inventory_item_id == item_id
        )
        result = await db.execute(count_query)
        total = result.scalar_one()

        # 构建响应数据
        history_list = []
        for history in history_items:
            history_item = history.InventoryHistory
            history_dict = {
                **history_item.__dict__,
                "operator_name": history.operator_name
            }
            history_list.append(history_dict)

        return {
            "success": True,
            "history": history_list,
            "total": total,
            "page": skip // limit + 1 if limit > 0 else 1,
            "size": limit,
            "pages": (total + limit - 1) // limit if limit > 0 else 1
        }
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取库存历史记录失败: {str(e)}"
        )


# 库存导入相关API
@router.post("/upload", response_model=InventoryImportResponse)
async def upload_inventory_sheet(
    file: UploadFile = File(...),
    warehouse_id: uuid.UUID = Form(...),
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user),
    project_id: uuid.UUID = Depends(get_current_project_id),
    tenant_id: Optional[uuid.UUID] = Depends(get_current_tenant_id)
):
    """
    上传库存表格
    """
    try:
        # 检查仓库是否存在
        warehouse_query = select(Warehouse).where(
            Warehouse.id == warehouse_id,
            Warehouse.project_id == project_id
        )
        result = await db.execute(warehouse_query)
        warehouse = result.scalar_one_or_none()

        if not warehouse:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="仓库不存在"
            )

        # 读取Excel文件
        content = await file.read()

        if file.filename.endswith('.xlsx') or file.filename.endswith('.xls'):
            df = pd.read_excel(io.BytesIO(content))
        elif file.filename.endswith('.csv'):
            df = pd.read_csv(io.BytesIO(content))
        else:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="不支持的文件格式，请上传.xlsx、.xls或.csv文件"
            )

        # 检查必要的列
        required_columns = ['商品名称', '商品编码', '当前库存', '最低库存', '单位']
        missing_columns = [col for col in required_columns if col not in df.columns]

        if missing_columns:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"缺少必要的列: {', '.join(missing_columns)}"
            )

        # 获取所有产品
        product_query = select(Product).where(Product.project_id == project_id)
        result = await db.execute(product_query)
        products = {p.sku: p for p in result.scalars().all()}

        # 获取现有库存项
        inventory_query = select(InventoryItem).where(
            InventoryItem.warehouse_id == warehouse_id,
            InventoryItem.project_id == project_id
        )
        result = await db.execute(inventory_query)
        existing_items = {item.product_id: item for item in result.scalars().all()}

        # 处理数据
        preview_items = []
        new_count = 0
        update_count = 0
        error_count = 0

        for _, row in df.iterrows():
            try:
                item = {
                    'name': str(row['商品名称']),
                    'code': str(row['商品编码']),
                    'category': str(row.get('分类', '')),
                    'current_stock': int(row['当前库存']),
                    'min_stock': int(row['最低库存']),
                    'unit': str(row['单位'])
                }

                # 检查产品是否存在
                product = products.get(item['code'])

                if not product:
                    item['status'] = 'error'
                    item['error_message'] = '商品编码不存在'
                    error_count += 1
                else:
                    # 检查是否已有库存记录
                    if product.id in existing_items:
                        item['status'] = 'update'
                        update_count += 1
                    else:
                        item['status'] = 'new'
                        new_count += 1

                preview_items.append(item)
            except Exception as e:
                preview_items.append({
                    'name': str(row.get('商品名称', '')),
                    'code': str(row.get('商品编码', '')),
                    'category': str(row.get('分类', '')),
                    'current_stock': 0,
                    'min_stock': 0,
                    'unit': str(row.get('单位', '')),
                    'status': 'error',
                    'error_message': str(e)
                })
                error_count += 1

        # 保存预览数据到会话
        session_data = {
            'warehouse_id': str(warehouse_id),
            'preview_items': preview_items,
            'products': {sku: str(p.id) for sku, p in products.items()},
            'existing_items': {str(pid): str(item.id) for pid, item in existing_items.items()}
        }

        # 在实际应用中，应该使用Redis或其他缓存存储会话数据
        # 这里简化处理，将数据存储在数据库中的临时表或配置中
        # 创建或更新临时导入配置
        import_config_query = select(SystemConfig).where(
            SystemConfig.project_id == project_id,
            SystemConfig.key == f"inventory_import_{current_user.id}"
        )
        result = await db.execute(import_config_query)
        import_config = result.scalar_one_or_none()

        if import_config:
            import_config.value = json.dumps(session_data)
            import_config.updated_at = datetime.now()
        else:
            from models.system_config import SystemConfig
            import_config = SystemConfig(
                id=uuid.uuid4(),
                tenant_id=tenant_id,
                project_id=project_id,
                key=f"inventory_import_{current_user.id}",
                value=json.dumps(session_data),
                created_by=current_user.id
            )
            db.add(import_config)

        await db.commit()

        # 返回预览数据
        return {
            "success": True,
            "message": "库存表上传成功",
            "data": {
                "preview": preview_items[:100],  # 限制预览数量
                "total": len(preview_items),
                "new_items": new_count,
                "updated_items": update_count,
                "errors": error_count
            }
        }
    except HTTPException:
        raise
    except Exception as e:
        await db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"上传库存表失败: {str(e)}"
        )


@router.post("/confirm-import", response_model=InventoryImportConfirmResponse)
async def confirm_inventory_import(
    data: InventoryImportConfirm,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user),
    project_id: uuid.UUID = Depends(get_current_project_id),
    tenant_id: Optional[uuid.UUID] = Depends(get_current_tenant_id)
):
    """
    确认导入库存表
    """
    try:
        # 获取导入配置
        from models.system_config import SystemConfig
        import_config_query = select(SystemConfig).where(
            SystemConfig.project_id == project_id,
            SystemConfig.key == f"inventory_import_{current_user.id}"
        )
        result = await db.execute(import_config_query)
        import_config = result.scalar_one_or_none()

        if not import_config:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="没有找到导入数据，请先上传库存表"
            )

        # 解析导入数据
        session_data = json.loads(import_config.value)

        # 检查仓库ID是否匹配
        if str(data.warehouse_id) != session_data['warehouse_id']:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="仓库ID不匹配，请重新上传库存表"
            )

        # 获取产品和现有库存项映射
        products_map = {sku: uuid.UUID(pid) for sku, pid in session_data['products'].items()}
        existing_items_map = {uuid.UUID(pid): uuid.UUID(iid) for pid, iid in session_data['existing_items'].items()}

        # 处理导入数据
        imported_count = 0
        now = datetime.now()

        for item_data in session_data['preview_items']:
            if item_data['status'] == 'error':
                continue

            product = products_map.get(item_data['code'])
            if not product:
                continue

            if item_data['status'] == 'update':
                # 更新现有库存项
                inventory_id = existing_items_map.get(product)
                if not inventory_id:
                    continue

                # 查询现有库存项
                item_query = select(InventoryItem).where(InventoryItem.id == inventory_id)
                result = await db.execute(item_query)
                inventory_item = result.scalar_one_or_none()

                if not inventory_item:
                    continue

                # 记录旧数量
                old_quantity = inventory_item.quantity
                new_quantity = item_data['current_stock']

                # 更新库存项
                inventory_item.quantity = new_quantity
                inventory_item.min_quantity = item_data['min_stock']
                inventory_item.updated_by = current_user.id
                inventory_item.updated_at = now

                if old_quantity != new_quantity:
                    inventory_item.last_movement_date = now

                # 创建历史记录
                if old_quantity != new_quantity:
                    history = InventoryHistory(
                        id=uuid.uuid4(),
                        inventory_item_id=inventory_item.id,
                        tenant_id=tenant_id,
                        project_id=project_id,
                        action_type="import",
                        previous_quantity=old_quantity,
                        new_quantity=new_quantity,
                        change_amount=new_quantity - old_quantity,
                        notes="导入更新",
                        created_by=current_user.id
                    )
                    db.add(history)

                imported_count += 1

            elif item_data['status'] == 'new':
                # 创建新库存项
                new_item = InventoryItem(
                    id=uuid.uuid4(),
                    tenant_id=tenant_id,
                    project_id=project_id,
                    product_id=product,
                    warehouse_id=uuid.UUID(session_data['warehouse_id']),
                    quantity=item_data['current_stock'],
                    min_quantity=item_data['min_stock'],
                    is_slow_moving=False,
                    last_movement_date=now if item_data['current_stock'] > 0 else None,
                    created_by=current_user.id,
                    updated_by=current_user.id
                )

                db.add(new_item)

                # 创建历史记录
                if item_data['current_stock'] > 0:
                    history = InventoryHistory(
                        id=uuid.uuid4(),
                        inventory_item_id=new_item.id,
                        tenant_id=tenant_id,
                        project_id=project_id,
                        action_type="import",
                        previous_quantity=0,
                        new_quantity=item_data['current_stock'],
                        change_amount=item_data['current_stock'],
                        notes="导入新增",
                        created_by=current_user.id
                    )
                    db.add(history)

                imported_count += 1

        # 提交事务
        await db.commit()

        # 删除导入配置
        await db.delete(import_config)
        await db.commit()

        return {
            "success": True,
            "message": "库存导入成功",
            "imported_count": imported_count
        }
    except HTTPException:
        raise
    except Exception as e:
        await db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"确认导入库存表失败: {str(e)}"
        )


@router.post("/cancel-import", response_model=dict)
async def cancel_inventory_import(
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user),
    project_id: uuid.UUID = Depends(get_current_project_id)
):
    """
    取消导入库存表
    """
    try:
        # 获取导入配置
        from models.system_config import SystemConfig
        import_config_query = select(SystemConfig).where(
            SystemConfig.project_id == project_id,
            SystemConfig.key == f"inventory_import_{current_user.id}"
        )
        result = await db.execute(import_config_query)
        import_config = result.scalar_one_or_none()

        if import_config:
            # 删除导入配置
            await db.delete(import_config)
            await db.commit()

        return {
            "success": True,
            "message": "已取消导入"
        }
    except Exception as e:
        await db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"取消导入库存表失败: {str(e)}"
        )


@router.get("/template")
async def download_inventory_template(
    project_id: uuid.UUID = Depends(get_current_project_id),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    下载库存表模板
    """
    try:
        import pandas as pd
        from io import BytesIO
        from fastapi.responses import Response
        
        # 创建模板数据
        template_data = {
            '商品编号': ['P001', 'P002', 'P003'],
            '商品名称': ['苹果', '香蕉', '橙子'],
            '库存数量': [100, 50, 75],
            '单位': ['公斤', '公斤', '公斤'],
            '规格': ['红富士', '进口', '脐橙'],
            '分类': ['水果', '水果', '水果'],
            '最低库存': [10, 5, 8],
            '备注': ['新鲜水果', '进口水果', '当季水果']
        }
        
        # 创建DataFrame
        df = pd.DataFrame(template_data)
        
        # 保存为Excel文件
        output = BytesIO()
        with pd.ExcelWriter(output, engine='openpyxl') as writer:
            df.to_excel(writer, index=False, sheet_name='库存表模板')
            
            # 获取工作表
            worksheet = writer.sheets['库存表模板']
            
            # 设置列宽
            for column in worksheet.columns:
                max_length = 0
                column_letter = column[0].column_letter
                for cell in column:
                    try:
                        if len(str(cell.value)) > max_length:
                            max_length = len(str(cell.value))
                    except:
                        pass
                adjusted_width = (max_length + 2) * 1.2
                worksheet.column_dimensions[column_letter].width = adjusted_width
        
        output.seek(0)
        
        # 返回文件
        return Response(
            content=output.getvalue(),
            media_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            headers={
                'Content-Disposition': 'attachment; filename=库存表模板.xlsx'
            }
        )
        
    except Exception as e:
        logger.error(f"下载库存表模板失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"下载库存表模板失败: {str(e)}"
        )

@router.post("/preview-upload-ai", response_model=dict)
async def preview_upload_inventory_with_ai(
    file_id: uuid.UUID,
    warehouse_id: uuid.UUID,
    use_ai: bool = True,
    processing_mode: str = "auto",  # auto, template_only, ai_only
    project_id: uuid.UUID = Depends(get_current_project_id),
    tenant_id: Optional[uuid.UUID] = Depends(get_current_tenant_id),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    使用智能处理库存表预览上传
    
    处理模式说明：
    - auto: 自动选择（推荐）- 优先模板匹配，若模板匹配失败或置信度低则使用AI
    - template_only: 仅模板匹配 - 适用于标准格式表格  
    - ai_only: 仅AI识别 - 适用于复杂或非标准格式
    """
    try:
        logger.info(f"开始处理库存表AI预览: file_id={file_id}, processing_mode={processing_mode}")
        
        # 验证处理模式
        valid_processing_modes = ["auto", "template_only", "ai_only"]
        if processing_mode not in valid_processing_modes:
            logger.warning(f"无效的处理模式: {processing_mode}, 使用默认值 auto")
            processing_mode = "auto"

        # 验证仓库是否存在
        warehouse_query = select(Warehouse).where(
            Warehouse.id == warehouse_id,
            Warehouse.project_id == project_id
        )
        result = await db.execute(warehouse_query)
        warehouse = result.scalar_one_or_none()

        if not warehouse:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="仓库不存在"
            )

        # 验证文件是否存在
        from services.storage_service import StorageService
        file_info = await StorageService.get_file_by_id(db, file_id, project_id)

        if not file_info:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="文件不存在或不属于当前项目"
            )

        # 智能表格处理
        logger.info(f"使用{processing_mode}方案处理库存表: {file_id}")
        
        # 导入表格处理服务
        from services.table_processing_service import TableProcessingService
        from schemas.table_processing import TableProcessingRequest
        
        # 构建表格处理请求
        table_request = TableProcessingRequest(
            file_id=file_id,
            processing_mode=processing_mode,
            template_hint="inventory_sheet",
            custom_prompt="""
这是一个库存表格，请提取以下信息：
1. 商品基础信息：商品编号、商品名称、库存数量、单位、规格、分类
2. 库存数据：当前库存、最低库存、最大库存等

表格结构：
- 商品编号（必填）
- 商品名称（必填）
- 库存数量（必填）
- 单位
- 规格
- 分类
- 最低库存
- 备注等

请按照以下JSON格式返回：
{
    "columns": ["商品编号", "商品名称", "库存数量", "单位", "规格", "分类", "最低库存", "备注"],
    "data": [
        {
            "商品编号": "商品编号",
            "商品名称": "商品名称",
            "库存数量": 数量数值,
            "单位": "单位",
            "规格": "规格",
            "分类": "分类",
            "最低库存": 数量数值,
            "备注": "备注信息"
        }
    ]
}

要求：
1. 准确识别所有列名，特别是商品编号和商品名称
2. 提取所有商品行的数据
3. 数字保持为数字格式（库存数量、最低库存等）
4. 空白单元格用合适的默认值或0填充
""",
            use_default_vision_model=True,
            vision_model_id=None,
            vision_temperature=0.7,
            enable_validation=True,
            enable_correction=True
        )
        
        logger.info(f"开始调用TableProcessingService处理库存表文件: {file_id}")
        
        # 调用通用表格处理服务
        result = await TableProcessingService.process_table(
            db=db,
            request=table_request,
            project_id=project_id,
            tenant_id=tenant_id,
            user_id=current_user.id
        )
        
        logger.info(f"TableProcessingService处理完成: success={result.success}, method={getattr(result, 'processing_method', 'unknown')}")
        
        if hasattr(result, 'errors') and result.errors:
            logger.warning(f"处理过程中的错误: {result.errors}")
        
        if hasattr(result, 'warnings') and result.warnings:
            logger.info(f"处理过程中的警告: {result.warnings}")
        
        if not result.success:
            logger.warning(f"智能处理失败: {result.errors}")
            error_detail = "智能处理失败，请检查文件格式或重新上传"
            if result.errors:
                # 提取更具体的错误信息
                main_error = result.errors[0] if result.errors else {}
                if main_error.get("type") == "ai_error":
                    error_detail = f"AI处理失败: {main_error.get('message', '未知错误')}"
                elif main_error.get("type") == "template_error":
                    error_detail = f"模板处理失败: {main_error.get('message', '未知错误')}"
                elif main_error.get("type") == "file_error":
                    error_detail = f"文件处理失败: {main_error.get('message', '文件格式不支持或损坏')}"
                elif main_error.get("type") == "vision_error":
                    error_detail = f"视觉识别失败: {main_error.get('message', '图像识别错误')}"
                else:
                    # 如果有具体的错误消息，使用它
                    if main_error.get("message"):
                        error_detail = f"处理失败: {main_error.get('message')}"
                        
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=error_detail
            )
        
        # 转换处理结果为库存表格式
        preview_data = await _convert_ai_result_to_inventory_format(
            result, warehouse_id, project_id, db
        )
        
        return {
            "success": True,
            "message": f"智能解析完成（使用{result.processing_method}方案）",
            "data": {
                "preview": preview_data,
                "processing_info": {
                    "method": result.processing_method,
                    "template_matched": result.template_matched,
                    "total_rows": result.total_rows,
                    "valid_rows": result.valid_rows,
                    "error_rows": result.error_rows,
                    "confidence": getattr(result, 'confidence', 0.8),
                    "processing_mode": processing_mode,
                    "ai_processing_info": result.ai_processing_info
                },
                "validation_results": {
                    "errors": result.errors,
                    "warnings": result.warnings
                },
                "file_id": str(file_id),  # 确保返回file_id供后续确认使用
                # 直接将预览数据展平到根级别，便于前端访问
                "inventory_items": preview_data.get("inventory_items", []),
                "new_items": preview_data.get("new_items", 0),
                "updated_items": preview_data.get("updated_items", 0),
                "warehouse_info": preview_data.get("warehouse_info", {})
            }
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"库存表预览处理失败: {e}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"库存表预览处理失败: {str(e)}"
        )

async def _convert_ai_result_to_inventory_format(
    ai_result: "TableProcessingResult",
    warehouse_id: uuid.UUID,
    project_id: uuid.UUID,
    db: AsyncSession
) -> Dict[str, Any]:
    """
    将AI提取结果转换为库存表格式
    """
    try:
        preview_data = {}
        
        # 处理不同的数据格式
        if isinstance(ai_result.extracted_data, dict):
            # 新格式：{"columns": [...], "data": [...]}
            columns = ai_result.extracted_data.get("columns", [])
            data_rows = ai_result.extracted_data.get("data", [])
        elif isinstance(ai_result.extracted_data, list):
            # 兼容旧格式：直接是数据行列表
            data_rows = ai_result.extracted_data
            columns = []
            if data_rows:
                columns = list(data_rows[0].keys())
        else:
            raise ValueError("无效的数据格式")
        
        if not data_rows:
            raise ValueError("未提取到有效数据")
        
        # 识别列类型
        product_code_columns = {"商品编号", "商品编码", "产品编号", "product_code", "code"}
        product_name_columns = {"商品名称", "产品名称", "商品", "product_name", "name"}
        quantity_columns = {"库存数量", "当前库存", "数量", "库存", "quantity", "stock", "current_stock"}
        unit_columns = {"单位", "unit"}
        spec_columns = {"规格", "specification", "spec"}
        category_columns = {"分类", "类别", "category"}
        min_stock_columns = {"最低库存", "安全库存", "min_stock", "minimum_stock"}
        max_stock_columns = {"最大库存", "最高库存", "max_stock", "maximum_stock"}
        remark_columns = {"备注", "说明", "remark", "note"}
        
        # 识别各列的映射关系
        column_mapping = {}
        for col in columns:
            col_lower = col.lower()
            if col in product_code_columns or col_lower in product_code_columns:
                column_mapping["product_code"] = col
            elif col in product_name_columns or col_lower in product_name_columns:
                column_mapping["product_name"] = col
            elif col in quantity_columns or col_lower in quantity_columns:
                column_mapping["quantity"] = col
            elif col in unit_columns or col_lower in unit_columns:
                column_mapping["unit"] = col
            elif col in spec_columns or col_lower in spec_columns:
                column_mapping["specification"] = col
            elif col in category_columns or col_lower in category_columns:
                column_mapping["category"] = col
            elif col in min_stock_columns or col_lower in min_stock_columns:
                column_mapping["min_stock"] = col
            elif col in max_stock_columns or col_lower in max_stock_columns:
                column_mapping["max_stock"] = col
            elif col in remark_columns or col_lower in remark_columns:
                column_mapping["remark"] = col
        
        # 获取仓库信息
        warehouse_query = select(Warehouse).where(Warehouse.id == warehouse_id)
        result = await db.execute(warehouse_query)
        warehouse = result.scalar_one_or_none()
        
        # 处理库存数据
        inventory_items = []
        new_items = 0
        updated_items = 0
        
        for idx, row in enumerate(data_rows):
            # 跳过空行
            product_name = str(row.get(column_mapping.get("product_name", "商品名称"), "")).strip()
            if not product_name or product_name == "nan":
                continue
            
            # 提取库存数量
            quantity_raw = row.get(column_mapping.get("quantity", "库存数量"), 0)
            try:
                quantity = float(quantity_raw) if quantity_raw and str(quantity_raw).strip() != "" else 0
            except (ValueError, TypeError):
                quantity = 0
            
            # 提取最低库存
            min_stock_raw = row.get(column_mapping.get("min_stock", "最低库存"), 10)
            try:
                min_stock = float(min_stock_raw) if min_stock_raw and str(min_stock_raw).strip() != "" else 10
            except (ValueError, TypeError):
                min_stock = 10
            
            # 提取最大库存
            max_stock_raw = row.get(column_mapping.get("max_stock", "最大库存"), 0)
            try:
                max_stock = float(max_stock_raw) if max_stock_raw and str(max_stock_raw).strip() != "" else 0
            except (ValueError, TypeError):
                max_stock = 0
            
            item = {
                "index": idx,
                "product_code": str(row.get(column_mapping.get("product_code", "商品编码"), "")).strip(),
                "product_name": product_name,
                "current_stock": quantity,
                "min_stock": min_stock,
                "max_stock": max_stock,
                "unit": str(row.get(column_mapping.get("unit", "单位"), "个")).strip(),
                "specification": str(row.get(column_mapping.get("specification", "规格"), "")).strip(),
                "category": str(row.get(column_mapping.get("category", "分类"), "其他")).strip(),
                "remark": str(row.get(column_mapping.get("remark", "备注"), "")).strip(),
                "warehouse_id": str(warehouse_id),
                "warehouse_name": warehouse.name if warehouse else "未知仓库"
            }
            
            inventory_items.append(item)
            
            # 简单的新增/更新判断逻辑（实际应该查询数据库）
            if item["product_code"]:
                updated_items += 1
            else:
                new_items += 1
        
        preview_data["inventory_items"] = inventory_items
        preview_data["new_items"] = new_items
        preview_data["updated_items"] = updated_items
        preview_data["warehouse_info"] = {
            "id": str(warehouse_id),
            "name": warehouse.name if warehouse else "未知仓库"
        }
        
        return preview_data
        
    except Exception as e:
        logger.error(f"转换AI结果为库存格式失败: {e}")
        raise ValueError(f"数据转换失败: {str(e)}")
