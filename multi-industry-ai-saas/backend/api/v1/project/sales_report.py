#!/usr/bin/env python
# -*- coding: utf-8 -*-

import logging
from typing import List, Optional, Dict, Any
from fastapi import APIRouter, Depends, HTTPException, status, Query, Path
from sqlalchemy.ext.asyncio import AsyncSession
from pydantic import parse_obj_as
from datetime import datetime, date
import uuid

from db.database import get_db
from core.auth import get_current_user
from api.deps import get_current_project
from models.project import Project
from models.user import User
from schemas.sales_report import (
    SalesReportCreate,
    SalesReportUpdate,
    SalesReportStatusUpdate,
    SalesReportResponse,
    SalesReportListResponse,
    SalesReportItemCreate,
    SalesReportItemUpdate,
    SalesReportStatisticsResponse
)
from services.sales_report import SalesReportService

# 配置日志
logger = logging.getLogger(__name__)

# 创建路由器
router = APIRouter()

@router.get("", response_model=SalesReportListResponse)
async def get_sales_reports(
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=1000),
    store_id: Optional[uuid.UUID] = None,
    report_status: Optional[str] = None,
    report_type: Optional[str] = None,
    search: Optional[str] = None,
    start_date: Optional[date] = None,
    end_date: Optional[date] = None,
    sort_by: str = "report_date",
    sort_order: str = "desc",
    project: Project = Depends(get_current_project),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    获取销售上报列表
    """
    try:
        # 转换日期为datetime
        start_datetime = datetime.combine(start_date, datetime.min.time()) if start_date else None
        end_datetime = datetime.combine(end_date, datetime.max.time()) if end_date else None

        # 获取销售上报
        sales_reports = await SalesReportService.get_sales_reports(
            db=db,
            project_id=project.id,
            skip=skip,
            limit=limit,
            store_id=store_id,
            status=report_status,
            report_type=report_type,
            search=search,
            start_date=start_datetime,
            end_date=end_datetime,
            sort_by=sort_by,
            sort_order=sort_order
        )

        # 获取销售上报总数
        total = await SalesReportService.count_sales_reports(
            db=db,
            project_id=project.id,
            store_id=store_id,
            status=report_status,
            report_type=report_type,
            search=search,
            start_date=start_datetime,
            end_date=end_datetime
        )

        # 构建响应数据
        report_responses = []
        for report in sales_reports:
            # 获取销售上报详情
            report_detail = await SalesReportService.get_sales_report_with_details(db, report.id)
            if report_detail:
                report_responses.append(report_detail)

        return {
            "items": parse_obj_as(List[SalesReportResponse], report_responses),
            "total": total,
            "page": skip // limit + 1 if limit else 1,
            "size": limit,
            "pages": (total + limit - 1) // limit if limit else 1
        }
    except Exception as e:
        logger.error(f"获取销售上报列表失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取销售上报列表失败: {str(e)}"
        )

@router.post("", response_model=SalesReportResponse, status_code=status.HTTP_201_CREATED)
async def create_sales_report(
    report_data: SalesReportCreate,
    project: Project = Depends(get_current_project),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    创建销售上报
    """
    try:
        # 创建销售上报
        report = await SalesReportService.create_sales_report(
            db=db,
            report_data=report_data,
            project_id=project.id,
            user_id=current_user.id
        )

        # 获取销售上报详情
        report_detail = await SalesReportService.get_sales_report_with_details(db, report.id)
        if not report_detail:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="销售上报不存在"
            )

        return parse_obj_as(SalesReportResponse, report_detail)
    except ValueError as e:
        logger.error(f"创建销售上报失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        logger.error(f"创建销售上报失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"创建销售上报失败: {str(e)}"
        )

@router.get("/statistics", response_model=SalesReportStatisticsResponse)
async def get_sales_statistics(
    start_date: date = Query(..., description="开始日期"),
    end_date: date = Query(..., description="结束日期"),
    store_id: Optional[uuid.UUID] = Query(None, description="门店ID"),
    channel_id: Optional[uuid.UUID] = Query(None, description="渠道ID"),
    group_by: str = Query("month", description="分组方式: month, day"),
    data_type: str = Query(None, description="数据类型: profit"),
    project: Project = Depends(get_current_project),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    获取销售统计数据
    """
    try:
        # 转换日期为datetime
        start_datetime = datetime.combine(start_date, datetime.min.time())
        end_datetime = datetime.combine(end_date, datetime.max.time())

        # 获取销售统计数据
        statistics = await SalesReportService.get_sales_statistics(
            db=db,
            project_id=project.id,
            start_date=start_datetime,
            end_date=end_datetime,
            store_id=store_id,
            channel_id=channel_id,
            group_by=group_by
        )

        # 构建响应数据
        items = []
        for stat in statistics:
            # 确保所有字段都存在，避免前端解析错误
            item = {
                "month": stat[group_by],
                "total_sales": stat["total_sales"],
                "online_sales": stat["online_sales"],
                "offline_sales": stat["offline_sales"],
                "recharge_total": stat.get("recharge_total", 0),  # 添加充值售卡总额
                "total_orders": stat["total_orders"],
                "total_customers": stat["total_customers"],
                "channel_stats": stat.get("channel_stats", []),
                "payment_stats": stat.get("payment_stats", [])
            }

            # 如果是利润分析数据类型，添加成本和利润数据
            if data_type == "profit":
                # 获取成本数据 - 从产品成本或采购订单中获取
                # 这里使用简化的计算方式：成本为销售额的60%
                total_cost = stat["total_sales"] * 0.6

                # 计算利润和利润率
                profit = stat["total_sales"] - total_cost
                profit_rate = (profit / stat["total_sales"]) * 100 if stat["total_sales"] > 0 else 0

                # 添加到响应数据
                item["total_cost"] = round(total_cost, 2)
                item["profit"] = round(profit, 2)
                item["profit_rate"] = round(profit_rate, 2)

            items.append(item)

        return {
            "items": items,
            "total": len(items)
        }
    except Exception as e:
        logger.error(f"获取销售统计数据失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取销售统计数据失败: {str(e)}"
        )

@router.get("/{report_id}", response_model=SalesReportResponse)
async def get_sales_report(
    report_id: uuid.UUID = Path(...),
    project: Project = Depends(get_current_project),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    获取销售上报详情
    """
    try:
        # 获取销售上报
        report = await SalesReportService.get_sales_report_by_id(db, report_id)
        if not report:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="销售上报不存在"
            )

        # 检查销售上报是否属于当前项目
        if report.project_id != project.id:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="无权访问此销售上报"
            )

        # 获取销售上报详情
        report_detail = await SalesReportService.get_sales_report_with_details(db, report_id)
        if not report_detail:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="销售上报不存在"
            )

        return parse_obj_as(SalesReportResponse, report_detail)
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取销售上报详情失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取销售上报详情失败: {str(e)}"
        )

@router.put("/{report_id}", response_model=SalesReportResponse)
async def update_sales_report(
    report_id: uuid.UUID = Path(...),
    report_data: SalesReportUpdate = None,
    project: Project = Depends(get_current_project),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    更新销售上报
    """
    try:
        # 获取销售上报
        report = await SalesReportService.get_sales_report_by_id(db, report_id)
        if not report:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="销售上报不存在"
            )

        # 检查销售上报是否属于当前项目
        if report.project_id != project.id:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="无权修改此销售上报"
            )

        # 检查是否为创建者
        if report.created_by != current_user.id:
            # 检查用户是否有管理员权限
            if not current_user.is_system_admin and not current_user.is_tenant_admin:
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail="只有创建者或管理员可以修改销售上报"
                )

        # 更新销售上报
        updated_report = await SalesReportService.update_sales_report(
            db=db,
            report_id=report_id,
            report_data=report_data,
            user_id=current_user.id
        )

        if not updated_report:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="销售上报不存在"
            )

        # 获取销售上报详情
        report_detail = await SalesReportService.get_sales_report_with_details(db, report_id)
        if not report_detail:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="销售上报不存在"
            )

        return parse_obj_as(SalesReportResponse, report_detail)
    except ValueError as e:
        logger.error(f"更新销售上报失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"更新销售上报失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"更新销售上报失败: {str(e)}"
        )

@router.put("/{report_id}/status", response_model=SalesReportResponse)
async def update_sales_report_status(
    report_id: uuid.UUID = Path(...),
    status_data: SalesReportStatusUpdate = None,
    project: Project = Depends(get_current_project),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    更新销售上报状态
    """
    try:
        # 获取销售上报
        report = await SalesReportService.get_sales_report_by_id(db, report_id)
        if not report:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="销售上报不存在"
            )

        # 检查销售上报是否属于当前项目
        if report.project_id != project.id:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="无权修改此销售上报状态"
            )

        # 检查权限
        new_status = status_data.status
        if new_status == "submitted":
            # 提交操作：只有创建者可以提交
            if report.created_by != current_user.id:
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail="只有创建者可以提交销售上报"
                )
        elif new_status in ["approved", "rejected"]:
            # 审核操作：只有管理员可以审核
            if not current_user.is_system_admin and not current_user.is_tenant_admin:
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail="只有管理员可以审核销售上报"
                )
        elif new_status == "draft":
            # 撤回操作：只有创建者可以撤回
            if report.created_by != current_user.id:
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail="只有创建者可以撤回销售上报"
                )

        # 更新销售上报状态
        updated_report = await SalesReportService.update_sales_report_status(
            db=db,
            report_id=report_id,
            status_data=status_data,
            user_id=current_user.id
        )

        if not updated_report:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="销售上报不存在"
            )

        # 获取销售上报详情
        report_detail = await SalesReportService.get_sales_report_with_details(db, report_id)
        if not report_detail:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="销售上报不存在"
            )

        return parse_obj_as(SalesReportResponse, report_detail)
    except ValueError as e:
        logger.error(f"更新销售上报状态失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"更新销售上报状态失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"更新销售上报状态失败: {str(e)}"
        )

@router.delete("/{report_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_sales_report(
    report_id: uuid.UUID = Path(...),
    project: Project = Depends(get_current_project),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    删除销售上报
    """
    try:
        # 获取销售上报
        report = await SalesReportService.get_sales_report_by_id(db, report_id)
        if not report:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="销售上报不存在"
            )

        # 检查销售上报是否属于当前项目
        if report.project_id != project.id:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="无权删除此销售上报"
            )

        # 检查是否为创建者
        if report.created_by != current_user.id:
            # 检查用户是否有管理员权限
            if not current_user.is_system_admin and not current_user.is_tenant_admin:
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail="只有创建者或管理员可以删除销售上报"
                )

        # 删除销售上报
        success = await SalesReportService.delete_sales_report(db, report_id)

        if not success:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="销售上报不存在"
            )

        return None
    except ValueError as e:
        logger.error(f"删除销售上报失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"删除销售上报失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"删除销售上报失败: {str(e)}"
        )

@router.post("/{report_id}/items", response_model=SalesReportResponse)
async def add_sales_report_item(
    report_id: uuid.UUID = Path(...),
    item_data: SalesReportItemCreate = None,
    project: Project = Depends(get_current_project),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    添加销售上报项
    """
    try:
        # 获取销售上报
        report = await SalesReportService.get_sales_report_by_id(db, report_id)
        if not report:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="销售上报不存在"
            )

        # 检查销售上报是否属于当前项目
        if report.project_id != project.id:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="无权修改此销售上报"
            )

        # 检查是否为创建者
        if report.created_by != current_user.id:
            # 检查用户是否有管理员权限
            if not current_user.is_system_admin and not current_user.is_tenant_admin:
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail="只有创建者或管理员可以修改销售上报"
                )

        # 添加销售上报项
        updated_report, _ = await SalesReportService.add_sales_report_item(
            db=db,
            report_id=report_id,
            item_data=item_data.dict(),
            user_id=current_user.id
        )

        if not updated_report:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="销售上报不存在"
            )

        # 获取销售上报详情
        report_detail = await SalesReportService.get_sales_report_with_details(db, report_id)
        if not report_detail:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="销售上报不存在"
            )

        return parse_obj_as(SalesReportResponse, report_detail)
    except ValueError as e:
        logger.error(f"添加销售上报项失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"添加销售上报项失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"添加销售上报项失败: {str(e)}"
        )

@router.put("/{report_id}/items/{item_id}", response_model=SalesReportResponse)
async def update_sales_report_item(
    report_id: uuid.UUID = Path(...),
    item_id: uuid.UUID = Path(...),
    item_data: SalesReportItemUpdate = None,
    project: Project = Depends(get_current_project),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    更新销售上报项
    """
    try:
        # 获取销售上报
        report = await SalesReportService.get_sales_report_by_id(db, report_id)
        if not report:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="销售上报不存在"
            )

        # 检查销售上报是否属于当前项目
        if report.project_id != project.id:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="无权修改此销售上报"
            )

        # 检查是否为创建者
        if report.created_by != current_user.id:
            # 检查用户是否有管理员权限
            if not current_user.is_system_admin and not current_user.is_tenant_admin:
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail="只有创建者或管理员可以修改销售上报"
                )

        # 更新销售上报项
        updated_report, _ = await SalesReportService.update_sales_report_item(
            db=db,
            item_id=item_id,
            item_data=item_data.dict(exclude_unset=True),
            user_id=current_user.id
        )

        if not updated_report:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="销售上报项不存在"
            )

        # 获取销售上报详情
        report_detail = await SalesReportService.get_sales_report_with_details(db, report_id)
        if not report_detail:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="销售上报不存在"
            )

        return parse_obj_as(SalesReportResponse, report_detail)
    except ValueError as e:
        logger.error(f"更新销售上报项失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"更新销售上报项失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"更新销售上报项失败: {str(e)}"
        )

@router.delete("/{report_id}/items/{item_id}", response_model=SalesReportResponse)
async def delete_sales_report_item(
    report_id: uuid.UUID = Path(...),
    item_id: uuid.UUID = Path(...),
    project: Project = Depends(get_current_project),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    删除销售上报项
    """
    try:
        # 获取销售上报
        report = await SalesReportService.get_sales_report_by_id(db, report_id)
        if not report:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="销售上报不存在"
            )

        # 检查销售上报是否属于当前项目
        if report.project_id != project.id:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="无权修改此销售上报"
            )

        # 检查是否为创建者
        if report.created_by != current_user.id:
            # 检查用户是否有管理员权限
            if not current_user.is_system_admin and not current_user.is_tenant_admin:
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail="只有创建者或管理员可以修改销售上报"
                )

        # 删除销售上报项
        updated_report = await SalesReportService.delete_sales_report_item(
            db=db,
            item_id=item_id,
            user_id=current_user.id
        )

        if not updated_report:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="销售上报项不存在"
            )

        # 获取销售上报详情
        report_detail = await SalesReportService.get_sales_report_with_details(db, report_id)
        if not report_detail:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="销售上报不存在"
            )

        return parse_obj_as(SalesReportResponse, report_detail)
    except ValueError as e:
        logger.error(f"删除销售上报项失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"删除销售上报项失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"删除销售上报项失败: {str(e)}"
        )

