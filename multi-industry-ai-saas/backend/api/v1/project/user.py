import logging
from fastapi import APIRouter, Depends, HTTPException, status, Path
from sqlalchemy.orm import Session, selectinload
from typing import List, Any
from db.database import get_db
from api.deps import get_current_project, get_current_user
from models.user import User
from models.project import Project
from models.project_user import ProjectUser
from schemas.user import UserProfileResponse, UserProfileUpdate, UserCreate
from services.role import RoleService
from sqlalchemy.future import select
from models.store import ProjectUserStore, Store
from models.role import Role
from sqlalchemy import and_

router = APIRouter()

@router.get("", response_model=List[UserProfileResponse])
async def get_project_users(
    project: Project = Depends(get_current_project),
    db: Session = Depends(get_db),
    role: str = None  # 添加角色过滤参数
):
    """获取项目下所有用户列表，可选按角色过滤"""
    # 构建基础查询
    query = (
        select(ProjectUser)
        .options(
            selectinload(ProjectUser.user),
            selectinload(ProjectUser.project_user_stores).selectinload(ProjectUserStore.store),
            selectinload(ProjectUser.role)
        )
        .where(ProjectUser.project_id == project.id)
    )

    # 如果指定了角色，添加角色过滤条件
    if role:
        logging.info(f"按角色过滤用户: {role}")
        query = (
            query
            .join(Role, ProjectUser.role_id == Role.id)
            .where(Role.code == role)
        )

    # 执行查询
    result = await db.execute(query)
    project_users = result.scalars().all()

    # 构建响应
    users = []
    for pu in project_users:
        if not pu.user:
            continue
        store_ids = [pus.store_id for pus in pu.project_user_stores]
        store_names = [pus.store.name for pus in pu.project_user_stores if pus.store]
        role_code = pu.role.code if pu.role else None
        role_name = pu.role.name if pu.role else None
        users.append(UserProfileResponse(success=True, data={
            "id": str(pu.user.id),
            "username": pu.user.username,
            "email": pu.user.email,
            "name": getattr(pu.user, "full_name", None),
            "phone": pu.user.phone,
            "status": pu.user.status,
            "store_ids": store_ids,
            "store_names": store_names,
            "role": role_code,
            "role_name": role_name
        }))
    return users

@router.post("", response_model=UserProfileResponse, status_code=201)
async def create_project_user(
    user_in: dict,  # 直接接收前端传递的所有字段
    project: Project = Depends(get_current_project),
    db: Session = Depends(get_db)
):
    """创建项目用户（兼容前端参数结构）"""
    try:
        logging.info(f"创建项目用户: {user_in}")
        logging.info(f"项目ID: {project.id}")

        # 1. 检查用户名和邮箱是否已存在
        result = await db.execute(
            select(User).where(
                (User.username == user_in["username"]) |
                (User.email == user_in["email"])
            )
        )
        existing_user = result.scalars().first()

        if existing_user:
            if existing_user.username == user_in["username"]:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="用户名已存在"
                )
            else:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="邮箱已存在"
                )

        # 2. 获取角色ID
        role_code = user_in.get("role")
        role_id = None
        if role_code:
            role = await RoleService.get_role_by_code(db, project.id, role_code)
            if role:
                role_id = role.id
                logging.info(f"找到角色: {role.name} ({role.code}), ID: {role.id}")
            else:
                logging.warning(f"未找到角色代码为 {role_code} 的角色，尝试创建")
                # 如果角色不存在，创建一个新角色
                from schemas.role import RoleCreate
                try:
                    role_name = role_code.replace('_', ' ').title()
                    new_role = await RoleService.create_role(
                        db,
                        RoleCreate(
                            project_id=project.id,
                            name=role_name,
                            code=role_code,
                            description=f"自动创建的角色: {role_name}",
                            permissions={},
                            is_system_role=False
                        )
                    )
                    role_id = new_role.id
                    logging.info(f"创建角色成功: {new_role.name} ({new_role.code}), ID: {new_role.id}")
                except Exception as e:
                    logging.error(f"创建角色失败: {e}")
                    raise HTTPException(
                        status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                        detail=f"创建角色失败: {str(e)}"
                    )

        # 如果没有指定角色，但设置了项目管理员标志，使用项目管理员角色
        is_project_admin = bool(user_in.get("is_project_admin"))
        if is_project_admin and not role_id:
            # 查找项目管理员角色
            project_admin_role = await RoleService.get_role_by_code(db, project.id, "project_admin")
            if project_admin_role:
                role_id = project_admin_role.id
                logging.info(f"用户是项目管理员，使用项目管理员角色: {project_admin_role.id}")
            else:
                # 创建项目管理员角色
                from schemas.role import RoleCreate
                try:
                    project_admin_role = await RoleService.create_role(
                        db,
                        RoleCreate(
                            project_id=project.id,
                            name="项目管理员",
                            code="project_admin",
                            description="项目管理员，拥有项目的所有权限",
                            permissions={
                                "project_view": True,
                                "project_edit": True,
                                "project_admin": True,
                                "user_manage": True,
                                "role_manage": True,
                                "store_manage": True,
                                "product_manage": True,
                                "inventory_manage": True,
                                "purchase_manage": True,
                                "finance_manage": True,
                                "report_view": True
                            },
                            is_system_role=True
                        )
                    )
                    role_id = project_admin_role.id
                    logging.info(f"创建项目管理员角色成功: {project_admin_role.id}")
                except Exception as e:
                    logging.error(f"创建项目管理员角色失败: {e}")
                    raise HTTPException(
                        status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                        detail=f"创建项目管理员角色失败: {str(e)}"
                    )

        # 如果仍然没有角色ID，使用默认角色
        if not role_id:
            # 查找或创建默认角色（普通用户）
            default_role = await RoleService.get_role_by_code(db, project.id, "user")
            if default_role:
                role_id = default_role.id
                logging.info(f"使用默认角色: {default_role.name} ({default_role.code}), ID: {default_role.id}")
            else:
                # 创建默认角色
                from schemas.role import RoleCreate
                try:
                    default_role = await RoleService.create_role(
                        db,
                        RoleCreate(
                            project_id=project.id,
                            name="普通用户",
                            code="user",
                            description="普通用户，拥有基本权限",
                            permissions={
                                "project_view": True
                            },
                            is_system_role=True
                        )
                    )
                    role_id = default_role.id
                    logging.info(f"创建默认角色成功: {default_role.name} ({default_role.code}), ID: {default_role.id}")
                except Exception as e:
                    logging.error(f"创建默认角色失败: {e}")
                    raise HTTPException(
                        status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                        detail=f"创建默认角色失败: {str(e)}"
                    )

        # 3. 创建 User
        user = User(
            username=user_in["username"],
            email=user_in["email"],
            full_name=user_in.get("name"),
            phone=user_in.get("phone"),
            status=user_in.get("status", "active")
        )
        user.password_hash = User.get_password_hash(user_in["password"])
        db.add(user)
        await db.flush()  # 获取 user.id
        logging.info(f"创建用户成功，用户ID: {user.id}")

        # 4. 创建 ProjectUser 关联
        project_user = ProjectUser(
            user_id=user.id,
            project_id=project.id,
            role_id=role_id,  # 确保角色ID不为空
            is_admin=is_project_admin,
            status=user_in.get("status", "active")
        )
        db.add(project_user)
        await db.flush()
        logging.info(f"创建项目用户关联成功，ID: {project_user.id}, 角色ID: {role_id}, 是否管理员: {is_project_admin}")

        # 5. 多门店关联
        store_ids = user_in.get("store_ids") or []
        for store_id in store_ids:
            db.add(ProjectUserStore(project_user_id=project_user.id, store_id=store_id))
            logging.info(f"添加门店关联: {store_id}")

        # 5.1 如果用户角色是门店管理员，自动设置为门店负责人
        if role_code == "store_admin" and store_ids:
            from services.store import ProjectUserStoreService
            for store_id in store_ids:
                try:
                    await ProjectUserStoreService.set_manager(db, store_id, project_user.id)
                    logging.info(f"自动设置门店管理员 {user_in['username']} 为门店 {store_id} 的负责人")
                except Exception as e:
                    logging.warning(f"设置门店负责人失败: {e}")

        # 6. 提交事务
        await db.commit()
        await db.refresh(user)
        await db.refresh(project_user)

        # 7. 验证关联是否创建成功
        result = await db.execute(
            select(ProjectUser)
            .where(
                and_(
                    ProjectUser.user_id == user.id,
                    ProjectUser.project_id == project.id
                )
            )
        )
        created_project_user = result.scalars().first()

        if not created_project_user:
            logging.error(f"项目用户关联创建失败，未找到关联记录")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="项目用户关联创建失败"
            )

        # 8. 获取完整的用户信息
        # 获取用户关联的门店
        result = await db.execute(
            select(ProjectUserStore)
            .where(ProjectUserStore.project_user_id == project_user.id)
        )
        store_ids = [str(pus.store_id) for pus in result.scalars().all()]

        # 获取用户角色
        role = None
        if project_user.role_id:
            result = await db.execute(
                select(Role).where(Role.id == project_user.role_id)
            )
            role = result.scalars().first()

        # 构建响应（不包含密码）
        response_data = {
            "id": str(user.id),
            "username": user.username,
            "email": user.email,
            "name": user.full_name,
            "phone": user.phone,
            "status": user.status,
            "role": role.code if role else None,
            "role_name": role.name if role else None,
            "is_project_admin": project_user.is_admin,
            "store_ids": store_ids
        }

        # 确保不返回密码
        if "password" in response_data:
            del response_data["password"]

        return UserProfileResponse(success=True, data=response_data)
    except HTTPException:
        await db.rollback()
        raise
    except Exception as e:
        logging.error(f"创建项目用户失败: {e}")
        await db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"创建项目用户失败: {str(e)}"
        )


@router.put("", response_model=List[UserProfileResponse])
async def update_project_users(
    updates: List[UserProfileUpdate],
    project: Project = Depends(get_current_project),
    db: Session = Depends(get_db)
):
    """批量更新项目下用户信息"""
    try:
        updated_users = []
        for update in updates:
            # 查询用户
            result = await db.execute(
                select(User).where(User.id == update.id)
            )
            user = result.scalars().first()

            if not user:
                continue

            # 查询项目用户关联
            result = await db.execute(
                select(ProjectUser).where(
                    and_(
                        ProjectUser.user_id == update.id,
                        ProjectUser.project_id == project.id
                    )
                )
            )
            project_user = result.scalars().first()

            if not project_user:
                continue

            # 更新用户信息
            update_data = update.dict(exclude_unset=True)

            # 特殊处理密码字段
            if "password" in update_data:
                password = update_data.pop("password")
                if password:
                    user.password_hash = User.get_password_hash(password)

            # 更新其他字段
            for field, value in update_data.items():
                if hasattr(user, field):
                    setattr(user, field, value)

            db.add(user)

            # 获取用户角色
            role = None
            if project_user.role_id:
                result = await db.execute(
                    select(Role).where(Role.id == project_user.role_id)
                )
                role = result.scalars().first()

            # 构建响应数据
            user_data = {
                "id": str(user.id),
                "username": user.username,
                "email": user.email,
                "name": user.full_name,
                "phone": user.phone,
                "status": user.status,
                "role": role.code if role else None,
                "role_name": role.name if role else None,
                "is_project_admin": project_user.is_admin
            }

            updated_users.append(UserProfileResponse(success=True, data=user_data))

        await db.commit()
        return updated_users
    except Exception as e:
        logging.error(f"批量更新用户失败: {e}")
        await db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"批量更新用户失败: {str(e)}"
        )


@router.put("/{user_id}", response_model=UserProfileResponse)
async def update_project_user(
    user_id: str = Path(...),
    user_data: dict = None,
    project: Project = Depends(get_current_project),
    db: Session = Depends(get_db)
):
    """更新单个项目用户信息"""
    try:
        logging.info(f"更新用户 {user_id} 在项目 {project.id} 中的信息: {user_data}")

        # 1. 查询用户
        result = await db.execute(
            select(User).where(User.id == user_id)
        )
        user = result.scalars().first()

        if not user:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="用户不存在"
            )

        # 2. 查询项目用户关联
        result = await db.execute(
            select(ProjectUser).where(
                and_(
                    ProjectUser.user_id == user_id,
                    ProjectUser.project_id == project.id
                )
            )
        )
        project_user = result.scalars().first()

        if not project_user:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="用户在当前项目中不存在"
            )

        # 3. 更新用户基本信息
        if user_data.get("name"):
            user.full_name = user_data.get("name")
        if user_data.get("phone"):
            user.phone = user_data.get("phone")
        if user_data.get("email"):
            user.email = user_data.get("email")
        if user_data.get("status"):
            user.status = user_data.get("status")
            project_user.status = user_data.get("status")

        # 特殊处理密码字段
        if user_data.get("password"):
            password = user_data.get("password")
            if password:
                user.password_hash = User.get_password_hash(password)
                logging.info(f"更新用户密码成功")

        # 4. 更新项目用户关联信息
        if "is_project_admin" in user_data:
            project_user.is_admin = bool(user_data.get("is_project_admin"))

        # 5. 更新角色
        if user_data.get("role"):
            role_code = user_data.get("role")
            role = await RoleService.get_role_by_code(db, project.id, role_code)

            if role:
                project_user.role_id = role.id
                logging.info(f"更新用户角色为: {role.name} ({role.code})")
            else:
                logging.warning(f"未找到角色代码为 {role_code} 的角色，尝试创建")
                # 如果角色不存在，创建一个新角色
                from schemas.role import RoleCreate
                try:
                    role_name = role_code.replace('_', ' ').title()
                    new_role = await RoleService.create_role(
                        db,
                        RoleCreate(
                            project_id=project.id,
                            name=role_name,
                            code=role_code,
                            description=f"自动创建的角色: {role_name}",
                            permissions={},
                            is_system_role=False
                        )
                    )
                    project_user.role_id = new_role.id
                    logging.info(f"创建角色成功: {new_role.name} ({new_role.code})")
                except Exception as e:
                    logging.error(f"创建角色失败: {e}")

        # 6. 更新门店关联
        if "store_ids" in user_data:
            store_ids = user_data.get("store_ids") or []

            # 删除现有的门店关联
            await db.execute(
                ProjectUserStore.__table__.delete().where(ProjectUserStore.project_user_id == project_user.id)
            )

            # 添加新的门店关联
            for store_id in store_ids:
                db.add(ProjectUserStore(project_user_id=project_user.id, store_id=store_id))

            # 6.1 如果用户角色是门店管理员，自动设置为门店负责人
            if role_code == "store_admin" and store_ids:
                from services.store import ProjectUserStoreService
                for store_id in store_ids:
                    try:
                        await ProjectUserStoreService.set_manager(db, store_id, project_user.id)
                        logging.info(f"自动设置门店管理员为门店 {store_id} 的负责人")
                    except Exception as e:
                        logging.warning(f"设置门店负责人失败: {e}")

        # 7. 保存更改
        db.add(user)
        db.add(project_user)
        await db.commit()
        await db.refresh(user)
        await db.refresh(project_user)

        # 8. 获取更新后的用户信息
        # 获取用户关联的门店
        result = await db.execute(
            select(ProjectUserStore)
            .where(ProjectUserStore.project_user_id == project_user.id)
        )
        store_ids = [str(pus.store_id) for pus in result.scalars().all()]

        # 获取用户角色
        role = None
        if project_user.role_id:
            result = await db.execute(
                select(Role).where(Role.id == project_user.role_id)
            )
            role = result.scalars().first()

        # 构建响应
        user_data = {
            "id": str(user.id),
            "username": user.username,
            "email": user.email,
            "name": user.full_name,
            "phone": user.phone,
            "status": user.status,
            "role": role.code if role else None,
            "role_name": role.name if role else None,
            "is_project_admin": project_user.is_admin,
            "store_ids": store_ids
        }

        return UserProfileResponse(success=True, data=user_data)
    except HTTPException:
        raise
    except Exception as e:
        logging.error(f"更新用户失败: {e}")
        await db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"更新用户失败: {str(e)}"
        )

@router.delete("/{user_id}", status_code=204)
async def delete_project_user(
    user_id: str = Path(...),
    project: Project = Depends(get_current_project),
    db: Session = Depends(get_db)
):
    """删除项目用户（同时删除项目关联和全局用户）"""
    try:
        logging.info(f"删除用户 {user_id} 在项目 {project.id} 中的关联")

        # 1. 查找 ProjectUser
        result = await db.execute(
            select(ProjectUser).where(
                and_(
                    ProjectUser.user_id == user_id,
                    ProjectUser.project_id == project.id
                )
            )
        )
        project_user = result.scalars().first()

        if not project_user:
            raise HTTPException(status_code=404, detail="项目用户不存在")

        # 2. 删除 ProjectUserStore 关联
        try:
            await db.execute(
                ProjectUserStore.__table__.delete().where(ProjectUserStore.project_user_id == project_user.id)
            )
            logging.info(f"删除用户门店关联成功")
        except Exception as e:
            logging.error(f"删除用户门店关联失败: {e}")
            raise

        # 3. 删除 ProjectUser
        try:
            await db.delete(project_user)
            logging.info(f"删除项目用户关联成功")
        except Exception as e:
            logging.error(f"删除项目用户关联失败: {e}")
            raise

        # 4. 检查用户是否还有其他项目关联
        result = await db.execute(
            select(ProjectUser).where(ProjectUser.user_id == user_id)
        )
        other_project_users = result.scalars().all()

        if not other_project_users:
            # 如果用户没有其他项目关联，删除用户
            try:
                # 查找用户
                result = await db.execute(
                    select(User).where(User.id == user_id)
                )
                user = result.scalars().first()

                if user:
                    await db.delete(user)
                    logging.info(f"删除用户成功")
                else:
                    logging.warning(f"用户 {user_id} 不存在")
            except Exception as e:
                logging.error(f"删除用户失败: {e}")
                raise

        # 5. 提交事务
        await db.commit()
        return None
    except HTTPException:
        await db.rollback()
        raise
    except Exception as e:
        logging.error(f"删除用户失败: {e}")
        await db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"删除用户失败: {str(e)}"
        )


@router.get("/projects", response_model=Any)
async def get_user_projects(
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """获取用户关联的所有项目"""
    try:
        # 查询用户关联的所有项目
        result = await db.execute(
            select(Project)
            .join(ProjectUser, Project.id == ProjectUser.project_id)
            .where(ProjectUser.user_id == current_user.id)
            .order_by(Project.name)
        )
        projects = result.scalars().all()

        # 获取每个项目中用户的角色
        project_list = []
        for project in projects:
            # 查询用户在项目中的角色
            result = await db.execute(
                select(ProjectUser)
                .join(Role, ProjectUser.role_id == Role.id, isouter=True)
                .where(
                    and_(
                        ProjectUser.user_id == current_user.id,
                        ProjectUser.project_id == project.id
                    )
                )
            )
            project_user = result.scalars().first()

            # 获取角色信息
            role = None
            role_name = None
            is_admin = False

            if project_user:
                is_admin = project_user.is_admin

                if project_user.role_id:
                    result = await db.execute(
                        select(Role).where(Role.id == project_user.role_id)
                    )
                    role_obj = result.scalars().first()
                    if role_obj:
                        role = role_obj.code
                        role_name = role_obj.name

            # 构建项目信息
            project_info = {
                "id": str(project.id),
                "name": project.name,
                "code": project.code,
                "description": project.description,
                "industry_type": project.industry_type,
                "status": project.status,
                "created_at": project.created_at,
                "role": role,
                "role_name": role_name,
                "is_admin": is_admin
            }

            project_list.append(project_info)

        return {"success": True, "data": project_list}
    except Exception as e:
        logging.error(f"获取用户项目列表失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取用户项目列表失败: {str(e)}"
        )