#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
分享助手 API
"""

import logging
import uuid
import json
from typing import Optional, List, Dict, Any
from fastapi import APIRouter, Depends, HTTPException, Query, Path, status, Form, File, UploadFile
from sqlalchemy.ext.asyncio import AsyncSession

from db.database import get_db
from services.ai import AIAssistantService, AIChatService
from schemas.ai import (
    AIAssistantResponse,
    AIAssistantChatRequest,
    AIAssistantChatResponse,
)

logger = logging.getLogger(__name__)
router = APIRouter()

@router.get("/shared/assistant/{assistant_id}")
async def get_shared_assistant(
    assistant_id: uuid.UUID = Path(...),
    db: AsyncSession = Depends(get_db),
):
    """
    获取分享的 AI 助手信息
    """
    try:
        assistant = await AIAssistantService.get_assistant(db=db, assistant_id=assistant_id)

        if not assistant:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"AI 助手 ID '{assistant_id}' 不存在",
            )

        # 检查助手是否公开
        if not assistant.is_public:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="该助手未公开分享",
            )

        return {
            "success": True,
            "message": "获取分享助手信息成功",
            "data": assistant,
        }
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取分享助手信息失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取分享助手信息失败: {str(e)}",
        )

@router.post("/shared/assistant/{assistant_id}/chat")
async def chat_with_shared_assistant(
    assistant_id: uuid.UUID = Path(...),
    content: str = Form(..., description="消息内容"),
    content_type: str = Form("text", description="内容类型"),
    file: Optional[UploadFile] = File(None, description="上传的文件"),
    db: AsyncSession = Depends(get_db),
):
    """
    与分享的 AI 助手对话
    """
    try:
        # 获取助手信息
        assistant = await AIAssistantService.get_assistant(db=db, assistant_id=assistant_id)

        if not assistant:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"AI 助手 ID '{assistant_id}' 不存在",
            )

        # 检查助手是否公开
        if not assistant.is_public:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="该助手未公开分享",
            )

        # 检查助手状态
        if assistant.status != "active":
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="该助手当前不可用",
            )

        # 处理文件上传
        file_ids = []
        if file:
            # 这里应该实现文件上传逻辑
            # file_id = await upload_file(file)
            # file_ids.append(file_id)
            pass

        # 为分享模式实现简化的MCP工具调用
        try:
            # 构建聊天消息
            chat_messages = []
            
            # 添加系统消息
            if assistant.instructions:
                chat_messages.append({
                    "role": "system",
                    "content": assistant.instructions
                })

            # 添加用户消息
            chat_messages.append({
                "role": "user",
                "content": content
            })

            # 获取MCP工具（如果配置了）
            available_tools = []
            
            # 获取MCP服务器工具
            if assistant.mcp_server_ids:
                logger.info(f"共享助理 {assistant_id} 配置了MCP服务器: {assistant.mcp_server_ids}")
                mcp_tools = await AIAssistantService.get_mcp_tools_from_servers(
                    db, assistant.project_id, assistant.mcp_server_ids
                )
                logger.info(f"获取到 {len(mcp_tools)} 个MCP工具")
                available_tools.extend(mcp_tools)
            else:
                logger.info(f"共享助理 {assistant_id} 没有配置MCP服务器")

            # 构建聊天请求参数
            chat_params = {
                "db": db,
                "project_id": assistant.project_id or assistant.tenant_id,
                "user_id": None,  # 分享模式下不需要用户ID
                "messages": chat_messages,
                "model_id": assistant.model_id,
                "config_id": assistant.config_id,
                "temperature": assistant.temperature,
                "max_tokens": assistant.max_tokens,
                "is_shared": True  # 标记为分享模式
            }
            
            # 如果有可用工具，添加到请求中
            if available_tools:
                chat_params["tools"] = available_tools
                chat_params["tool_choice"] = "auto"
            
            chat_response = await AIChatService.chat_completion(**chat_params)

            # 检查是否有工具调用
            tool_calls = []
            response_content = ""
            
            if "choices" in chat_response and chat_response["choices"]:
                message = chat_response["choices"][0].get("message", {})
                response_content = message.get("content", "")
                if "tool_calls" in message:
                    tool_calls = message["tool_calls"]

            # 处理工具调用
            if tool_calls:
                tool_results = []
                for tool_call in tool_calls:
                    function_name = tool_call["function"]["name"]
                    function_args = json.loads(tool_call["function"]["arguments"])
                    
                    # 查找对应的MCP服务器工具
                    tool_result = None
                    for tool_spec in available_tools:
                        if (tool_spec["function"]["name"] == function_name and 
                            "_mcp_server_id" in tool_spec):
                            try:
                                server_id = uuid.UUID(tool_spec["_mcp_server_id"])
                                tool_result = await AIAssistantService.execute_mcp_tool(
                                    db=db,
                                    server_id=server_id,
                                    tool_name=function_name,
                                    parameters=function_args,
                                    user_id=uuid.uuid4()  # 临时用户ID
                                )
                                break
                            except Exception as e:
                                logger.error(f"执行MCP工具失败: {e}")
                                tool_result = {"success": False, "error": str(e)}
                                break
                    
                    # 构建工具结果
                    if tool_result:
                        if tool_result.get("success"):
                            content_result = json.dumps(tool_result.get("data", {}))
                        else:
                            content_result = f"工具执行失败: {tool_result.get('error', '未知错误')}"
                    else:
                        content_result = f"未找到工具: {function_name}"
                    
                    tool_results.append({
                        "tool_call_id": tool_call["id"],
                        "role": "tool",
                        "name": function_name,
                        "content": content_result
                    })

                # 添加工具调用消息和结果
                chat_messages.append(chat_response["choices"][0]["message"])
                chat_messages.extend(tool_results)
                
                # 重新调用AI获取最终回复
                final_chat_params = {
                    "db": db,
                    "project_id": assistant.project_id or assistant.tenant_id,
                    "user_id": None,
                    "messages": chat_messages,
                    "model_id": assistant.model_id,
                    "config_id": assistant.config_id,
                    "temperature": assistant.temperature,
                    "max_tokens": assistant.max_tokens,
                    "is_shared": True
                }
                
                # 获取最终回复
                final_response = await AIChatService.chat_completion(**final_chat_params)
                
                if "choices" in final_response and final_response["choices"]:
                    response_content = final_response["choices"][0]["message"]["content"]

            result = {
                "response": response_content,
                "usage": chat_response.get("usage", {}),
                "created_at": chat_response.get("created")
            }

            return {
                "success": True,
                "message": "对话成功",
                "data": {
                    "response": result.get("response", ""),
                    "content": result.get("response", ""),
                    "usage": {},  # 分享模式下不返回使用统计
                    "assistant_name": result.get("assistant_name", assistant.name),
                    "created_at": result.get("created_at")
                }
            }
        except Exception as chat_error:
            logger.error(f"AI聊天服务调用失败: {chat_error}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"AI聊天服务调用失败: {str(chat_error)}",
            )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"分享助手对话失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"分享助手对话失败: {str(e)}",
        )