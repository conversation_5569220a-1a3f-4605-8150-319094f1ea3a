-- 清理storage_files表中已标记为deleted的记录
-- 这是测试用的清理脚本，会物理删除软删除的记录

DO $$
DECLARE
    deleted_count INTEGER;
    freed_space BIGINT;
BEGIN
    -- 计算要删除的记录数和释放的空间
    SELECT COUNT(*), COALESCE(SUM(size), 0) 
    INTO deleted_count, freed_space
    FROM storage_files 
    WHERE status = 'deleted';
    
    IF deleted_count = 0 THEN
        RAISE NOTICE '没有找到已删除的文件记录';
        RETURN;
    END IF;
    
    RAISE NOTICE '准备清理 % 个已删除的文件记录，释放 % 字节空间', deleted_count, freed_space;
    
    -- 删除已标记为deleted的文件记录
    DELETE FROM storage_files WHERE status = 'deleted';
    
    RAISE NOTICE '成功清理了 % 个已删除的文件记录，释放了 % 字节（%.2f MB）空间', 
                 deleted_count, freed_space, freed_space::float / (1024 * 1024);
    
    -- 显示当前存储状态
    SELECT COUNT(*), COALESCE(SUM(size), 0) 
    INTO deleted_count, freed_space
    FROM storage_files 
    WHERE status = 'active';
    
    RAISE NOTICE '当前活跃文件: % 个，总大小: % 字节（%.2f MB）', 
                 deleted_count, freed_space, freed_space::float / (1024 * 1024);
    
END $$; 