import asyncio
import asyncpg
from core.config import settings

async def main():
    conn = await asyncpg.connect(settings.DATABASE_URL.replace('postgresql+asyncpg://', 'postgresql://'))
    try:
        # 获取阿里巴巴百炼提供商ID
        provider_id = await conn.fetchval("SELECT id FROM ai_providers WHERE name = 'alibaba_bailian'")
        if not provider_id:
            print('阿里巴巴百炼提供商不存在')
            return
        
        # 检查模型是否已存在
        exists = await conn.fetchval("SELECT COUNT(*) FROM ai_models WHERE provider_id = $1 AND name = 'qwen-vl-ocr-latest'", provider_id)
        if exists > 0:
            print('qwen-vl-ocr-latest模型已存在')
            return
            
        # 添加OCR模型
        result = await conn.execute("""
            INSERT INTO ai_models (
                id, provider_id, name, display_name, description, model_type,
                capabilities, context_window, token_limit,
                input_price_per_1k_tokens, output_price_per_1k_tokens,
                image_price_per_1k_tokens, average_response_time, rate_limit_per_minute,
                status, is_builtin, created_at, updated_at
            ) VALUES (
                gen_random_uuid(), $1, 'qwen-vl-ocr-latest', '通义千问VL OCR最新版',
                '阿里云百炼专业OCR视觉模型，专门优化用于文档和表格识别，支持高精度文字提取和表格数据解析',
                'vision',
                '{"supports_vision": true, "supports_ocr": true, "supports_table_extraction": true}',
                38192, 30000, 0.0008, 0.002, 0.0008, 2.5, 60,
                'active', true, NOW(), NOW()
            )
        """, provider_id)
        print(f'执行结果: {result}')
        print('成功添加OCR模型: qwen-vl-ocr-latest')
        print('上下文窗口: 38192, 最大输入: 30000, 最大输出: 8192')
    except Exception as e:
        print(f'执行失败: {e}')
    finally:
        await conn.close()

if __name__ == "__main__":
    asyncio.run(main()) 