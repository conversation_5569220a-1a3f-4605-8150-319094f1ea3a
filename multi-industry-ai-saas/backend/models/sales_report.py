#!/usr/bin/env python
# -*- coding: utf-8 -*-

import uuid
from datetime import datetime
from typing import List, Optional
from sqlalchemy import Column, String, Float, Integer, Boolean, ForeignKey, DateTime, Text, Enum
from sqlalchemy.dialects.postgresql import UUID, JSONB
from sqlalchemy.orm import relationship

from models.core import Base

class SalesReport(Base):
    """销售上报模型"""
    __tablename__ = "sales_reports"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    project_id = Column(UUID(as_uuid=True), ForeignKey("projects.id", ondelete="CASCADE"), nullable=False, index=True, comment="项目ID")
    store_id = Column(UUID(as_uuid=True), ForeignKey("stores.id", ondelete="SET NULL"), nullable=True, index=True, comment="门店ID")

    report_date = Column(DateTime, nullable=False, default=datetime.utcnow, comment="上报日期")
    report_type = Column(
        Enum("shift", "daily", "weekly", "monthly", name="sales_report_type"),
        nullable=False,
        default="daily",
        comment="上报类型: shift-班次, daily-日报, weekly-周报, monthly-月报"
    )

    total_sales = Column(Float, nullable=False, default=0.0, comment="销售总额")
    total_orders = Column(Integer, nullable=False, default=0, comment="订单总数")
    total_customers = Column(Integer, nullable=False, default=0, comment="顾客总数")

    online_sales = Column(Float, nullable=False, default=0.0, comment="线上销售额")
    offline_sales = Column(Float, nullable=False, default=0.0, comment="线下销售额")

    payment_methods = Column(JSONB, nullable=True, comment="支付方式统计")
    recharge_payment_methods = Column(JSONB, nullable=True, comment="充值/售卡支付方式统计")
    product_categories = Column(JSONB, nullable=True, comment="产品类别统计")
    hourly_sales = Column(JSONB, nullable=True, comment="分时段销售统计")
    channel_sales = Column(JSONB, nullable=True, comment="渠道销售统计")

    # 充值售卡相关字段
    recharge_amount = Column(Float, nullable=False, default=0.0, comment="会员充值金额")
    card_sales_amount = Column(Float, nullable=False, default=0.0, comment="储值卡销售金额")
    recharge_count = Column(Integer, nullable=False, default=0, comment="会员充值笔数")
    card_sales_count = Column(Integer, nullable=False, default=0, comment="储值卡销售数量")

    status = Column(
        Enum("draft", "submitted", "approved", "rejected", name="sales_report_status"),
        nullable=False,
        default="draft",
        comment="状态: draft-草稿, submitted-已提交, approved-已审核, rejected-已拒绝"
    )

    notes = Column(Text, nullable=True, comment="备注")
    reject_reason = Column(Text, nullable=True, comment="拒绝原因")

    created_by = Column(UUID(as_uuid=True), ForeignKey("users.id", ondelete="SET NULL"), nullable=True, comment="创建人ID")
    updated_by = Column(UUID(as_uuid=True), ForeignKey("users.id", ondelete="SET NULL"), nullable=True, comment="更新人ID")
    approved_by = Column(UUID(as_uuid=True), ForeignKey("users.id", ondelete="SET NULL"), nullable=True, comment="审核人ID")
    approved_at = Column(DateTime, nullable=True, comment="审核时间")

    created_at = Column(DateTime, nullable=False, default=datetime.utcnow, comment="创建时间")
    updated_at = Column(DateTime, nullable=False, default=datetime.utcnow, onupdate=datetime.utcnow, comment="更新时间")

    # 关系
    project = relationship("Project", back_populates="sales_reports")
    store = relationship("Store", back_populates="sales_reports")
    creator = relationship("User", foreign_keys=[created_by])
    updater = relationship("User", foreign_keys=[updated_by])
    approver = relationship("User", foreign_keys=[approved_by])
    items = relationship("SalesReportItem", back_populates="sales_report", cascade="all, delete-orphan")


class SalesReportItem(Base):
    """销售上报项模型"""
    __tablename__ = "sales_report_items"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    sales_report_id = Column(UUID(as_uuid=True), ForeignKey("sales_reports.id", ondelete="CASCADE"), nullable=False, index=True, comment="销售上报ID")
    product_id = Column(UUID(as_uuid=True), ForeignKey("products.id", ondelete="SET NULL"), nullable=True, index=True, comment="产品ID")

    product_name = Column(String(255), nullable=False, comment="产品名称")
    product_code = Column(String(50), nullable=True, comment="产品编码")
    product_category = Column(String(50), nullable=True, comment="产品类别")
    product_unit = Column(String(20), nullable=True, comment="产品单位")

    quantity = Column(Float, nullable=False, default=0.0, comment="销售数量")
    unit_price = Column(Float, nullable=False, default=0.0, comment="单价")
    discount_amount = Column(Float, nullable=False, default=0.0, comment="折扣金额")
    total_amount = Column(Float, nullable=False, default=0.0, comment="销售金额")

    notes = Column(Text, nullable=True, comment="备注")

    created_at = Column(DateTime, nullable=False, default=datetime.utcnow, comment="创建时间")
    updated_at = Column(DateTime, nullable=False, default=datetime.utcnow, onupdate=datetime.utcnow, comment="更新时间")

    # 关系
    sales_report = relationship("SalesReport", back_populates="items")
    product = relationship("Product")
