#!/usr/bin/env python
# -*- coding: utf-8 -*-

from sqlalchemy import Column, String, Integer, Float, Text, Boolean, ForeignKey, DateTime, UniqueConstraint, Enum
from sqlalchemy.dialects.postgresql import UUID, JSONB
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
import uuid
from datetime import datetime

from db.database import Base

class ArrivalConfirmation(Base):
    """到货确认模型"""
    __tablename__ = "arrival_confirmations"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    tenant_id = Column(UUID(as_uuid=True), ForeignKey("tenants.id", ondelete="SET NULL"), nullable=True, index=True)
    project_id = Column(UUID(as_uuid=True), ForeignKey("projects.id", ondelete="CASCADE"), nullable=False, index=True)
    store_id = Column(UUID(as_uuid=True), ForeignKey("stores.id", ondelete="CASCADE"), nullable=False, index=True)
    
    arrival_number = Column(String(50), nullable=False, index=True, comment="到货单号")
    arrival_type = Column(
        Enum("market", "warehouse", "third_party", name="arrival_type"),
        nullable=False,
        comment="到货类型: market-市场采购, warehouse-仓库配货, third_party-三方配送"
    )
    
    supplier_id = Column(UUID(as_uuid=True), ForeignKey("suppliers.id", ondelete="SET NULL"), nullable=True, index=True)
    warehouse_id = Column(UUID(as_uuid=True), ForeignKey("warehouses.id", ondelete="SET NULL"), nullable=True, index=True)
    purchase_order_id = Column(UUID(as_uuid=True), ForeignKey("purchase_orders.id", ondelete="SET NULL"), nullable=True, index=True, comment="关联采购单ID")
    
    arrival_date = Column(DateTime, nullable=False, default=datetime.utcnow, comment="到货日期")
    status = Column(
        Enum("pending", "confirmed", "rejected", name="arrival_status"),
        nullable=False,
        default="pending",
        comment="状态: pending-待确认, confirmed-已确认, rejected-已拒绝"
    )
    
    total_amount = Column(Float, nullable=False, default=0.0, comment="总金额")
    total_items = Column(Integer, nullable=False, default=0, comment="商品总数")
    
    notes = Column(Text, nullable=True, comment="备注")
    
    created_by = Column(UUID(as_uuid=True), ForeignKey("users.id", ondelete="SET NULL"), nullable=True, comment="创建人ID")
    updated_by = Column(UUID(as_uuid=True), ForeignKey("users.id", ondelete="SET NULL"), nullable=True, comment="更新人ID")
    created_at = Column(DateTime, nullable=False, default=datetime.utcnow, comment="创建时间")
    updated_at = Column(DateTime, nullable=False, default=datetime.utcnow, onupdate=datetime.utcnow, comment="更新时间")
    
    # 关联关系
    tenant = relationship("Tenant", backref="arrival_confirmations")
    project = relationship("Project", backref="arrival_confirmations")
    store = relationship("Store", backref="arrival_confirmations")
    supplier = relationship("Supplier", backref="arrival_confirmations")
    warehouse = relationship("Warehouse", backref="arrival_confirmations")
    purchase_order = relationship("PurchaseOrder", backref="arrival_confirmations")
    creator = relationship("User", foreign_keys=[created_by])
    updater = relationship("User", foreign_keys=[updated_by])
    items = relationship("ArrivalConfirmationItem", back_populates="arrival", cascade="all, delete-orphan")
    
    def __repr__(self):
        return f"<ArrivalConfirmation(id={self.id}, arrival_number='{self.arrival_number}')>"


class ArrivalConfirmationItem(Base):
    """到货确认明细模型"""
    __tablename__ = "arrival_confirmation_items"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    arrival_id = Column(UUID(as_uuid=True), ForeignKey("arrival_confirmations.id", ondelete="CASCADE"), nullable=False, index=True)
    product_id = Column(UUID(as_uuid=True), ForeignKey("products.id", ondelete="SET NULL"), nullable=True, index=True)
    
    product_name = Column(String(200), nullable=False, comment="商品名称")
    product_code = Column(String(50), nullable=True, comment="商品编码")
    specification = Column(String(200), nullable=True, comment="规格")
    unit = Column(String(20), nullable=True, comment="单位")
    
    expected_quantity = Column(Integer, nullable=False, comment="应到数量")
    actual_quantity = Column(Integer, nullable=False, comment="实收数量")
    price = Column(Float, nullable=False, comment="单价")
    amount = Column(Float, nullable=False, comment="金额")
    
    is_confirmed = Column(Boolean, default=True, comment="是否确认")
    notes = Column(Text, nullable=True, comment="备注")
    
    # 关联关系
    arrival = relationship("ArrivalConfirmation", back_populates="items")
    product = relationship("Product")
    
    def __repr__(self):
        return f"<ArrivalConfirmationItem(id={self.id}, product_name='{self.product_name}')>"


class DailyExpense(Base):
    """日常费用模型"""
    __tablename__ = "daily_expenses"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    tenant_id = Column(UUID(as_uuid=True), ForeignKey("tenants.id", ondelete="SET NULL"), nullable=True, index=True)
    project_id = Column(UUID(as_uuid=True), ForeignKey("projects.id", ondelete="CASCADE"), nullable=False, index=True)
    store_id = Column(UUID(as_uuid=True), ForeignKey("stores.id", ondelete="CASCADE"), nullable=False, index=True)
    
    expense_number = Column(String(50), nullable=False, index=True, comment="费用编号")
    expense_type = Column(
        Enum("misc", "material", "electricity", "internet", "other", name="expense_type"),
        nullable=False,
        comment="费用类型: misc-杂费, material-物料费, electricity-电费, internet-网费, other-其他"
    )
    
    expense_date = Column(DateTime, nullable=False, comment="费用日期")
    amount = Column(Float, nullable=False, comment="金额")
    description = Column(Text, nullable=True, comment="描述")
    
    receipt_url = Column(String(500), nullable=True, comment="凭据URL")
    has_receipt = Column(Boolean, default=False, comment="是否有凭据")
    
    created_by = Column(UUID(as_uuid=True), ForeignKey("users.id", ondelete="SET NULL"), nullable=True, comment="创建人ID")
    updated_by = Column(UUID(as_uuid=True), ForeignKey("users.id", ondelete="SET NULL"), nullable=True, comment="更新人ID")
    created_at = Column(DateTime, nullable=False, default=datetime.utcnow, comment="创建时间")
    updated_at = Column(DateTime, nullable=False, default=datetime.utcnow, onupdate=datetime.utcnow, comment="更新时间")
    
    # 关联关系
    tenant = relationship("Tenant", backref="daily_expenses")
    project = relationship("Project", backref="daily_expenses")
    store = relationship("Store", backref="daily_expenses")
    creator = relationship("User", foreign_keys=[created_by])
    updater = relationship("User", foreign_keys=[updated_by])
    
    def __repr__(self):
        return f"<DailyExpense(id={self.id}, expense_number='{self.expense_number}')>"
