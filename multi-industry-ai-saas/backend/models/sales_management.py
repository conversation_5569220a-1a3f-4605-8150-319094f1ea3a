from sqlalchemy import Column, String, Float, Boolean, ForeignKey, Text, JSON, Enum
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import relationship
import uuid
from datetime import datetime
from .base import TimestampMixin
from db.database import Base

class SalesChannel(Base, TimestampMixin):
    """销售渠道模型"""
    __tablename__ = "sales_channels"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    project_id = Column(UUID(as_uuid=True), ForeignKey("projects.id", ondelete="CASCADE"), nullable=False)
    name = Column(String(100), nullable=False, comment="渠道名称")
    code = Column(String(50), nullable=True, comment="渠道编码")
    type = Column(Enum("online", "offline", "other", name="channel_type"), default="offline", nullable=False, comment="渠道类型")
    description = Column(Text, nullable=True, comment="渠道描述")
    is_active = Column(Boolean, default=True, comment="是否启用")
    config = Column(JSON, nullable=True, comment="渠道配置")

    # 关联关系
    project = relationship("Project", back_populates="sales_channels")
    financial_reconciliations = relationship("FinancialReconciliation", back_populates="channel")

    def __repr__(self):
        return f"<SalesChannel {self.name}>"


class PaymentMethod(Base, TimestampMixin):
    """支付方式模型"""
    __tablename__ = "payment_methods"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    project_id = Column(UUID(as_uuid=True), ForeignKey("projects.id", ondelete="CASCADE"), nullable=False)
    name = Column(String(100), nullable=False, comment="支付方式名称")
    code = Column(String(50), nullable=True, comment="支付方式编码")
    icon = Column(String(255), nullable=True, comment="支付方式图标")
    description = Column(Text, nullable=True, comment="支付方式描述")
    is_active = Column(Boolean, default=True, comment="是否启用")
    is_default = Column(Boolean, default=False, comment="是否默认")
    sort_order = Column(Float, default=0, comment="排序顺序")
    config = Column(JSON, nullable=True, comment="支付方式配置")

    # 关联关系
    project = relationship("Project", back_populates="payment_methods")

    def __repr__(self):
        return f"<PaymentMethod {self.name}>"
