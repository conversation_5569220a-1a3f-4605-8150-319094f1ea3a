#!/usr/bin/env python
# -*- coding: utf-8 -*-

from .plugin import Plugin, PluginVersion, TenantPlugin, PluginPurchase
from .core import ServiceProvider, SubscriptionPlan, Industry
from .tenant import Tenant
from .tenant_data_sharing import TenantDataSharing, TenantDataSharingLog
from .user import User
from models.project import Project
from .project_user import ProjectUser
from .role import Role
from .sales_report import SalesReport, SalesReportItem
from .store import Store, StoreCategory, StoreRegion
from .warehouse import Warehouse, WarehouseSetting
from .sales_management import SalesChannel, PaymentMethod
from .finance import FinancialReconciliation, ReconciliationUpload
# 将 RoutineTask 放在 Project 和 Store 之后导入
from .routine_task import RoutineTask, TaskComment
from .area_survey import AreaSurvey
from models.user import (
    UserActivity,
    ThirdPartyAccount,
)
from models.notification import Notification
from models.notification_settings import NotificationSettings
from models.ai import (
    AIProvider, AIModel, AIConfig, AIUsage,
    AIKnowledgeBase, AIKnowledgeDocument, AIKnowledgeChunk,
    AIAssistant, AIAssistantThread, AIAssistantMessage, AIAssistantTool
)
from models.system_config import SystemConfig
from models.product import (
    ProductBrand,
    ProductCategory,
    Product
)
from models.inventory import InventoryItem
from models.supplier import (
    Supplier,
    SupplierProduct,
    SupplierEvaluation
)
from models.storage import (
    StorageFile,
    StorageFolder,
    StorageQuota,
    FileShare
)
from models.purchase_order import (
    PurchaseOrder,
    PurchaseOrderItem
)
from models.loss import Loss
from models.store_operations import ArrivalConfirmation, ArrivalConfirmationItem, DailyExpense
from models.inventory_transfer import StoreInventoryTransfer, StoreInventoryTransferItem
from models.monthly_inventory import MonthlyInventory, MonthlyInventorySummary, MonthlyInventoryUpload
from models.knowledge_base import KnowledgeCategory, KnowledgeDocument
from models.task import AsyncTask
from models.marketing_activity import MarketingActivity

# 导出所有模型
__all__ = [
    'ServiceProvider',
    'Tenant',
    'Project',
    'ProjectUser',
    'Industry',
    'SubscriptionPlan',
    'User',
    'UserActivity',
    'ThirdPartyAccount',
    'SystemConfig',
    'ProductBrand',
    'ProductCategory',
    'Product',
    'InventoryItem',
    'Supplier',
    'SupplierProduct',
    'SupplierEvaluation',
    'PurchaseOrder',
    'PurchaseOrderItem',
    'Loss',
    'ArrivalConfirmation',
    'ArrivalConfirmationItem',
    'DailyExpense',
    'StoreInventoryTransfer',
    'StoreInventoryTransferItem',
    'MonthlyInventory',
    'MonthlyInventorySummary',
    'MonthlyInventoryUpload',
    'StorageFile',
    'StorageFolder',
    'StorageQuota',
    'FileShare',
    'Plugin',
    'PluginVersion',
    'TenantPlugin',
    'PluginPurchase',
    'Role',
    'SalesReport',
    'SalesReportItem',
    'Store',
    'StoreCategory',
    'StoreRegion',
    'Warehouse',
    'WarehouseSetting',
    'SalesChannel',
    'PaymentMethod',
    'FinancialReconciliation',
    'ReconciliationUpload',
    'RoutineTask',
    'TaskComment',
    'Notification',
    'NotificationSettings',
    'AIProvider',
    'AIModel',
    'AIConfig',
    'AIUsage',
    'AIKnowledgeBase',
    'AIKnowledgeDocument',
    'AIKnowledgeChunk',
    'AIAssistant',
    'AIAssistantThread',
    'AIAssistantMessage',
    'AIAssistantTool',
    'TenantDataSharing',
    'TenantDataSharingLog',
    'KnowledgeCategory',
    'KnowledgeDocument',
    'AsyncTask',
    'AreaSurvey',
    'MarketingActivity',
]
