#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
系统 AI 整合设置模型
"""

import uuid
from datetime import datetime
from sqlalchemy import Column, String, Integer, Float, Boolean, ForeignKey, DateTime, func
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import relationship

from db.database import Base

class SystemAIIntegration(Base):
    """系统AI整合设置模型"""
    __tablename__ = "system_ai_integrations"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    tenant_id = Column(UUID(as_uuid=True), ForeignKey("tenants.id", ondelete="CASCADE"), nullable=True, comment="租户 ID，空表示全局设置")
    project_id = Column(UUID(as_uuid=True), ForeignKey("projects.id", ondelete="CASCADE"), nullable=True, comment="项目 ID，空表示租户级设置")
    
    # 默认模型配置
    default_chat_model_id = Column(UUID(as_uuid=True), ForeignKey("ai_models.id", ondelete="SET NULL"), nullable=True, comment="默认聊天模型ID")
    default_vision_model_id = Column(UUID(as_uuid=True), ForeignKey("ai_models.id", ondelete="SET NULL"), nullable=True, comment="默认视觉模型ID")
    default_audio_model_id = Column(UUID(as_uuid=True), ForeignKey("ai_models.id", ondelete="SET NULL"), nullable=True, comment="默认语音模型ID")
    default_embedding_model_id = Column(UUID(as_uuid=True), ForeignKey("ai_models.id", ondelete="SET NULL"), nullable=True, comment="默认嵌入模型ID")
    
    # 模型配置参数
    chat_temperature = Column(Float, nullable=False, default=0.7, comment="聊天模型温度参数")
    chat_max_tokens = Column(Integer, nullable=True, comment="聊天模型最大token数")
    vision_temperature = Column(Float, nullable=False, default=0.7, comment="视觉模型温度参数")
    vision_max_tokens = Column(Integer, nullable=True, comment="视觉模型最大token数")
    
    # 服务可用性控制
    chat_enabled = Column(Boolean, nullable=False, default=True, comment="启用聊天功能")
    vision_enabled = Column(Boolean, nullable=False, default=True, comment="启用视觉功能")
    audio_enabled = Column(Boolean, nullable=False, default=True, comment="启用语音功能")
    embedding_enabled = Column(Boolean, nullable=False, default=True, comment="启用嵌入功能")
    
    # 通用配置
    enable_fallback = Column(Boolean, nullable=False, default=True, comment="启用模型回退机制")
    request_timeout = Column(Integer, nullable=False, default=30, comment="请求超时时间(秒)")
    max_retries = Column(Integer, nullable=False, default=3, comment="最大重试次数")
    
    # 元数据
    created_at = Column(DateTime, nullable=False, default=func.now(), comment="创建时间")
    updated_at = Column(DateTime, nullable=False, default=func.now(), onupdate=func.now(), comment="更新时间")
    
    # 关联关系
    default_chat_model = relationship("AIModel", foreign_keys=[default_chat_model_id], lazy="select")
    default_vision_model = relationship("AIModel", foreign_keys=[default_vision_model_id], lazy="select")
    default_audio_model = relationship("AIModel", foreign_keys=[default_audio_model_id], lazy="select")
    default_embedding_model = relationship("AIModel", foreign_keys=[default_embedding_model_id], lazy="select")
    
    def __repr__(self):
        return f"<SystemAIIntegration(id={self.id}, tenant_id={self.tenant_id}, project_id={self.project_id})>" 