#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
AI 知识库模型
"""

import uuid
from datetime import datetime
from sqlalchemy import Column, String, Integer, Float, Boolean, ForeignKey, DateTime, Text, func, Index
from sqlalchemy.dialects.postgresql import UUID, JSONB, ARRAY
from sqlalchemy.orm import relationship
from pgvector.sqlalchemy import Vector

from db.database import Base

class AIKnowledgeBase(Base):
    """AI 知识库模型"""
    __tablename__ = "ai_knowledge_bases"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    tenant_id = Column(UUID(as_uuid=True), ForeignKey("tenants.id", ondelete="CASCADE"), nullable=False, comment="租户 ID")
    project_id = Column(UUID(as_uuid=True), ForeignKey("projects.id", ondelete="CASCADE"), nullable=True, comment="项目 ID，为空表示租户级知识库")
    name = Column(String(100), nullable=False, comment="知识库名称")
    description = Column(Text, nullable=True, comment="描述")
    industry_type = Column(String(50), nullable=True, comment="行业类型：retail, restaurant 等")
    embedding_model_id = Column(UUID(as_uuid=True), ForeignKey("ai_models.id", ondelete="SET NULL"), nullable=True, comment="嵌入模型 ID")
    status = Column(String(20), nullable=False, default="active", comment="状态：active, building, inactive")
    document_count = Column(Integer, nullable=False, default=0, comment="文档数量")
    chunk_count = Column(Integer, nullable=False, default=0, comment="文本块数量")
    config = Column(JSONB, nullable=True, comment="配置参数")
    created_at = Column(DateTime(timezone=True), server_default=func.now(), comment="创建时间")
    updated_at = Column(DateTime(timezone=True), onupdate=func.now(), comment="更新时间")

    # 关系
    tenant = relationship("Tenant", back_populates="ai_knowledge_bases")
    project = relationship("Project", back_populates="ai_knowledge_bases")
    embedding_model = relationship("AIModel", foreign_keys=[embedding_model_id])
    documents = relationship("AIKnowledgeDocument", back_populates="knowledge_base", cascade="all, delete-orphan")
    chunks = relationship("AIKnowledgeChunk", back_populates="knowledge_base", cascade="all, delete-orphan")

    def __repr__(self):
        return f"<AIKnowledgeBase(id={self.id}, name='{self.name}')>"

class AIKnowledgeDocument(Base):
    """AI 知识文档模型"""
    __tablename__ = "ai_knowledge_documents"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    knowledge_base_id = Column(UUID(as_uuid=True), ForeignKey("ai_knowledge_bases.id", ondelete="CASCADE"), nullable=False, comment="知识库 ID")
    title = Column(String(255), nullable=False, comment="文档标题")
    content = Column(Text, nullable=True, comment="文档内容")
    file_path = Column(String(255), nullable=True, comment="文件路径")
    file_type = Column(String(50), nullable=True, comment="文件类型：pdf, docx, txt 等")
    file_size = Column(Integer, nullable=True, comment="文件大小（字节）")
    source_url = Column(String(255), nullable=True, comment="来源 URL")
    chunk_count = Column(Integer, nullable=False, default=0, comment="文本块数量")
    embedding_status = Column(String(20), nullable=False, default="pending", comment="嵌入状态：pending, processing, completed, failed")
    error_message = Column(Text, nullable=True, comment="错误信息")
    request_metadata = Column(JSONB, nullable=True, comment="请求元数据")
    created_at = Column(DateTime(timezone=True), server_default=func.now(), comment="创建时间")
    updated_at = Column(DateTime(timezone=True), onupdate=func.now(), comment="更新时间")

    # 关系
    knowledge_base = relationship("AIKnowledgeBase", back_populates="documents")
    chunks = relationship("AIKnowledgeChunk", back_populates="document", cascade="all, delete-orphan")

    def __repr__(self):
        return f"<AIKnowledgeDocument(id={self.id}, title='{self.title}')>"

class AIKnowledgeChunk(Base):
    """AI 知识文本块模型"""
    __tablename__ = "ai_knowledge_chunks"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    knowledge_base_id = Column(UUID(as_uuid=True), ForeignKey("ai_knowledge_bases.id", ondelete="CASCADE"), nullable=False, comment="知识库 ID")
    document_id = Column(UUID(as_uuid=True), ForeignKey("ai_knowledge_documents.id", ondelete="CASCADE"), nullable=False, comment="文档 ID")
    content = Column(Text, nullable=False, comment="文本块内容")
    embedding = Column(Vector(1536), nullable=True, comment="嵌入向量")
    chunk_metadata = Column(JSONB, nullable=True, comment="文本块元数据")
    page_number = Column(Integer, nullable=True, comment="页码")
    chunk_index = Column(Integer, nullable=False, comment="文本块索引")
    token_count = Column(Integer, nullable=True, comment="Token 数量")
    created_at = Column(DateTime(timezone=True), server_default=func.now(), comment="创建时间")

    # 关系
    knowledge_base = relationship("AIKnowledgeBase", back_populates="chunks")
    document = relationship("AIKnowledgeDocument", back_populates="chunks")

    # 索引
    __table_args__ = (
        Index('idx_knowledge_chunks_embedding', embedding, postgresql_using='ivfflat', postgresql_with={'lists': 100}, postgresql_ops={'embedding': 'vector_l2_ops'}),
    )

    def __repr__(self):
        return f"<AIKnowledgeChunk(id={self.id}, document_id={self.document_id}, chunk_index={self.chunk_index})>"
