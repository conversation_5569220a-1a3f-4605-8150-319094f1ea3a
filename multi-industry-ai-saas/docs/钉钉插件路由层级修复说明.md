# 钉钉插件路由层级修复说明

## 问题分析

用户反馈钉钉插件路由使用错误，指出：
- 插件的安装/卸载确实应该在租户级别（插件市场）
- 但插件的配置和交互应该属于项目级别
- 前端API调用路径使用了错误的层级

## 路由层级设计原则

### 1. 租户级别 (Tenant Level)
**用途**：插件市场相关功能
- 插件安装/卸载
- 插件购买/订阅
- 插件订单管理

**路径格式**：`/api/v1/tenant/plugins/{plugin_name}/*`

**示例**：
```
/api/v1/tenant/plugins/dingtalk/install    # 安装插件
/api/v1/tenant/plugins/dingtalk/uninstall  # 卸载插件
/api/v1/tenant/plugin-market               # 插件市场
```

### 2. 项目级别 (Project Level)
**用途**：插件的具体配置和业务交互
- 插件设置配置
- 业务功能操作
- 数据查询和管理

**路径格式**：`/api/project/{project_id}/plugin/{plugin_name}/*`

**示例**：
```
/api/project/{project_id}/plugin/dingtalk/settings    # 插件设置
/api/project/{project_id}/plugin/dingtalk/webhooks    # Webhook管理
/api/project/{project_id}/plugin/dingtalk/logs        # 通知日志
/api/project/{project_id}/plugin/dingtalk/ai/chat     # AI聊天
```

## 当前问题

### 错误的路由注册
钉钉插件目前只注册在租户级别：
```python
# backend/api/v1/tenant/__init__.py
router.include_router(dingtalk_plugin_router, prefix="/plugins/dingtalk", tags=["tenant_dingtalk_plugin"])
```

### 错误的前端API调用
前端使用了租户级别的API路径：
```javascript
// 错误的路径
const response = await api.get(`/tenant/plugins/dingtalk/logs`);
```

## 修复方案

### 1. 后端路由修复

#### 确认项目级别路由注册
项目级别的插件路由已经正确注册：
```python
# backend/api/v1/project/__init__.py (第92行)
router.include_router(plugin_router, prefix="/{project_id}/plugin")
```

#### 修复依赖函数
将钉钉插件的依赖函数改为使用项目级别的标准依赖：

**修复前**：
```python
from core.auth import get_current_user as auth_get_current_user
from api.deps import get_current_project as deps_get_current_project, get_current_tenant as deps_get_current_tenant
```

**修复后**：
```python
from core.auth import get_current_user
from api.deps import get_current_project, get_current_tenant
```

### 2. 前端API路径修复

#### 修复所有API调用路径
将所有钉钉插件的API调用从租户级别改为项目级别：

**修复前**：
```javascript
// 租户级别路径（错误）
const response = await api.get(`/tenant/plugins/dingtalk/settings`);
const response = await api.get(`/tenant/plugins/dingtalk/logs`);
const response = await api.get(`/tenant/plugins/dingtalk/auth/user-info`);
```

**修复后**：
```javascript
// 项目级别路径（正确）
const projectId = getProjectId();
const response = await api.get(`/project/${projectId}/plugin/dingtalk/settings`);
const response = await api.get(`/project/${projectId}/plugin/dingtalk/logs`);
const response = await api.get(`/project/${projectId}/plugin/dingtalk/auth/user-info`);
```

## 修复的文件

### 后端文件
1. **`backend/plugins/dingtalk/api/router.py`**
   - 修复依赖函数导入
   - 使用标准的项目级别依赖

2. **`backend/plugins/dingtalk/api/auth.py`**
   - 已经使用正确的依赖函数

3. **`backend/plugins/dingtalk/api/ai_chat.py`**
   - 已经使用正确的依赖函数

### 前端文件
1. **`frontend/src/pages/project/plugins/dingtalk/service/dingtalkService.js`**
   - 修复所有API调用路径
   - 从租户级别改为项目级别

## 路由对应关系

### 正确的API路径映射

| 功能 | 前端调用 | 后端路由 |
|------|----------|----------|
| 插件设置 | `/project/{projectId}/plugin/dingtalk/settings` | `@router.get("/settings")` |
| Webhook管理 | `/project/{projectId}/plugin/dingtalk/webhooks` | `@router.get("/webhooks")` |
| 通知日志 | `/project/{projectId}/plugin/dingtalk/logs` | `@router.get("/logs")` |
| 用户信息 | `/project/{projectId}/plugin/dingtalk/auth/user-info` | `@router.get("/auth/user-info")` |
| AI聊天 | `/project/{projectId}/plugin/dingtalk/ai/chat` | `@router.post("/ai/chat")` |
| 测试Webhook | `/project/{projectId}/plugin/dingtalk/test` | `@router.post("/test")` |
| 同步数据 | `/project/{projectId}/plugin/dingtalk/sync` | `@router.post("/sync")` |

### 依赖函数参数获取

项目级别路由的依赖函数会自动从路径参数中获取：
- `project_id`: 从路径参数 `/{project_id}/plugin/dingtalk/*` 中获取
- `current_user`: 从认证token中获取
- `current_tenant`: 从用户信息中获取租户ID

## 验证方法

### 1. 后端验证
```bash
# 检查路由注册
python -c "
from main import app
for route in app.routes:
    if hasattr(route, 'path') and 'dingtalk' in route.path:
        print(f'{route.methods} {route.path}')
"
```

### 2. 前端验证
1. 打开浏览器开发者工具
2. 进入钉钉插件页面
3. 检查网络请求，确认API路径格式为：
   ```
   /api/project/{project_id}/plugin/dingtalk/*
   ```

### 3. 功能验证
1. 测试插件设置保存
2. 测试Webhook管理
3. 测试通知日志查看
4. 测试AI聊天功能
5. 确认不再出现 "Field required" 错误

## 注意事项

1. **路径参数**：项目级别路由会自动从URL路径中提取 `project_id`
2. **权限控制**：项目级别的权限控制更精细，确保用户只能访问自己项目的数据
3. **数据隔离**：项目级别确保了不同项目间的数据隔离
4. **一致性**：所有插件都应该遵循相同的路由层级设计

## 后续优化建议

1. **统一插件路由规范**：制定插件开发的路由规范文档
2. **自动化测试**：为插件路由添加自动化测试用例
3. **路由文档**：完善API文档，明确路由层级设计
4. **开发工具**：提供插件开发脚手架，避免类似问题 