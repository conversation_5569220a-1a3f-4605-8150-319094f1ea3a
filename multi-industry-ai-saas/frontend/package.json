{"name": "retail-ai-saas-frontend", "version": "0.1.0", "private": true, "dependencies": {"@ant-design/charts": "2.2.7", "@ant-design/icons": "^5.3.7", "@ant-design/plots": "^2.3.3", "@ant-design/pro-components": "^2.7.10", "@antv/data-set": "^0.11.8", "@testing-library/jest-dom": "^6.4.6", "@testing-library/react": "^14.3.1", "@testing-library/user-event": "^14.5.2", "ajv": "^8.12.0", "ajv-keywords": "^5.1.0", "antd": "^5.19.0", "axios": "^1.7.2", "dayjs": "^1.11.11", "echarts": "^5.5.1", "echarts-for-react": "^3.0.2", "process": "^0.11.10", "react": "^18.2.0", "react-dom": "^18.2.0", "react-json-view": "^1.21.3", "react-markdown": "^9.0.1", "react-router-dom": "^6.24.1", "react-scripts": "5.0.1", "react-syntax-highlighter": "^15.5.0", "reactflow": "^11.11.4", "web-vitals": "^2.1.4"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "proxy": "http://retail-ai-saas-backend:8000"}