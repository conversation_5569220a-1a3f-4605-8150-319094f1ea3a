import React, { useState, useEffect, useCallback } from 'react';
import {
  Card, Table, Button, Space, Modal, Form, Input, Select, Switch,
  message, Popconfirm, Tag, Tooltip, Badge, Drawer, Typography,
  Row, Col, Statistic, Alert, Tabs, Divider, Collapse,
  Slider, InputNumber, Checkbox
} from 'antd';
import {
  PlusOutlined, EditOutlined, DeleteOutlined, PlayCircleOutlined,
  StopOutlined, RobotOutlined, ShareAltOutlined,
  EyeOutlined, SoundOutlined, CodeOutlined, GlobalOutlined, 
  UploadOutlined, MessageOutlined, BranchesOutlined, ToolOutlined, 
  CopyOutlined, BarChartOutlined, ApiOutlined, DatabaseOutlined, BookOutlined,
  SettingOutlined
} from '@ant-design/icons';
import { useProject } from '../../contexts/ProjectContext';
import { useNavigate } from 'react-router-dom';
import apiService from '../../services/api';

// 导入子组件
import AIToolList from './AIToolList';
import AIKnowledgeBaseList from './AIKnowledgeBaseList';
import SystemIntegrationSettings from './SystemIntegrationSettings';

const { Title, Text, Paragraph } = Typography;
const { TextArea } = Input;
const { TabPane } = Tabs;
const { Option } = Select;

/**
 * AI助手管理页面
 */
const AIAssistantManagement = () => {
  const { currentProject } = useProject();
  const navigate = useNavigate();
  const [assistants, setAssistants] = useState([]);
  const [models, setModels] = useState([]);
  const [configs, setConfigs] = useState([]);
  const [mcpTools, setMcpTools] = useState([]);
  const [mcpServers, setMcpServers] = useState([]);
  const [knowledgeBases, setKnowledgeBases] = useState([]);
  const [roles, setRoles] = useState([]);
  const [loading, setLoading] = useState(false);
  const [modalVisible, setModalVisible] = useState(false);
  const [detailDrawerVisible, setDetailDrawerVisible] = useState(false);
  const [editingAssistant, setEditingAssistant] = useState(null);
  const [selectedAssistant, setSelectedAssistant] = useState(null);
  const [form] = Form.useForm();
  const [activeTab, setActiveTab] = useState('assistants');
  const [usageStats, setUsageStats] = useState({
    total_tokens: 0,
    total_cost: 0,
    current_month_tokens: 0,
    current_month_cost: 0,
    total_conversations: 0,
    active_assistants: 0
  });

  // 获取助手列表
  const fetchAssistants = useCallback(async () => {
    try {
      setLoading(true);
      const response = await apiService.project.ai.aiAssistants.list(currentProject.id);
      // 检查响应格式，兼容不同的返回格式
      if (response.success !== false) {
        setAssistants(response.data || response || []);
      } else {
        console.error('获取AI助手列表失败:', response.message);
        message.error(response.message || '获取助手列表失败');
      }
    } catch (error) {
      console.error('获取AI助手列表失败:', error);
      message.error('获取助手列表失败');
    } finally {
      setLoading(false);
    }
  }, [currentProject.id]);

  // 获取模型列表
  const fetchModels = useCallback(async () => {
    try {
      const response = await apiService.project.ai.aiModels.list(currentProject.id);
      // 检查响应格式，兼容不同的返回格式
      if (response.success !== false) {
        const modelData = response.data || response || [];
        console.log('获取到的模型数据:', modelData);
        setModels(modelData);
      } else {
        console.error('获取模型列表失败:', response.message);
        message.error(response.message || '获取模型列表失败');
      }
    } catch (error) {
      console.error('获取模型列表失败:', error);
      message.error('获取模型列表失败');
    }
  }, [currentProject.id]);

  // 获取配置列表
  const fetchConfigs = useCallback(async () => {
    try {
      const response = await apiService.project.ai.aiConfigs.list(currentProject.id);
      // 检查响应格式，兼容不同的返回格式
      if (response.success !== false) {
        const configData = response.data || response || [];
        console.log('获取到的配置数据:', configData);
        setConfigs(configData);
      } else {
        console.error('获取配置列表失败:', response.message);
        message.error(response.message || '获取配置列表失败');
      }
    } catch (error) {
      console.error('获取配置列表失败:', error);
      message.error('获取配置列表失败');
    }
  }, [currentProject.id]);

  // 获取MCP工具列表
  const fetchMcpTools = useCallback(async () => {
    try {
      const response = await apiService.project.ai.mcpTools.list(currentProject.id);
      setMcpTools(response.data || []);
    } catch (error) {
      console.error('获取MCP工具列表失败:', error);
      message.error('获取MCP工具列表失败');
    }
  }, [currentProject.id]);

  // 获取MCP服务器列表
  const fetchMcpServers = useCallback(async () => {
    try {
      const response = await apiService.project.ai.mcpServers.list(currentProject.id);
      // 检查响应格式，兼容不同的返回格式
      if (response && typeof response === 'object') {
        const servers = response.data || response || [];
        setMcpServers(servers);
      } else {
        console.error('获取MCP服务器列表失败: 响应格式不正确');
        setMcpServers([]);
      }
    } catch (error) {
      console.error('获取MCP服务器列表失败:', error);
      message.error('获取MCP服务器列表失败');
      setMcpServers([]);
    }
  }, [currentProject.id]);

  // 获取知识库列表
  const fetchKnowledgeBases = useCallback(async () => {
    try {
      const response = await apiService.project.knowledgeBase.getAvailableAIKnowledgeBases();
      setKnowledgeBases(response.data || []);
    } catch (error) {
      console.error('获取知识库列表失败:', error);
      message.error('获取知识库列表失败');
    }
  }, [currentProject.id]);

  // 获取角色列表
  const fetchRoles = useCallback(async () => {
    if (!currentProject?.id) {
      console.warn('项目ID未准备好，跳过角色列表获取');
      return;
    }
    
    try {
      const response = await apiService.project.role.getList();
      // 检查响应格式，兼容不同的返回格式
      if (response && typeof response === 'object') {
        if (response.success !== false) {
          const roleData = response.data || response.items || response || [];
          setRoles(Array.isArray(roleData) ? roleData : []);
        } else {
          console.error('获取角色列表失败:', response.message);
          message.error(response.message || '获取角色列表失败');
          setRoles([]);
        }
      } else if (Array.isArray(response)) {
        setRoles(response);
      } else {
        // 如果响应不是对象，设置为空数组
        setRoles([]);
      }
    } catch (error) {
      console.error('获取角色列表失败:', error);
      message.error('获取角色列表失败');
      setRoles([]);
    }
  }, [currentProject?.id]);

  // 获取AI使用统计
  const fetchUsageStats = useCallback(async () => {
    try {
      const response = await apiService.project.ai.aiUsage.getStats(currentProject.id);
      const data = response.data || {};
      
      setUsageStats({
        total_tokens: data.total_tokens || 0,
        total_cost: data.total_cost || 0,
        current_month_tokens: data.current_month_tokens || 0,
        current_month_cost: data.current_month_cost || 0,
        total_conversations: data.total_conversations || data.total_requests || 0,
        active_assistants: data.active_assistants || 0,
        model_stats: data.model_stats || []
      });
    } catch (error) {
      console.error('获取使用统计失败:', error);
      message.error('获取使用统计失败');
    }
  }, [currentProject.id]);

  useEffect(() => {
    if (currentProject?.id) {
      fetchAssistants();
      fetchModels();
      fetchConfigs();
      fetchMcpTools();
      fetchMcpServers();
      fetchKnowledgeBases();
      fetchRoles();
      fetchUsageStats();
    }
  }, [currentProject?.id, fetchAssistants, fetchModels, fetchConfigs, fetchMcpTools, fetchMcpServers, fetchKnowledgeBases, fetchRoles, fetchUsageStats]);

  // 处理模型选择变化
  const handleModelChange = (modelId) => {
    // 清空配置选择，因为配置需要与模型匹配
    form.setFieldsValue({
      config_id: undefined
    });
    
    // 根据选择的模型自动设置能力
    const selectedModel = models.find(m => m.id === modelId);
    if (selectedModel && selectedModel.capabilities) {
      const capabilities = selectedModel.capabilities;
      form.setFieldsValue({
        supports_vision: capabilities.supports_vision || false,
        supports_audio_input: capabilities.supports_audio_input || false,
        supports_audio_output: capabilities.supports_audio_output || false,
        supports_speech_to_text: capabilities.supports_speech_to_text || false,
        supports_text_to_speech: capabilities.supports_text_to_speech || false,
        supports_file_upload: capabilities.supports_file_upload || false,
        supports_web_search: capabilities.supports_web_search || false,
        supports_code_execution: capabilities.supports_code_execution || false,
        supports_thinking: capabilities.supports_thinking || false,
        supported_image_formats: capabilities.supported_image_formats || [],
        supported_audio_formats: capabilities.supported_audio_formats || [],
        max_file_size: capabilities.max_file_size || 10,
        max_audio_duration: capabilities.max_audio_duration || 300
      });
    }
  };

  // 创建/更新助手
  const handleSubmit = async (values) => {
    try {
      // 处理能力配置
      const capabilities = {
        supports_vision: values.supports_vision || false,
        supports_audio_input: values.supports_audio_input || false,
        supports_audio_output: values.supports_audio_output || false,
        supports_speech_to_text: values.supports_speech_to_text || false,
        supports_text_to_speech: values.supports_text_to_speech || false,
        supports_file_upload: values.supports_file_upload || false,
        supports_web_search: values.supports_web_search || false,
        supports_code_execution: values.supports_code_execution || false,
        supports_memory: values.supports_memory || false,
        supports_plugins: values.supports_plugins || false,
        supports_thinking: values.supports_thinking || false,
        supported_image_formats: values.supported_image_formats || [],
        supported_audio_formats: values.supported_audio_formats || [],
        max_file_size: values.max_file_size || 10,
        max_audio_duration: values.max_audio_duration || 300
      };

      const assistantData = {
        ...values,
        capabilities,
        mcp_server_ids: values.mcp_server_ids || [],
        knowledge_bases: values.knowledge_bases || [],
        published_roles: values.published_roles || [],
        // 将is_active转换为status字段
        status: values.is_active !== false ? 'active' : 'inactive'
      };

      // 移除前端特有的字段
      delete assistantData.is_active;

      if (editingAssistant) {
        await apiService.project.ai.aiAssistants.update(currentProject.id, editingAssistant.id, assistantData);
        message.success('更新助手成功');
      } else {
        await apiService.project.ai.aiAssistants.create(currentProject.id, assistantData);
        message.success('创建助手成功');
      }
      
      setModalVisible(false);
      setEditingAssistant(null);
      form.resetFields();
      fetchAssistants();
    } catch (error) {
      console.error('保存助手失败:', error);
      message.error(editingAssistant ? '更新助手失败' : '创建助手失败');
    }
  };

  // 删除助手
  const handleDelete = async (assistantId) => {
    try {
      await apiService.project.ai.aiAssistants.delete(currentProject.id, assistantId);
      message.success('删除助手成功');
      fetchAssistants();
    } catch (error) {
      console.error('删除助手失败:', error);
      message.error('删除助手失败');
    }
  };

  // 切换助手状态
  const handleToggleStatus = async (assistant) => {
    try {
      const newStatus = assistant.status === 'active' ? 'inactive' : 'active';
      await apiService.project.ai.aiAssistants.update(currentProject.id, assistant.id, { status: newStatus });
      message.success(`${newStatus === 'active' ? '启用' : '禁用'}助手成功`);
      fetchAssistants();
    } catch (error) {
      console.error('切换助手状态失败:', error);
      message.error('操作失败');
    }
  };

  // 复制分享链接
  const handleCopyShareLink = (assistant) => {
    const shareUrl = `${window.location.origin}/shared/assistant/${assistant.id}`;
    
    // 检查clipboard API是否可用
    if (navigator.clipboard && navigator.clipboard.writeText) {
      navigator.clipboard.writeText(shareUrl).then(() => {
        message.success('分享链接已复制到剪贴板');
      }).catch(err => {
        console.error('复制失败:', err);
        // 降级处理：显示链接让用户手动复制
        Modal.info({
          title: '分享链接',
          content: (
            <div>
              <p>请手动复制以下链接：</p>
              <Input.TextArea
                value={shareUrl}
                readOnly
                rows={3}
                style={{ marginTop: 8 }}
                onClick={e => e.target.select()}
              />
            </div>
          ),
          width: 500
        });
      });
    } else {
      // 兼容旧版浏览器或HTTPS环境问题
      try {
        // 尝试使用传统方法
        const textArea = document.createElement('textarea');
        textArea.value = shareUrl;
        textArea.style.position = 'fixed';
        textArea.style.opacity = '0';
        document.body.appendChild(textArea);
        textArea.focus();
        textArea.select();
        
        const successful = document.execCommand('copy');
        document.body.removeChild(textArea);
        
        if (successful) {
          message.success('分享链接已复制到剪贴板');
        } else {
          throw new Error('execCommand failed');
        }
      } catch (err) {
        console.error('复制失败:', err);
        // 最终降级：显示模态框让用户手动复制
        Modal.info({
          title: '分享链接',
          content: (
            <div>
              <p>请手动复制以下链接：</p>
              <Input.TextArea
                value={shareUrl}
                readOnly
                rows={3}
                style={{ marginTop: 8 }}
                onClick={e => e.target.select()}
              />
            </div>
          ),
          width: 500
        });
      }
    }
  };

  // 渲染能力标签
  const renderCapabilityTags = (capabilities) => {
    if (!capabilities) return [];
    
    const tags = [];
    if (capabilities.supports_vision) tags.push(<Tag key="vision" color="blue">视觉</Tag>);
    if (capabilities.supports_audio_input) tags.push(<Tag key="audio-in" color="green">语音输入</Tag>);
    if (capabilities.supports_audio_output) tags.push(<Tag key="audio-out" color="green">语音输出</Tag>);
    if (capabilities.supports_file_upload) tags.push(<Tag key="file" color="orange">文件上传</Tag>);
    if (capabilities.supports_web_search) tags.push(<Tag key="web" color="purple">网络搜索</Tag>);
    if (capabilities.supports_code_execution) tags.push(<Tag key="code" color="red">代码执行</Tag>);
    
    return tags.slice(0, 3); // 只显示前3个
  };

  // 表格列定义
  const columns = [
    {
      title: '助手信息',
      dataIndex: 'name',
      key: 'name',
      render: (text, record) => (
        <div>
          <div style={{ fontWeight: 'bold', marginBottom: '4px' }}>{text}</div>
          <div style={{ fontSize: '12px', color: '#666' }}>{record.description}</div>
        </div>
      )
    },
    {
      title: '模型',
      dataIndex: 'model_name',
      key: 'model_name',
      render: (text, record) => (
        <div>
          <div>{text}</div>
          <div style={{ fontSize: '12px', color: '#666' }}>{record.provider_name}</div>
        </div>
      )
    },
    {
      title: '能力',
      dataIndex: 'capabilities',
      key: 'capabilities',
      render: (capabilities) => (
        <div>
          {renderCapabilityTags(capabilities)}
          {renderCapabilityTags(capabilities).length > 0 && (
            <div style={{ marginTop: '4px' }}>
              <Text type="secondary" style={{ fontSize: '12px' }}>
                +{Object.values(capabilities || {}).filter(Boolean).length - renderCapabilityTags(capabilities).length} 更多
              </Text>
            </div>
          )}
        </div>
      )
    },
    {
      title: 'MCP服务器',
      dataIndex: 'mcp_server_ids',
      key: 'mcp_server_ids',
      render: (serverIds) => (
        <div>
          {/* 显示MCP服务器 */}
          {serverIds?.slice(0, 2).map(serverId => {
            const server = mcpServers.find(s => s.id === serverId);
            return server ? (
              <Tag key={serverId} size="small" color="blue">
                {server.name} ({server.available_tools?.length || 0}个工具)
              </Tag>
            ) : null;
          })}
          
          {/* 显示更多提示 */}
          {(serverIds?.length || 0) > 2 && (
            <Tag size="small">+{(serverIds?.length || 0) - 2}</Tag>
          )}
          
          {/* 无服务器提示 */}
          {(!serverIds || serverIds.length === 0) && (
            <span style={{ color: '#999', fontSize: '12px' }}>未配置</span>
          )}
        </div>
      )
    },
    {
      title: '发布角色',
      dataIndex: 'published_roles',
      key: 'published_roles',
      render: (roles) => (
        <div>
          {roles?.slice(0, 2).map(roleCode => {
            const roleNames = {
              'store_manager': '门店经理',
              'finance_manager': '财务经理',
              'warehouse_manager': '仓库经理',
              'purchase_manager': '采购经理',
              'operation_manager': '运营经理',
              'project_admin': '项目管理员',
              'tenant_admin': '租户管理员'
            };
            return <Tag key={roleCode} size="small" color="purple">{roleNames[roleCode] || roleCode}</Tag>;
          })}
          {roles?.length > 2 && (
            <Tag size="small" color="purple">+{roles.length - 2}</Tag>
          )}
          {(!roles || roles.length === 0) && (
            <Text type="secondary">未发布</Text>
          )}
        </div>
      )
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status) => (
        <Badge 
          status={status === 'active' ? 'success' : 'default'} 
          text={status === 'active' ? '活跃' : '非活跃'} 
        />
      )
    },
    {
      title: '操作',
      key: 'action',
      render: (_, record) => (
        <Space size="small">
          <Tooltip title="查看详情">
            <Button
              type="text"
              icon={<EyeOutlined />}
              onClick={() => {
                setSelectedAssistant(record);
                setDetailDrawerVisible(true);
              }}
            />
          </Tooltip>
          <Tooltip title="编辑">
            <Button
              type="text"
              icon={<EditOutlined />}
              onClick={() => {
                setEditingAssistant(record);
                // 设置表单值，确保正确映射字段
                const formValues = {
                  ...record,
                  is_active: record.status === 'active',
                  // 展开capabilities到表单字段
                  ...(record.capabilities || {}),
                  knowledge_bases: record.knowledge_bases || [],
                  published_roles: record.published_roles || [],
                  mcp_server_ids: record.mcp_server_ids || [],
                  // 确保系统提示词字段正确映射
                  system_prompt: record.system_prompt || record.instructions || ''
                };
                console.log('编辑助手 - 原始数据:', record);
                console.log('编辑助手 - 表单值:', formValues);
                console.log('当前模型列表:', models);
                console.log('当前配置列表:', configs);
                form.setFieldsValue(formValues);
                setModalVisible(true);
              }}
            />
          </Tooltip>
          <Tooltip title="聊天">
            <Button
              type="text"
              icon={<MessageOutlined />}
              onClick={() => navigate(`/project/ai/chat/${record.id}`)}
            />
          </Tooltip>
          <Tooltip title="分享">
            <Button
              type="text"
              icon={<ShareAltOutlined />}
              onClick={() => handleCopyShareLink(record)}
            />
          </Tooltip>
          <Tooltip title="角色发布">
            <Button
              type="text"
              icon={<BranchesOutlined />}
              onClick={() => {
                setEditingAssistant(record);
                // 设置表单值，确保正确映射字段
                const formValues = {
                  ...record,
                  is_active: record.status === 'active',
                  // 展开capabilities到表单字段
                  ...(record.capabilities || {}),
                  knowledge_bases: record.knowledge_bases || [],
                  published_roles: record.published_roles || [],
                  // 确保系统提示词字段正确映射
                  system_prompt: record.system_prompt || record.instructions || ''
                };
                form.setFieldsValue(formValues);
                setModalVisible(true);
                // 自动切换到角色发布标签页
                setTimeout(() => {
                  const tabElement = document.querySelector('[data-node-key="roles"]');
                  if (tabElement) {
                    tabElement.click();
                  }
                }, 100);
              }}
            />
          </Tooltip>
          <Tooltip title={record.status === 'active' ? '禁用' : '启用'}>
            <Button
              type="text"
              icon={record.status === 'active' ? <StopOutlined /> : <PlayCircleOutlined />}
              onClick={() => handleToggleStatus(record)}
            />
          </Tooltip>
          <Popconfirm
            title="确定要删除这个助手吗？"
            onConfirm={() => handleDelete(record.id)}
            okText="确定"
            cancelText="取消"
          >
            <Tooltip title="删除">
              <Button
                type="text"
                danger
                icon={<DeleteOutlined />}
              />
            </Tooltip>
          </Popconfirm>
        </Space>
      )
    }
  ];

  return (
    <div className="ant-card ant-card-bordered settings-card css-dev-only-do-not-override-vrrzze" style={{ padding: '24px' }}>
      <div style={{ marginBottom: '24px' }}>
        <Title level={2}>
          <RobotOutlined /> AI助手管理
        </Title>
        <Paragraph>
          创建和管理具有多模态能力的AI助手，支持视觉、语音、文件处理等功能
        </Paragraph>
      </div>

      {/* 统计卡片 */}
      <Row gutter={16} style={{ marginBottom: '24px' }}>
        <Col span={6}>
          <Card>
            <Statistic
              title="总助手数"
              value={assistants.length}
              prefix={<RobotOutlined />}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="活跃助手"
              value={assistants.filter(a => a.status === 'active').length}
              prefix={<PlayCircleOutlined />}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="总对话数"
              value={usageStats.total_conversations || 0}
              prefix={<MessageOutlined />}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="本月费用"
              value={usageStats.current_month_cost || 0}
              prefix="¥"
              precision={2}
            />
          </Card>
        </Col>
      </Row>

      <Card>
        <Tabs
          activeKey={activeTab}
          onChange={setActiveTab}
          items={[
            {
              key: 'assistants',
              label: (
                <span>
                  <RobotOutlined />
                  AI助手
                </span>
              ),
              children: (
                <div>
                  <div style={{ marginBottom: '16px' }}>
                    <Button
                      type="primary"
                      icon={<PlusOutlined />}
                      onClick={() => {
                        setEditingAssistant(null);
                        form.resetFields();
                        setModalVisible(true);
                      }}
                    >
                      创建助手
                    </Button>
                  </div>
                  
                  <Table
                    columns={columns}
                    dataSource={assistants}
                    rowKey="id"
                    loading={loading}
                    pagination={{
                      showSizeChanger: true,
                      showQuickJumper: true,
                      showTotal: (total) => `共 ${total} 个助手`
                    }}
                  />
                </div>
              )
            },
            {
              key: 'models',
              label: (
                <span>
                  <ApiOutlined />
                  AI模型
                </span>
              ),
              children: (
                <div>
                  <Alert
                    message="AI模型管理"
                    description="管理可用的AI模型，包括OpenAI、Claude、本地模型等。配置模型参数和能力。"
                    type="info"
                    showIcon
                    style={{ marginBottom: '16px' }}
                  />
                  <Button
                    type="primary"
                    icon={<PlusOutlined />}
                    onClick={() => navigate('/project/ai/models')}
                    style={{ marginBottom: '16px' }}
                  >
                    管理AI模型
                  </Button>
                  
                  <Table
                    columns={[
                      {
                        title: '模型名称',
                        dataIndex: 'name',
                        key: 'name',
                        render: (text, record) => (
                          <div>
                            <div style={{ fontWeight: 'bold' }}>{text}</div>
                            <div style={{ fontSize: '12px', color: '#666' }}>{record.provider_name}</div>
                          </div>
                        )
                      },
                      {
                        title: '模型类型',
                        dataIndex: 'model_type',
                        key: 'model_type',
                        render: (type) => {
                          const typeMap = {
                            'chat': { color: 'blue', text: '对话' },
                            'completion': { color: 'green', text: '补全' },
                            'embedding': { color: 'orange', text: '嵌入' },
                            'image': { color: 'purple', text: '图像' },
                            'audio': { color: 'cyan', text: '音频' }
                          };
                          const config = typeMap[type] || { color: 'default', text: type };
                          return <Tag color={config.color}>{config.text}</Tag>;
                        }
                      },
                      {
                        title: '能力',
                        dataIndex: 'capabilities',
                        key: 'capabilities',
                        render: (capabilities) => renderCapabilityTags(capabilities)
                      },
                      {
                        title: '状态',
                        dataIndex: 'is_enabled',
                        key: 'is_enabled',
                        render: (enabled) => (
                          <Tag color={enabled ? 'green' : 'default'}>
                            {enabled ? '启用' : '禁用'}
                          </Tag>
                        )
                      }
                    ]}
                    dataSource={models}
                    rowKey="id"
                    loading={loading}
                    pagination={{
                      showSizeChanger: true,
                      showQuickJumper: true,
                      showTotal: (total) => `共 ${total} 个模型`
                    }}
                  />
                </div>
              )
            },
            {
              key: 'mcp-tools',
              label: (
                <span>
                  <ToolOutlined />
                  MCP服务器
                </span>
              ),
              children: (
                <div>
                  <Alert
                    message="MCP服务器管理"
                    description="管理Model Context Protocol (MCP) 服务器，支持sse、stdio、streamhttp等协议类型。添加服务器后可选择其提供的工具。"
                    type="info"
                    showIcon
                    style={{ marginBottom: '16px' }}
                  />
                  <Button
                    type="primary"
                    icon={<PlusOutlined />}
                    onClick={() => {
                      navigate('/project/ai/mcp-servers');
                    }}
                    style={{ marginBottom: '16px' }}
                  >
                    管理MCP服务器
                  </Button>
                  
                  <Table
                    columns={[
                      {
                        title: '服务器名称',
                        dataIndex: 'name',
                        key: 'name',
                        render: (text, record) => (
                          <div>
                            <div style={{ fontWeight: 'bold' }}>{text}</div>
                            <div style={{ fontSize: '12px', color: '#666' }}>{record.description}</div>
                          </div>
                        )
                      },
                      {
                        title: '协议类型',
                        dataIndex: 'transport_types',
                        key: 'transport_types',
                        render: (types) => {
                          if (!types || !Array.isArray(types)) return '-';
                          const typeMap = {
                            'stdio': { color: 'blue', text: 'STDIO' },
                            'streamhttp': { color: 'green', text: 'StreamHTTP' },
                            'sse': { color: 'orange', text: 'SSE' },
                            'websocket': { color: 'purple', text: 'WebSocket' }
                          };
                          return types.map(type => {
                            const config = typeMap[type] || { color: 'default', text: type };
                            return <Tag key={type} color={config.color}>{config.text}</Tag>;
                          });
                        }
                      },
                      {
                        title: '连接状态',
                        dataIndex: 'connection_status',
                        key: 'connection_status',
                        render: (status) => (
                          <Tag color={status === 'connected' ? 'green' : status === 'connecting' ? 'orange' : 'red'}>
                            {status === 'connected' ? '已连接' : 
                             status === 'connecting' ? '连接中' : 
                             status === 'disconnected' ? '已断开' : '未知'}
                          </Tag>
                        )
                      },
                      {
                        title: '可用工具',
                        dataIndex: 'available_tools',
                        key: 'available_tools',
                        render: (tools) => (
                          <span>{Array.isArray(tools) ? tools.length : 0} 个工具</span>
                        )
                      },
                      {
                        title: '状态',
                        dataIndex: 'enabled',
                        key: 'enabled',
                        render: (enabled) => (
                          <Tag color={enabled ? 'green' : 'default'}>
                            {enabled ? '启用' : '禁用'}
                          </Tag>
                        )
                      }
                    ]}
                    dataSource={mcpServers}
                    rowKey="id"
                    loading={loading}
                    pagination={{
                      showSizeChanger: true,
                      showQuickJumper: true,
                      showTotal: (total) => `共 ${total} 个服务器`
                    }}
                  />
                </div>
              )
            },
            {
              key: 'tools',
              label: (
                <span>
                  <DatabaseOutlined />
                  AI工具
                </span>
              ),
              children: (
                <div style={{ padding: '0' }}>
                  <AIToolList />
                </div>
              )
            },
            {
              key: 'knowledge',
              label: (
                <span>
                  <BookOutlined />
                  AI知识库
                </span>
              ),
              children: (
                <div style={{ padding: '0' }}>
                  <AIKnowledgeBaseList />
                </div>
              )
            },
            {
              key: 'system-integration',
              label: (
                <span>
                  <SettingOutlined />
                  系统整合
                </span>
              ),
              children: (
                <div style={{ padding: '0' }}>
                  <SystemIntegrationSettings />
                </div>
              )
            },
            {
              key: 'role-assistants',
              label: (
                <span>
                  <BranchesOutlined />
                  角色助手
                </span>
              ),
              children: (
                <div>
                  <Alert
                    message="角色专用AI助手"
                    description="查看为不同角色发布的AI助手，用户可根据自己的角色访问相应的专用助手。"
                    type="info"
                    showIcon
                    style={{ marginBottom: '24px' }}
                  />
                  
                  <Row gutter={16}>
                    {[
                      { code: 'store_manager', name: '门店经理', icon: '🏪' },
                      { code: 'finance_manager', name: '财务经理', icon: '💰' },
                      { code: 'warehouse_manager', name: '仓库经理', icon: '📦' },
                      { code: 'purchase_manager', name: '采购经理', icon: '🛒' },
                      { code: 'operation_manager', name: '运营经理', icon: '📊' }
                    ].map(role => {
                      const roleAssistants = assistants.filter(assistant => 
                        assistant.published_roles && assistant.published_roles.includes(role.code)
                      );
                      
                      return (
                        <Col span={8} key={role.code} style={{ marginBottom: '16px' }}>
                          <Card
                            hoverable
                            onClick={() => navigate(`/project/ai/role-assistants?role=${role.code}`)}
                            style={{ textAlign: 'center' }}
                          >
                            <div style={{ fontSize: '32px', marginBottom: '8px' }}>{role.icon}</div>
                            <div style={{ fontWeight: 'bold', marginBottom: '4px' }}>{role.name}</div>
                            <div style={{ color: '#666', fontSize: '12px' }}>
                              {roleAssistants.length} 个专用助手
                            </div>
                            <Button 
                              type="primary" 
                              size="small" 
                              style={{ marginTop: '8px' }}
                              onClick={(e) => {
                                e.stopPropagation();
                                navigate(`/project/ai/role-assistants?role=${role.code}`);
                              }}
                            >
                              查看助手
                            </Button>
                          </Card>
                        </Col>
                      );
                    })}
                  </Row>
                </div>
              )
            },
            {
              key: 'statistics',
              label: (
                <span>
                  <BarChartOutlined />
                  使用统计
                </span>
              ),
              children: (
                <div>
                  <Row gutter={16} style={{ marginBottom: '24px' }}>
                    <Col span={6}>
                      <Card>
                        <Statistic
                          title="总Token使用量"
                          value={usageStats.total_tokens || 0}
                          suffix="tokens"
                        />
                      </Card>
                    </Col>
                    <Col span={6}>
                      <Card>
                        <Statistic
                          title="总费用"
                          value={usageStats.total_cost || 0}
                          prefix="¥"
                          precision={2}
                        />
                      </Card>
                    </Col>
                    <Col span={6}>
                      <Card>
                        <Statistic
                          title="本月Token使用量"
                          value={usageStats.current_month_tokens || 0}
                          suffix="tokens"
                        />
                      </Card>
                    </Col>
                    <Col span={6}>
                      <Card>
                        <Statistic
                          title="本月费用"
                          value={usageStats.current_month_cost || 0}
                          prefix="¥"
                          precision={2}
                        />
                      </Card>
                    </Col>
                  </Row>
                  
                  {/* 模型使用统计详情 */}
                  {usageStats.model_stats && usageStats.model_stats.length > 0 && (
                    <Card title="模型使用明细" style={{ marginBottom: 16 }}>
                      <Table
                        size="small"
                        dataSource={usageStats.model_stats}
                        rowKey="model_id"
                        pagination={false}
                        columns={[
                          {
                            title: '模型名称',
                            dataIndex: 'model_name',
                            key: 'model_name',
                            render: (text, record) => (
                              <div>
                                <div style={{ fontWeight: 'bold' }}>{text}</div>
                                <div style={{ fontSize: '12px', color: '#666' }}>{record.provider_name}</div>
                              </div>
                            )
                          },
                          {
                            title: '调用次数',
                            dataIndex: 'request_count',
                            key: 'request_count',
                            render: (count) => count || 0
                          },
                          {
                            title: 'Token使用量',
                            key: 'tokens',
                            render: (_, record) => (
                              <div>
                                <div>输入: {(record.input_tokens || 0).toLocaleString()}</div>
                                <div>输出: {(record.output_tokens || 0).toLocaleString()}</div>
                                <div style={{ fontWeight: 'bold' }}>总计: {((record.input_tokens || 0) + (record.output_tokens || 0)).toLocaleString()}</div>
                              </div>
                            )
                          },
                          {
                            title: '费用统计',
                            key: 'cost',
                            render: (_, record) => (
                              <div>
                                <div>输入: ¥{(record.input_cost || 0).toFixed(4)}</div>
                                <div>输出: ¥{(record.output_cost || 0).toFixed(4)}</div>
                                <div style={{ fontWeight: 'bold' }}>总计: ¥{((record.input_cost || 0) + (record.output_cost || 0)).toFixed(4)}</div>
                              </div>
                            )
                          }
                        ]}
                      />
                    </Card>
                  )}
                </div>
              )
            }
          ]}
        />
      </Card>

      {/* 创建/编辑助手模态框 */}
      <Modal
        title={editingAssistant ? '编辑助手' : '创建助手'}
        open={modalVisible}
        onCancel={() => {
          setModalVisible(false);
          setEditingAssistant(null);
          form.resetFields();
        }}
        onOk={() => form.submit()}
        width={800}
        destroyOnHidden
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSubmit}
        >
          <Tabs defaultActiveKey="basic">
            <TabPane tab="基本信息" key="basic">
              <Form.Item
                name="name"
                label="助手名称"
                rules={[{ required: true, message: '请输入助手名称' }]}
              >
                <Input placeholder="请输入助手名称" />
              </Form.Item>

              <Form.Item
                name="description"
                label="助手描述"
              >
                <TextArea rows={3} placeholder="请输入助手描述" />
              </Form.Item>

              <Form.Item
                name="model_id"
                label="选择模型"
                rules={[{ required: true, message: '请选择模型' }]}
              >
                <Select
                  placeholder="请选择模型"
                  onChange={handleModelChange}
                  showSearch
                  filterOption={(input, option) =>
                    option.children.toLowerCase().indexOf(input.toLowerCase()) >= 0
                  }
                >
                  {models.filter(model => model.status === 'active' || model.is_enabled !== false).map(model => (
                    <Option key={model.id} value={model.id}>
                      {model.display_name || model.name} ({model.provider_display_name || model.provider_name || '未知提供商'})
                    </Option>
                  ))}
                </Select>
              </Form.Item>

              <Form.Item
                name="config_id"
                label="选择配置"
              >
                <Select
                  placeholder="请选择配置（可选）"
                  allowClear
                  showSearch
                  filterOption={(input, option) =>
                    option.children.toLowerCase().indexOf(input.toLowerCase()) >= 0
                  }
                >
                  {configs.filter(config => config.status === 'active' || config.is_enabled !== false).map(config => (
                    <Option key={config.id} value={config.id}>
                      {config.name} ({config.description || '无描述'})
                    </Option>
                  ))}
                </Select>
              </Form.Item>

              <Form.Item
                name="system_prompt"
                label="系统提示词"
              >
                <TextArea rows={4} placeholder="请输入系统提示词" />
              </Form.Item>

              <Form.Item
                name="is_active"
                label="启用状态"
                valuePropName="checked"
                initialValue={true}
              >
                <Switch />
              </Form.Item>
            </TabPane>

            <TabPane tab="能力配置" key="capabilities">
              <Row gutter={16}>
                <Col span={12}>
                  <Form.Item
                    name="supports_vision"
                    label="视觉能力"
                    valuePropName="checked"
                  >
                    <Switch />
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item
                    name="supports_audio_input"
                    label="语音输入"
                    valuePropName="checked"
                  >
                    <Switch />
                  </Form.Item>
                </Col>
              </Row>

              <Row gutter={16}>
                <Col span={12}>
                  <Form.Item
                    name="supports_audio_output"
                    label="语音输出"
                    valuePropName="checked"
                  >
                    <Switch />
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item
                    name="supports_file_upload"
                    label="文件上传"
                    valuePropName="checked"
                  >
                    <Switch />
                  </Form.Item>
                </Col>
              </Row>

              <Row gutter={16}>
                <Col span={12}>
                  <Form.Item
                    name="supports_web_search"
                    label="网络搜索"
                    valuePropName="checked"
                  >
                    <Switch />
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item
                    name="supports_code_execution"
                    label="代码执行"
                    valuePropName="checked"
                  >
                    <Switch />
                  </Form.Item>
                </Col>
              </Row>

              <Row gutter={16}>
                <Col span={12}>
                  <Form.Item
                    name="supports_thinking"
                    label="思维链"
                    valuePropName="checked"
                  >
                    <Switch />
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item
                    name="supports_memory"
                    label="记忆功能"
                    valuePropName="checked"
                  >
                    <Switch />
                  </Form.Item>
                </Col>
              </Row>

              <Divider />

              <Form.Item
                name="max_file_size"
                label="最大文件大小 (MB)"
              >
                <InputNumber min={1} max={100} />
              </Form.Item>

              <Form.Item
                name="max_audio_duration"
                label="最大音频时长 (秒)"
              >
                <InputNumber min={1} max={3600} />
              </Form.Item>
            </TabPane>

            <TabPane tab="MCP服务器" key="tools">
              <Form.Item
                name="mcp_server_ids"
                label="选择MCP服务器"
                extra="选择要关联的MCP服务器，助手将可以调用这些服务器提供的所有工具"
              >
                <Checkbox.Group style={{ width: '100%' }}>
                  <Row gutter={16}>
                    {mcpServers.filter(server => server.enabled).map(server => (
                      <Col span={12} key={server.id} style={{ marginBottom: '16px' }}>
                        <Checkbox value={server.id}>
                          <div style={{ 
                            border: '1px solid #d9d9d9', 
                            borderRadius: '8px', 
                            padding: '12px',
                            background: server.status === 'connected' ? '#f6ffed' : '#fff2f0'
                          }}>
                            <div style={{ fontWeight: 'bold', marginBottom: '4px' }}>
                              <Tag 
                                size="small" 
                                color={server.status === 'connected' ? 'success' : 'error'}
                              >
                                {server.status === 'connected' ? '已连接' : '未连接'}
                              </Tag>
                              {server.name}
                            </div>
                            <div style={{ fontSize: '12px', color: '#666', marginBottom: '8px' }}>
                              {server.description || '暂无描述'}
                            </div>
                            <div style={{ fontSize: '11px', color: '#999' }}>
                              <div>协议: {server.transport_types?.join(', ') || '未知'}</div>
                              <div>工具数量: {server.available_tools?.length || 0}个</div>
                              {server.available_tools?.length > 0 && (
                                <div style={{ marginTop: '4px' }}>
                                  可用工具: {server.available_tools.slice(0, 3).map(tool => tool.name).join(', ')}
                                  {server.available_tools.length > 3 && ` 等${server.available_tools.length}个工具`}
                                </div>
                              )}
                            </div>
                          </div>
                        </Checkbox>
                      </Col>
                    ))}
                  </Row>
                </Checkbox.Group>
              </Form.Item>

              {mcpServers.filter(server => server.enabled).length === 0 && (
                <Alert
                  message="暂无可用的MCP服务器"
                  description="请先在MCP服务器管理页面添加并启用MCP服务器。"
                  type="warning"
                  showIcon
                  style={{ marginTop: '16px' }}
                  action={
                    <Button 
                      type="link" 
                      size="small"
                      onClick={() => window.open('/project/ai/mcp-servers', '_blank')}
                    >
                      前往管理
                    </Button>
                  }
                />
              )}

              <Divider />

              <Alert
                message="MCP服务器说明"
                description="MCP (Model Context Protocol) 服务器提供各种工具来扩展AI助手的能力。选择服务器后，助手可以在对话中调用该服务器的所有可用工具。建议选择状态为已连接的服务器以确保工具正常使用。"
                type="info"
                showIcon
              />
            </TabPane>

            <TabPane tab="知识库" key="knowledge">
              <Form.Item
                name="knowledge_bases"
                label="选择知识库"
              >
                <Checkbox.Group style={{ width: '100%' }}>
                  <Row gutter={16}>
                    {knowledgeBases.filter(kb => kb.is_enabled).map(kb => (
                      <Col span={8} key={kb.id} style={{ marginBottom: '8px' }}>
                        <Checkbox value={kb.id}>
                          <div>
                            <div style={{ fontWeight: 'bold' }}>{kb.name}</div>
                            <div style={{ fontSize: '12px', color: '#666' }}>
                              {kb.description}
                            </div>
                          </div>
                        </Checkbox>
                      </Col>
                    ))}
                  </Row>
                </Checkbox.Group>
              </Form.Item>

              <Divider />

              <Alert
                message="知识库说明"
                description="选择相关的知识库可以让AI助手获得特定领域的专业知识，提高回答的准确性和专业性。"
                type="info"
                showIcon
              />
            </TabPane>

            <TabPane tab="角色发布" key="roles">
              <Form.Item
                name="published_roles"
                label="发布到角色"
              >
                <Checkbox.Group style={{ width: '100%' }}>
                  <Row gutter={16}>
                    {[
                      { code: 'store_manager', name: '门店经理', description: '负责门店日常运营管理' },
                      { code: 'finance_manager', name: '财务经理', description: '负责财务管理和分析' },
                      { code: 'warehouse_manager', name: '仓库经理', description: '负责仓储和库存管理' },
                      { code: 'purchase_manager', name: '采购经理', description: '负责采购和供应商管理' },
                      { code: 'operation_manager', name: '运营经理', description: '负责运营策略和执行' },
                      { code: 'project_admin', name: '项目管理员', description: '项目全局管理权限' },
                      { code: 'tenant_admin', name: '租户管理员', description: '租户级别管理权限' }
                    ].map(role => (
                      <Col span={8} key={role.code} style={{ marginBottom: '8px' }}>
                        <Checkbox value={role.code}>
                          <div>
                            <div style={{ fontWeight: 'bold' }}>{role.name}</div>
                            <div style={{ fontSize: '12px', color: '#666' }}>
                              {role.description}
                            </div>
                          </div>
                        </Checkbox>
                      </Col>
                    ))}
                  </Row>
                </Checkbox.Group>
              </Form.Item>

              <Divider />

              <Alert
                message="角色发布说明"
                description="选择要发布的角色后，对应角色的用户可以在其专用AI助手页面中看到并使用此助手。未选择任何角色则只有管理员可见。"
                type="info"
                showIcon
              />
            </TabPane>
          </Tabs>
        </Form>
      </Modal>

      {/* 助手详情抽屉 */}
      <Drawer
        title="助手详情"
        placement="right"
        onClose={() => setDetailDrawerVisible(false)}
        open={detailDrawerVisible}
        width={600}
      >
        {selectedAssistant && (
          <div>
            <Tabs defaultActiveKey="basic">
              <TabPane tab="基本信息" key="basic">
                <div style={{ marginBottom: 16 }}>
                  <Text strong>助手名称:</Text> {selectedAssistant.name}
                </div>
                <div style={{ marginBottom: 16 }}>
                  <Text strong>描述:</Text> {selectedAssistant.description}
                </div>
                <div style={{ marginBottom: 16 }}>
                  <Text strong>模型:</Text> {selectedAssistant.model_name} ({selectedAssistant.provider_name})
                </div>
                <div style={{ marginBottom: 16 }}>
                  <Text strong>状态:</Text> 
                  <Badge 
                    status={selectedAssistant.status === 'active' ? 'success' : 'default'} 
                    text={selectedAssistant.status === 'active' ? '活跃' : '非活跃'} 
                    style={{ marginLeft: 8 }}
                  />
                </div>
                <div style={{ marginBottom: 16 }}>
                  <Text strong>系统提示词:</Text>
                  <div style={{ marginTop: 4, padding: 8, background: '#f5f5f5', borderRadius: 4 }}>
                    {selectedAssistant.system_prompt || '未设置'}
                  </div>
                </div>
              </TabPane>
              
              <TabPane tab="能力配置" key="capabilities">
                <div style={{ marginBottom: 16 }}>
                  <Text strong>多模态能力:</Text>
                  <div style={{ marginTop: 8 }}>
                    {renderCapabilityTags(selectedAssistant.capabilities)}
                  </div>
                </div>
                
                <div style={{ marginBottom: 16 }}>
                  <Text strong>详细配置:</Text>
                  <pre style={{ 
                    background: '#f5f5f5', 
                    padding: '16px', 
                    borderRadius: '4px',
                    fontSize: '12px',
                    marginTop: 8
                  }}>
                    {JSON.stringify(selectedAssistant.capabilities, null, 2)}
                  </pre>
                </div>
              </TabPane>
              
              <TabPane tab="MCP服务器" key="tools">
                <div>
                  {/* 显示MCP服务器 */}
                  {selectedAssistant.mcp_server_ids?.map(serverId => {
                    const server = mcpServers.find(s => s.id === serverId);
                    return server ? (
                      <Card key={serverId} size="small" style={{ marginBottom: 8 }}>
                        <div style={{ fontWeight: 'bold' }}>
                          <Tag size="small" color="blue">MCP服务器</Tag>
                          {server.name}
                        </div>
                        <div style={{ fontSize: '12px', color: '#666', marginBottom: 4 }}>
                          {server.description || '暂无描述'}
                        </div>
                        <div style={{ fontSize: '12px', color: '#999', marginBottom: 4 }}>
                          状态: <Badge status={server.status === 'connected' ? 'success' : 'default'} text={server.status === 'connected' ? '已连接' : '未连接'} />
                          | 协议: {server.transport_types?.join(', ') || '未知'}
                          | 工具数量: {server.available_tools?.length || 0}个
                        </div>
                        {server.available_tools?.length > 0 && (
                          <div>
                            <div style={{ fontSize: '11px', color: '#999', marginBottom: 4 }}>可用工具:</div>
                            {server.available_tools.slice(0, 5).map((tool, index) => (
                              <Tag key={index} size="small" style={{ marginBottom: 2 }}>
                                {tool.name}
                              </Tag>
                            ))}
                            {server.available_tools.length > 5 && (
                              <Tag size="small" style={{ marginBottom: 2 }}>
                                +{server.available_tools.length - 5}个工具
                              </Tag>
                            )}
                          </div>
                        )}
                      </Card>
                    ) : null;
                  })}
                  
                  {/* 无服务器提示 */}
                  {(!selectedAssistant.mcp_server_ids || selectedAssistant.mcp_server_ids.length === 0) && (
                    <Text type="secondary">未配置MCP服务器</Text>
                  )}
                </div>
              </TabPane>
              
              <TabPane tab="知识库" key="knowledge">
                <div>
                  {selectedAssistant.knowledge_bases?.map(kbId => {
                    const kb = knowledgeBases.find(k => k.id === kbId);
                    return kb ? (
                      <Card key={kbId} size="small" style={{ marginBottom: 8 }}>
                        <div style={{ fontWeight: 'bold' }}>{kb.name}</div>
                        <div style={{ fontSize: '12px', color: '#666' }}>
                          {kb.description}
                        </div>
                      </Card>
                    ) : null;
                  })}
                  {(!selectedAssistant.knowledge_bases || selectedAssistant.knowledge_bases.length === 0) && (
                    <Text type="secondary">未配置知识库</Text>
                  )}
                </div>
              </TabPane>
              
              <TabPane tab="发布角色" key="roles">
                <div>
                  {selectedAssistant.published_roles?.map(roleCode => {
                    const roleNames = {
                      'store_manager': '门店经理',
                      'finance_manager': '财务经理',
                      'warehouse_manager': '仓库经理',
                      'purchase_manager': '采购经理',
                      'operation_manager': '运营经理',
                      'project_admin': '项目管理员',
                      'tenant_admin': '租户管理员'
                    };
                    return (
                      <Card key={roleCode} size="small" style={{ marginBottom: 8 }}>
                        <div style={{ fontWeight: 'bold' }}>{roleNames[roleCode] || roleCode}</div>
                        <div style={{ fontSize: '12px', color: '#666' }}>
                          该角色用户可在专用AI助手页面使用此助手
                        </div>
                      </Card>
                    );
                  })}
                  {(!selectedAssistant.published_roles || selectedAssistant.published_roles.length === 0) && (
                    <Text type="secondary">未发布到任何角色</Text>
                  )}
                </div>
              </TabPane>
            </Tabs>
          </div>
        )}
      </Drawer>
    </div>
  );
};

export default AIAssistantManagement; 