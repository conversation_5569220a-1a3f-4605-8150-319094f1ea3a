import React, { useState, useEffect } from 'react';
import {
  Card,
  Table,
  Button,
  Space,
  Tag,
  Tooltip,
  Modal,
  Form,
  Input,
  Select,
  message,
  Drawer,
  Divider,
  Upload,
  Progress,
  Typography,
  Tabs,
  Badge,
  Popconfirm
} from 'antd';
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  UploadOutlined,
  ReloadOutlined,
  FileTextOutlined,
  QuestionCircleOutlined,
  SyncOutlined,
  SettingOutlined,
  EyeOutlined,
  CloudUploadOutlined
} from '@ant-design/icons';
import { useProject } from '../../contexts/ProjectContext';
import request from '../../services/request';

const { Title, Text } = Typography;
const { TabPane } = Tabs;
const { Option } = Select;
const { TextArea } = Input;

const AIKnowledgeBaseList = () => {
  const { currentProject } = useProject();
  const [loading, setLoading] = useState(false);
  const [knowledgeBases, setKnowledgeBases] = useState([]);
  const [documents, setDocuments] = useState([]);
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 10,
    total: 0
  });
  const [activeTab, setActiveTab] = useState('bases');
  const [drawerVisible, setDrawerVisible] = useState(false);
  const [currentKnowledgeBase, setCurrentKnowledgeBase] = useState(null);
  const [documentDrawerVisible, setDocumentDrawerVisible] = useState(false);
  const [currentDocument, setCurrentDocument] = useState(null);
  const [uploadVisible, setUploadVisible] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [fileList, setFileList] = useState([]);
  const [selectedKnowledgeBaseId, setSelectedKnowledgeBaseId] = useState(null);
  const [form] = Form.useForm();
  const [documentForm] = Form.useForm();
  const [uploadForm] = Form.useForm();

  // 获取知识库列表
  const fetchKnowledgeBases = async () => {
    setLoading(true);
    try {
      const response = await request.get(`/project/${currentProject.id}/ai/knowledge/bases`);
      if (response.success) {
        setKnowledgeBases(response.data || []);
        setPagination({
          ...pagination,
          total: response.total || 0
        });
      }
    } catch (error) {
      console.error('获取知识库列表失败:', error);
      message.error('获取知识库列表失败');
    } finally {
      setLoading(false);
    }
  };

  // 获取文档列表
  const fetchDocuments = async (knowledgeBaseId) => {
    if (!knowledgeBaseId) return;
    
    setLoading(true);
    try {
      const response = await request.get(`/project/${currentProject.id}/ai/knowledge/bases/${knowledgeBaseId}/documents`);
      if (response.success) {
        setDocuments(response.data || []);
      }
    } catch (error) {
      console.error('获取文档列表失败:', error);
      message.error('获取文档列表失败');
    } finally {
      setLoading(false);
    }
  };

  // 初始化
  useEffect(() => {
    if (currentProject?.id) {
      fetchKnowledgeBases();
    }
  }, [currentProject?.id]);

  // 打开知识库抽屉
  const showKnowledgeBaseDrawer = (knowledgeBase = null) => {
    setCurrentKnowledgeBase(knowledgeBase);
    
    if (knowledgeBase) {
      form.setFieldsValue({
        name: knowledgeBase.name,
        description: knowledgeBase.description,
        industry_type: knowledgeBase.industry_type || 'retail',
        status: knowledgeBase.status || 'active'
      });
    } else {
      form.resetFields();
      form.setFieldsValue({
        industry_type: 'retail',
        status: 'active'
      });
    }
    
    setDrawerVisible(true);
  };

  // 关闭知识库抽屉
  const closeKnowledgeBaseDrawer = () => {
    setDrawerVisible(false);
    setCurrentKnowledgeBase(null);
    form.resetFields();
  };

  // 保存知识库
  const handleSaveKnowledgeBase = async () => {
    try {
      const values = await form.validateFields();
      
      if (currentKnowledgeBase) {
        // 更新知识库
        const response = await request.put(`/project/${currentProject.id}/ai/knowledge/bases/${currentKnowledgeBase.id}`, values);
        if (response.success) {
          message.success('更新知识库成功');
          closeKnowledgeBaseDrawer();
          fetchKnowledgeBases();
        } else {
          message.error(response.message || '更新知识库失败');
        }
      } else {
        // 创建知识库
        const response = await request.post(`/project/${currentProject.id}/ai/knowledge/bases`, values);
        if (response.success) {
          message.success('创建知识库成功');
          closeKnowledgeBaseDrawer();
          fetchKnowledgeBases();
        } else {
          message.error(response.message || '创建知识库失败');
        }
      }
    } catch (error) {
      console.error('保存知识库出错:', error);
    }
  };

  // 删除知识库
  const handleDeleteKnowledgeBase = async (id) => {
    try {
      const response = await request.delete(`/project/${currentProject.id}/ai/knowledge/bases/${id}`);
      if (response.success) {
        message.success('删除知识库成功');
        fetchKnowledgeBases();
      } else {
        message.error(response.message || '删除知识库失败');
      }
    } catch (error) {
      console.error('删除知识库出错:', error);
      message.error('删除知识库失败');
    }
  };

  // 打开文档抽屉
  const showDocumentDrawer = (document = null) => {
    setCurrentDocument(document);
    
    if (document) {
      documentForm.setFieldsValue({
        title: document.title,
        content: document.content,
        status: document.status || 'active'
      });
    } else {
      documentForm.resetFields();
      documentForm.setFieldsValue({
        status: 'active'
      });
    }
    
    setDocumentDrawerVisible(true);
  };

  // 关闭文档抽屉
  const closeDocumentDrawer = () => {
    setDocumentDrawerVisible(false);
    setCurrentDocument(null);
    documentForm.resetFields();
  };

  // 保存文档
  const handleSaveDocument = async () => {
    try {
      const values = await documentForm.validateFields();
      
      if (!selectedKnowledgeBaseId) {
        message.error('请先选择知识库');
        return;
      }
      
      if (currentDocument) {
        // 更新文档
        const response = await request.put(
          `/project/${currentProject.id}/ai/knowledge/bases/${selectedKnowledgeBaseId}/documents/${currentDocument.id}`, 
          values
        );
        if (response.success) {
          message.success('更新文档成功');
          closeDocumentDrawer();
          fetchDocuments(selectedKnowledgeBaseId);
        } else {
          message.error(response.message || '更新文档失败');
        }
      } else {
        // 创建文档
        const response = await request.post(
          `/project/${currentProject.id}/ai/knowledge/bases/${selectedKnowledgeBaseId}/documents`, 
          values
        );
        if (response.success) {
          message.success('创建文档成功');
          closeDocumentDrawer();
          fetchDocuments(selectedKnowledgeBaseId);
        } else {
          message.error(response.message || '创建文档失败');
        }
      }
    } catch (error) {
      console.error('保存文档出错:', error);
    }
  };

  // 删除文档
  const handleDeleteDocument = async (id) => {
    try {
      const response = await request.delete(
        `/project/${currentProject.id}/ai/knowledge/bases/${selectedKnowledgeBaseId}/documents/${id}`
      );
      if (response.success) {
        message.success('删除文档成功');
        fetchDocuments(selectedKnowledgeBaseId);
      } else {
        message.error(response.message || '删除文档失败');
      }
    } catch (error) {
      console.error('删除文档出错:', error);
      message.error('删除文档失败');
    }
  };

  // 打开上传抽屉
  const showUploadDrawer = () => {
    if (!selectedKnowledgeBaseId) {
      message.error('请先选择知识库');
      return;
    }
    
    setUploadVisible(true);
    setFileList([]);
    setUploadProgress(0);
    uploadForm.resetFields();
  };

  // 关闭上传抽屉
  const closeUploadDrawer = () => {
    setUploadVisible(false);
    setFileList([]);
    setUploadProgress(0);
    uploadForm.resetFields();
  };

  // 处理文件上传
  const handleUpload = async () => {
    try {
      if (fileList.length === 0) {
        message.error('请选择要上传的文件');
        return;
      }
      
      const formData = new FormData();
      fileList.forEach(file => {
        formData.append('files', file);
      });
      
      // 模拟上传进度
      let progress = 0;
      const interval = setInterval(() => {
        progress += 10;
        if (progress > 90) {
          clearInterval(interval);
        }
        setUploadProgress(progress);
      }, 300);
      
      const response = await request.post(
        `/project/${currentProject.id}/ai/knowledge/bases/${selectedKnowledgeBaseId}/upload`,
        formData,
        {
          headers: {
            'Content-Type': 'multipart/form-data'
          }
        }
      );
      
      clearInterval(interval);
      setUploadProgress(100);
      
      if (response.success) {
        message.success('文件上传成功');
        setTimeout(() => {
          closeUploadDrawer();
          fetchDocuments(selectedKnowledgeBaseId);
        }, 1000);
      } else {
        message.error(response.message || '文件上传失败');
      }
    } catch (error) {
      console.error('文件上传出错:', error);
      message.error('文件上传失败');
      setUploadProgress(0);
    }
  };

  // 处理文件列表变化
  const handleFileChange = ({ fileList }) => {
    setFileList(fileList);
  };

  // 处理知识库选择
  const handleKnowledgeBaseSelect = (knowledgeBaseId) => {
    setSelectedKnowledgeBaseId(knowledgeBaseId);
    fetchDocuments(knowledgeBaseId);
  };

  // 知识库表格列定义
  const knowledgeBaseColumns = [
    {
      title: '知识库名称',
      dataIndex: 'name',
      key: 'name',
    },
    {
      title: '描述',
      dataIndex: 'description',
      key: 'description',
      ellipsis: true,
    },
    {
      title: '行业',
      dataIndex: 'industry_type',
      key: 'industry_type',
      render: (type) => {
        const industryMap = {
          'retail': '零售',
          'restaurant': '餐饮',
          'hotel': '酒店',
          'education': '教育',
          'healthcare': '医疗',
          'general': '通用'
        };
        return industryMap[type] || type;
      }
    },
    {
      title: '文档数量',
      dataIndex: 'document_count',
      key: 'document_count',
      render: (count) => count || 0
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status) => (
        <Tag color={status === 'active' ? 'green' : 'default'}>
          {status === 'active' ? '启用' : '禁用'}
        </Tag>
      ),
    },
    {
      title: '操作',
      key: 'action',
      render: (_, record) => (
        <Space size="small">
          <Tooltip title="查看文档">
            <Button
              type="text"
              icon={<EyeOutlined />}
              onClick={() => {
                setActiveTab('documents');
                handleKnowledgeBaseSelect(record.id);
              }}
            />
          </Tooltip>
          <Tooltip title="编辑">
            <Button
              type="text"
              icon={<EditOutlined />}
              onClick={() => showKnowledgeBaseDrawer(record)}
            />
          </Tooltip>
          <Popconfirm
            title="确定要删除这个知识库吗？"
            description="删除后将无法恢复，且所有关联的文档也将被删除。"
            onConfirm={() => handleDeleteKnowledgeBase(record.id)}
            okText="删除"
            cancelText="取消"
          >
            <Button
              type="text"
              danger
              icon={<DeleteOutlined />}
            />
          </Popconfirm>
        </Space>
      ),
    },
  ];

  // 文档表格列定义
  const documentColumns = [
    {
      title: '文档标题',
      dataIndex: 'title',
      key: 'title',
    },
    {
      title: '内容预览',
      dataIndex: 'content',
      key: 'content',
      ellipsis: true,
      render: (content) => content ? content.substring(0, 50) + '...' : '-'
    },
    {
      title: '文件类型',
      dataIndex: 'file_type',
      key: 'file_type',
      render: (type) => type || '-'
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status) => (
        <Tag color={status === 'active' ? 'green' : 'default'}>
          {status === 'active' ? '启用' : '禁用'}
        </Tag>
      ),
    },
    {
      title: '操作',
      key: 'action',
      render: (_, record) => (
        <Space size="small">
          <Tooltip title="编辑">
            <Button
              type="text"
              icon={<EditOutlined />}
              onClick={() => showDocumentDrawer(record)}
            />
          </Tooltip>
          <Popconfirm
            title="确定要删除这个文档吗？"
            description="删除后将无法恢复。"
            onConfirm={() => handleDeleteDocument(record.id)}
            okText="删除"
            cancelText="取消"
          >
            <Button
              type="text"
              danger
              icon={<DeleteOutlined />}
            />
          </Popconfirm>
        </Space>
      ),
    },
  ];

  return (
    <div>
      <Card
        variant="outlined"
        title={
          <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            <span>AI 知识库管理</span>
            <Space>
              <span
                style={{
                  cursor: 'pointer',
                  fontWeight: activeTab === 'bases' ? 'bold' : 'normal',
                  borderBottom: activeTab === 'bases' ? '2px solid #1890ff' : 'none',
                  paddingBottom: 8,
                  marginRight: 16
                }}
                onClick={() => setActiveTab('bases')}
              >
                知识库列表
              </span>
              <span
                style={{
                  cursor: 'pointer',
                  fontWeight: activeTab === 'documents' ? 'bold' : 'normal',
                  borderBottom: activeTab === 'documents' ? '2px solid #1890ff' : 'none',
                  paddingBottom: 8
                }}
                onClick={() => setActiveTab('documents')}
              >
                文档管理
                {selectedKnowledgeBaseId && (
                  <Badge 
                    count={knowledgeBases.find(kb => kb.id === selectedKnowledgeBaseId)?.name} 
                    style={{ backgroundColor: '#1890ff', marginLeft: 8 }} 
                  />
                )}
              </span>
            </Space>
          </div>
        }
      >
        {activeTab === 'bases' ? (
          <>
            <div style={{ marginBottom: 16 }}>
              <Space>
                <Button
                  type="primary"
                  icon={<PlusOutlined />}
                  onClick={() => showKnowledgeBaseDrawer()}
                >
                  创建知识库
                </Button>
                <Button
                  icon={<ReloadOutlined />}
                  onClick={fetchKnowledgeBases}
                >
                  刷新
                </Button>
              </Space>
            </div>
            <Table
              columns={knowledgeBaseColumns}
              dataSource={knowledgeBases}
              rowKey="id"
              pagination={pagination}
              loading={loading}
            />
          </>
        ) : (
          <>
            <div style={{ marginBottom: 16 }}>
              <Space>
                <Select
                  placeholder="请选择知识库"
                  style={{ width: 200 }}
                  value={selectedKnowledgeBaseId}
                  onChange={handleKnowledgeBaseSelect}
                >
                  {knowledgeBases.map(kb => (
                    <Option key={kb.id} value={kb.id}>{kb.name}</Option>
                  ))}
                </Select>
                <Button
                  type="primary"
                  icon={<PlusOutlined />}
                  onClick={() => showDocumentDrawer()}
                  disabled={!selectedKnowledgeBaseId}
                >
                  添加文档
                </Button>
                <Button
                  type="primary"
                  icon={<CloudUploadOutlined />}
                  onClick={showUploadDrawer}
                  disabled={!selectedKnowledgeBaseId}
                >
                  上传文件
                </Button>
                <Button
                  icon={<ReloadOutlined />}
                  onClick={() => fetchDocuments(selectedKnowledgeBaseId)}
                  disabled={!selectedKnowledgeBaseId}
                >
                  刷新
                </Button>
              </Space>
            </div>
            <Table
              columns={documentColumns}
              dataSource={documents}
              rowKey="id"
              pagination={pagination}
              loading={loading}
              locale={{ emptyText: selectedKnowledgeBaseId ? '暂无文档' : '请先选择知识库' }}
            />
          </>
        )}
      </Card>

      {/* 知识库抽屉 */}
      <Drawer
        title={currentKnowledgeBase ? '编辑知识库' : '创建知识库'}
        width={600}
        onClose={closeKnowledgeBaseDrawer}
        open={drawerVisible}
        styles={{ body: { paddingBottom: 80 } }}
        footer={
          <div style={{ textAlign: 'right' }}>
            <Button onClick={closeKnowledgeBaseDrawer} style={{ marginRight: 8 }}>
              取消
            </Button>
            <Button onClick={handleSaveKnowledgeBase} type="primary" loading={loading}>
              保存
            </Button>
          </div>
        }
      >
        <Form
          form={form}
          layout="vertical"
        >
          <Form.Item
            name="name"
            label="知识库名称"
            rules={[{ required: true, message: '请输入知识库名称' }]}
          >
            <Input placeholder="请输入知识库名称" />
          </Form.Item>

          <Form.Item
            name="description"
            label="描述"
          >
            <TextArea rows={4} placeholder="请输入知识库描述" />
          </Form.Item>

          <Form.Item
            name="industry_type"
            label="行业类型"
            rules={[{ required: true, message: '请选择行业类型' }]}
          >
            <Select placeholder="请选择行业类型">
              <Option value="retail">零售</Option>
              <Option value="restaurant">餐饮</Option>
              <Option value="hotel">酒店</Option>
              <Option value="education">教育</Option>
              <Option value="healthcare">医疗</Option>
              <Option value="general">通用</Option>
            </Select>
          </Form.Item>

          <Form.Item
            name="status"
            label="状态"
          >
            <Select>
              <Option value="active">启用</Option>
              <Option value="inactive">禁用</Option>
            </Select>
          </Form.Item>
        </Form>
      </Drawer>

      {/* 文档抽屉 */}
      <Drawer
        title={currentDocument ? '编辑文档' : '添加文档'}
        width={600}
        onClose={closeDocumentDrawer}
        open={documentDrawerVisible}
        styles={{ body: { paddingBottom: 80 } }}
        footer={
          <div style={{ textAlign: 'right' }}>
            <Button onClick={closeDocumentDrawer} style={{ marginRight: 8 }}>
              取消
            </Button>
            <Button onClick={handleSaveDocument} type="primary" loading={loading}>
              保存
            </Button>
          </div>
        }
      >
        <Form
          form={documentForm}
          layout="vertical"
        >
          <Form.Item
            name="title"
            label="文档标题"
            rules={[{ required: true, message: '请输入文档标题' }]}
          >
            <Input placeholder="请输入文档标题" />
          </Form.Item>

          <Form.Item
            name="content"
            label="文档内容"
            rules={[{ required: true, message: '请输入文档内容' }]}
          >
            <TextArea rows={10} placeholder="请输入文档内容" />
          </Form.Item>

          <Form.Item
            name="status"
            label="状态"
          >
            <Select>
              <Option value="active">启用</Option>
              <Option value="inactive">禁用</Option>
            </Select>
          </Form.Item>
        </Form>
      </Drawer>

      {/* 上传抽屉 */}
      <Drawer
        title="上传文件"
        width={500}
        onClose={closeUploadDrawer}
        open={uploadVisible}
        styles={{ body: { paddingBottom: 80 } }}
        footer={
          <div style={{ textAlign: 'right' }}>
            <Button onClick={closeUploadDrawer} style={{ marginRight: 8 }}>
              取消
            </Button>
            <Button onClick={handleUpload} type="primary" loading={loading} disabled={fileList.length === 0}>
              上传
            </Button>
          </div>
        }
      >
        <Form
          form={uploadForm}
          layout="vertical"
        >
          <Form.Item
            label="选择文件"
            required
          >
            <Upload
              fileList={fileList}
              onChange={handleFileChange}
              beforeUpload={() => false}
              multiple
            >
              <Button icon={<UploadOutlined />}>选择文件</Button>
            </Upload>
            <div style={{ marginTop: 16 }}>
              <Text type="secondary">支持的文件格式：PDF, DOCX, TXT, MD</Text>
            </div>
          </Form.Item>

          {uploadProgress > 0 && (
            <Form.Item>
              <Progress percent={uploadProgress} status={uploadProgress === 100 ? 'success' : 'active'} />
            </Form.Item>
          )}
        </Form>
      </Drawer>
    </div>
  );
};

export default AIKnowledgeBaseList;
