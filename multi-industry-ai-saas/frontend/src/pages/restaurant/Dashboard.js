import React, { useState, useEffect } from 'react';
import {
  Card,
  Row,
  Col,
  Statistic,
  Typography,
  Table,
  Button,
  Space,
  Tooltip,
  Tag,
  Progress,
  Spin,
  Alert,
  Divider,
  List,
  Avatar,
  Badge
} from 'antd';
import {
  DashboardOutlined,
  CoffeeOutlined,
  ShopOutlined,
  TeamOutlined,
  Bar<PERSON>hartOutlined,
  ReloadOutlined,
  EyeOutlined,
  SettingOutlined,
  RiseOutlined,
  FallOutlined,
  ExclamationCircleOutlined,
  DollarOutlined,
  HddOutlined,
  SolutionOutlined,
  ClockCircleOutlined,
  FileTextOutlined,
  InfoCircleOutlined,
  StarOutlined,
  UserOutlined
} from '@ant-design/icons';
import { useNavigate } from 'react-router-dom';
import { useTenant } from '../../contexts/TenantContext';
import apiService from '../../services/api';
import { Line, Column, Pie } from '@ant-design/plots';

const { Title, Paragraph, Text } = Typography;

const RestaurantDashboard = () => {
  const navigate = useNavigate();
  const { currentTenant, currentProject } = useTenant();

  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [dashboardData, setDashboardData] = useState({
    summary: {
      totalSales: 0,
      totalOrders: 0,
      totalCustomers: 0,
      totalItems: 0,
      averageOrderValue: 0,
      tableOccupancyRate: 0,
    },
    salesTrend: [],
    topItems: [],
    topRestaurants: [],
    inventoryAlerts: [],
    categoryPerformance: [],
    recentOrders: [],
    aiRecommendations: [],
    currentReservations: []
  });

  // 加载仪表盘数据
  useEffect(() => {
    fetchDashboardData();
  }, [currentTenant, currentProject]);

  // 获取仪表盘数据
  const fetchDashboardData = async () => {
    try {
      setLoading(true);
      setError(null);

      // 这里应该调用API获取实际数据
      // 为了演示，使用模拟数据
      const mockData = {
        summary: {
          totalSales: 98560.75,
          totalOrders: 1856,
          totalCustomers: 1256,
          totalItems: 124,
          averageOrderValue: 53.10,
          tableOccupancyRate: 68.5,
        },
        salesTrend: [
          { date: '2023-06-01', sales: 5560.25 },
          { date: '2023-06-02', sales: 6890.50 },
          { date: '2023-06-03', sales: 7120.75 },
          { date: '2023-06-04', sales: 6450.30 },
          { date: '2023-06-05', sales: 7560.80 },
          { date: '2023-06-06', sales: 8250.40 },
          { date: '2023-06-07', sales: 7870.60 },
          { date: '2023-06-08', sales: 8560.90 },
          { date: '2023-06-09', sales: 9250.30 },
          { date: '2023-06-10', sales: 8890.75 },
          { date: '2023-06-11', sales: 7780.45 },
          { date: '2023-06-12', sales: 8450.80 },
          { date: '2023-06-13', sales: 9780.25 },
          { date: '2023-06-14', sales: 10560.90 },
          { date: '2023-06-15', sales: 11450.60 },
        ],
        topItems: [
          { id: '1', name: '红烧牛肉面', category: '面食', sales: 15600, profit: 7800, stock: 'unlimited' },
          { id: '2', name: '宫保鸡丁', category: '热菜', sales: 12800, profit: 6400, stock: 'unlimited' },
          { id: '3', name: '水煮鱼', category: '热菜', sales: 10500, profit: 5250, stock: 'unlimited' },
          { id: '4', name: '青岛啤酒', category: '饮品', sales: 9800, profit: 5880, stock: 120 },
          { id: '5', name: '提拉米苏', category: '甜点', sales: 8600, profit: 5160, stock: 45 },
        ],
        topRestaurants: [
          { id: '1', name: '北京王府井店', region: '华北', sales: 35600, orders: 650, growth: 18.2 },
          { id: '2', name: '上海南京路店', region: '华东', sales: 28900, orders: 580, growth: 15.5 },
          { id: '3', name: '广州天河店', region: '华南', sales: 22600, orders: 420, growth: 10.7 },
          { id: '4', name: '深圳福田店', region: '华南', sales: 18900, orders: 385, growth: 12.2 },
          { id: '5', name: '成都春熙路店', region: '西南', sales: 15600, orders: 350, growth: 11.5 },
        ],
        inventoryAlerts: [
          { id: '1', item: '青岛啤酒', category: '饮品', stock: 15, threshold: 20, status: 'low' },
          { id: '2', name: '提拉米苏', category: '甜点', stock: 5, threshold: 10, status: 'low' },
          { id: '3', name: '红酒', category: '饮品', stock: 3, threshold: 8, status: 'low' },
          { id: '4', name: '牛排', category: '热菜', stock: 8, threshold: 10, status: 'low' },
          { id: '5', name: '三文鱼', category: '生鲜', stock: 0, threshold: 5, status: 'out' },
        ],
        categoryPerformance: [
          { category: '热菜', sales: 38500, percentage: 39.1 },
          { category: '面食', sales: 22600, percentage: 22.9 },
          { category: '饮品', sales: 18900, percentage: 19.2 },
          { category: '甜点', sales: 12800, percentage: 13.0 },
          { category: '其他', sales: 5760, percentage: 5.8 },
        ],
        recentOrders: [
          { id: '1', customer: '张三', restaurant: '北京王府井店', amount: 560.50, items: 5, status: 'completed', created_at: '2023-06-15T10:30:00Z' },
          { id: '2', customer: '李四', restaurant: '上海南京路店', amount: 320.75, items: 3, status: 'processing', created_at: '2023-06-15T09:45:00Z' },
          { id: '3', customer: '王五', restaurant: '广州天河店', amount: 180.25, items: 2, status: 'completed', created_at: '2023-06-15T08:20:00Z' },
          { id: '4', customer: '赵六', restaurant: '深圳福田店', amount: 450.80, items: 4, status: 'completed', created_at: '2023-06-14T16:50:00Z' },
          { id: '5', customer: '钱七', restaurant: '成都春熙路店', amount: 290.60, items: 3, status: 'processing', created_at: '2023-06-14T15:30:00Z' },
        ],
        aiRecommendations: [
          { id: '1', title: '菜单优化建议', content: '热菜类销售占比高，建议增加热菜品种，特别是宫保鸡丁类似的川菜。', type: 'success' },
          { id: '2', title: '库存预警', content: '饮品类库存不足，特别是青岛啤酒和红酒，建议及时补货。', type: 'warning' },
          { id: '3', title: '餐厅表现分析', content: '北京王府井店和上海南京路店表现优异，可复制其成功经验到其他餐厅。', type: 'success' },
          { id: '4', title: '客户行为洞察', content: '数据显示晚餐时段客流量增加50%，建议增加晚餐时段的服务人员配置。', type: 'info' },
          { id: '5', title: '促销建议', content: '面食和饮品搭配销售效果好，建议推出套餐促销。', type: 'success' },
        ],
        currentReservations: [
          { id: '1', customer: '张三', people: 4, time: '2023-06-15T18:30:00Z', table: 'A-12', status: 'confirmed' },
          { id: '2', customer: '李四', people: 2, time: '2023-06-15T19:00:00Z', table: 'B-05', status: 'confirmed' },
          { id: '3', customer: '王五', people: 6, time: '2023-06-15T19:30:00Z', table: 'C-08', status: 'pending' },
          { id: '4', customer: '赵六', people: 3, time: '2023-06-15T20:00:00Z', table: 'A-08', status: 'confirmed' },
          { id: '5', customer: '钱七', people: 8, time: '2023-06-15T20:30:00Z', table: 'D-02', status: 'pending' },
        ],
      };

      setDashboardData(mockData);
    } catch (error) {
      console.error('获取仪表盘数据失败:', error);
      setError('获取仪表盘数据失败，请稍后重试');
    } finally {
      setLoading(false);
    }
  };

  // 获取状态标签
  const getStatusTag = (status) => {
    switch (status) {
      case 'completed':
        return <Tag color="success">已完成</Tag>;
      case 'processing':
        return <Tag color="processing">处理中</Tag>;
      case 'pending':
        return <Tag color="warning">待处理</Tag>;
      case 'cancelled':
        return <Tag color="error">已取消</Tag>;
      case 'confirmed':
        return <Tag color="success">已确认</Tag>;
      case 'low':
        return <Tag color="warning">库存不足</Tag>;
      case 'out':
        return <Tag color="error">缺货</Tag>;
      default:
        return <Tag>{status}</Tag>;
    }
  };

  // 获取AI建议类型图标
  const getRecommendationIcon = (type) => {
    switch (type) {
      case 'success':
        return <RiseOutlined style={{ color: '#52c41a' }} />;
      case 'warning':
        return <ExclamationCircleOutlined style={{ color: '#faad14' }} />;
      case 'info':
        return <InfoCircleOutlined style={{ color: '#1890ff' }} />;
      default:
        return <InfoCircleOutlined style={{ color: '#1890ff' }} />;
    }
  };

  // 菜品表格列
  const itemColumns = [
    {
      title: '菜品名称',
      dataIndex: 'name',
      key: 'name',
    },
    {
      title: '分类',
      dataIndex: 'category',
      key: 'category',
    },
    {
      title: '销售额',
      dataIndex: 'sales',
      key: 'sales',
      render: (text) => `¥${text.toLocaleString()}`,
      sorter: (a, b) => a.sales - b.sales,
    },
    {
      title: '利润',
      dataIndex: 'profit',
      key: 'profit',
      render: (text) => `¥${text.toLocaleString()}`,
    },
    {
      title: '库存',
      dataIndex: 'stock',
      key: 'stock',
      render: (text) => text === 'unlimited' ? '不限' : text,
    },
    {
      title: '操作',
      key: 'action',
      render: (_, record) => (
        <Space size="small">
          <Tooltip title="查看详情">
            <Button
              type="text"
              icon={<EyeOutlined />}
              onClick={() => navigate(`/restaurant/menu/${record.id}`)}
            />
          </Tooltip>
        </Space>
      ),
    },
  ];

  // 餐厅表格列
  const restaurantColumns = [
    {
      title: '餐厅名称',
      dataIndex: 'name',
      key: 'name',
    },
    {
      title: '区域',
      dataIndex: 'region',
      key: 'region',
    },
    {
      title: '销售额',
      dataIndex: 'sales',
      key: 'sales',
      render: (text) => `¥${text.toLocaleString()}`,
      sorter: (a, b) => a.sales - b.sales,
    },
    {
      title: '订单数',
      dataIndex: 'orders',
      key: 'orders',
    },
    {
      title: '增长率',
      dataIndex: 'growth',
      key: 'growth',
      render: (text) => (
        <span>
          {text >= 0 ? (
            <RiseOutlined style={{ color: '#52c41a' }} />
          ) : (
            <FallOutlined style={{ color: '#f5222d' }} />
          )}
          {' '}
          {Math.abs(text)}%
        </span>
      ),
    },
    {
      title: '操作',
      key: 'action',
      render: (_, record) => (
        <Space size="small">
          <Tooltip title="查看详情">
            <Button
              type="text"
              icon={<EyeOutlined />}
              onClick={() => navigate(`/restaurant/restaurants/${record.id}`)}
            />
          </Tooltip>
        </Space>
      ),
    },
  ];

  // 订单表格列
  const orderColumns = [
    {
      title: '订单号',
      dataIndex: 'id',
      key: 'id',
    },
    {
      title: '客户',
      dataIndex: 'customer',
      key: 'customer',
    },
    {
      title: '餐厅',
      dataIndex: 'restaurant',
      key: 'restaurant',
    },
    {
      title: '金额',
      dataIndex: 'amount',
      key: 'amount',
      render: (text) => `¥${text.toLocaleString()}`,
    },
    {
      title: '菜品数',
      dataIndex: 'items',
      key: 'items',
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (text) => getStatusTag(text),
    },
    {
      title: '创建时间',
      dataIndex: 'created_at',
      key: 'created_at',
      render: (text) => new Date(text).toLocaleString(),
    },
    {
      title: '操作',
      key: 'action',
      render: (_, record) => (
        <Space size="small">
          <Tooltip title="查看详情">
            <Button
              type="text"
              icon={<EyeOutlined />}
              onClick={() => navigate(`/restaurant/orders/${record.id}`)}
            />
          </Tooltip>
        </Space>
      ),
    },
  ];

  // 预订表格列
  const reservationColumns = [
    {
      title: '客户',
      dataIndex: 'customer',
      key: 'customer',
    },
    {
      title: '人数',
      dataIndex: 'people',
      key: 'people',
    },
    {
      title: '时间',
      dataIndex: 'time',
      key: 'time',
      render: (text) => new Date(text).toLocaleString(),
    },
    {
      title: '桌号',
      dataIndex: 'table',
      key: 'table',
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (text) => getStatusTag(text),
    },
    {
      title: '操作',
      key: 'action',
      render: (_, record) => (
        <Space size="small">
          <Button
            type="primary"
            size="small"
            onClick={() => navigate(`/restaurant/reservations/${record.id}`)}
          >
            处理
          </Button>
        </Space>
      ),
    },
  ];

  // 销售趋势图配置
  const salesTrendConfig = {
    data: dashboardData.salesTrend,
    xField: 'date',
    yField: 'sales',
    smooth: true,
    point: {
      size: 5,
      shape: 'diamond',
    },
    tooltip: {
      formatter: (datum) => {
        return { name: '销售额', value: `¥${datum.sales.toLocaleString()}` };
      },
    },
    xAxis: {
      title: {
        text: '日期',
      },
    },
    yAxis: {
      title: {
        text: '销售额 (元)',
      },
    },
    meta: {
      sales: {
        alias: '销售额',
      },
      date: {
        alias: '日期',
      },
    },
  };

  // 分类销售图配置
  const categoryPieConfig = {
    data: dashboardData.categoryPerformance.map(item => ({
      type: item.category,
      value: item.sales,
    })),
    angleField: 'value',
    colorField: 'type',
    radius: 0.8,
    label: {
      type: 'outer',
      formatter: (datum, item) => `${datum.type}: ${(item.percent * 100).toFixed(1)}%`,
    },
    tooltip: {
      formatter: (datum) => {
        return { name: datum.type, value: `¥${datum.value.toLocaleString()}` };
      },
    },
    legend: {
      layout: 'vertical',
      position: 'right',
    },
    interactions: [{ type: 'element-active' }],
  };

  // 错误提示
  const errorAlert = error ? (
    <Alert
      message="错误"
      description={error}
      type="error"
      showIcon
      style={{ marginBottom: 16 }}
    />
  ) : null;

  return (
    <div className="restaurant-dashboard">
      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: 24 }}>
        <div>
          <Title level={2}><CoffeeOutlined /> 餐饮仪表盘</Title>
          <Paragraph>
            {currentProject ? `${currentProject.name} - ` : ''}
            查看销售数据、菜品表现和AI分析建议。
          </Paragraph>
        </div>
        <Button
          type="primary"
          icon={<ReloadOutlined />}
          onClick={fetchDashboardData}
          loading={loading}
        >
          刷新数据
        </Button>
      </div>

      {errorAlert}

      <Spin spinning={loading}>
        {/* 统计卡片 */}
        <Row gutter={16}>
          <Col span={4}>
            <Card>
              <Statistic
                title="总销售额"
                value={dashboardData.summary.totalSales}
                precision={2}
                valueStyle={{ color: '#3f8600' }}
                prefix={<DollarOutlined />}
                suffix="元"
              />
            </Card>
          </Col>
          <Col span={4}>
            <Card>
              <Statistic
                title="总订单数"
                value={dashboardData.summary.totalOrders}
                valueStyle={{ color: '#1890ff' }}
                prefix={<FileTextOutlined />}
              />
            </Card>
          </Col>
          <Col span={4}>
            <Card>
              <Statistic
                title="客户数"
                value={dashboardData.summary.totalCustomers}
                valueStyle={{ color: '#722ed1' }}
                prefix={<UserOutlined />}
              />
            </Card>
          </Col>
          <Col span={4}>
            <Card>
              <Statistic
                title="菜品数"
                value={dashboardData.summary.totalItems}
                valueStyle={{ color: '#fa8c16' }}
                prefix={<CoffeeOutlined />}
              />
            </Card>
          </Col>
          <Col span={4}>
            <Card>
              <Statistic
                title="平均订单金额"
                value={dashboardData.summary.averageOrderValue}
                precision={2}
                valueStyle={{ color: '#cf1322' }}
                prefix={<BarChartOutlined />}
                suffix="元"
              />
            </Card>
          </Col>
          <Col span={4}>
            <Card>
              <Statistic
                title="餐桌占用率"
                value={dashboardData.summary.tableOccupancyRate}
                precision={1}
                valueStyle={{ color: '#13c2c2' }}
                prefix={<ShopOutlined />}
                suffix="%"
              />
            </Card>
          </Col>
        </Row>

        <Divider />

        {/* 图表 */}
        <Row gutter={16} style={{ marginTop: 16 }}>
          <Col span={16}>
            <Card title="销售趋势" className="dashboard-card">
              <div style={{ height: 300 }}>
                <Line {...salesTrendConfig} />
              </div>
            </Card>
          </Col>
          <Col span={8}>
            <Card title="分类销售占比" className="dashboard-card">
              <div style={{ height: 300 }}>
                <Pie {...categoryPieConfig} />
              </div>
            </Card>
          </Col>
        </Row>

        <Divider />

        {/* 当前预订 */}
        <Card
          title={
            <span>
              <ClockCircleOutlined style={{ marginRight: 8 }} />
              今日预订
            </span>
          }
          extra={<Button type="link" onClick={() => navigate('/restaurant/reservations')}>查看全部</Button>}
          style={{ marginTop: 16 }}
        >
          <Table
            columns={reservationColumns}
            dataSource={dashboardData.currentReservations}
            rowKey="id"
            pagination={false}
          />
        </Card>

        {/* AI建议 */}
        <Card title="AI分析建议" style={{ marginTop: 16 }}>
          <List
            itemLayout="horizontal"
            dataSource={dashboardData.aiRecommendations}
            renderItem={item => (
              <List.Item>
                <List.Item.Meta
                  avatar={
                    <Avatar icon={getRecommendationIcon(item.type)} />
                  }
                  title={item.title}
                  description={item.content}
                />
              </List.Item>
            )}
          />
        </Card>

        {/* 热销菜品 */}
        <Card
          title="热销菜品"
          extra={<Button type="link" onClick={() => navigate('/restaurant/menu')}>查看全部</Button>}
          style={{ marginTop: 16 }}
        >
          <Table
            columns={itemColumns}
            dataSource={dashboardData.topItems}
            rowKey="id"
            pagination={false}
          />
        </Card>

        {/* 餐厅表现 */}
        <Card
          title="餐厅表现"
          extra={<Button type="link" onClick={() => navigate('/restaurant/restaurants')}>查看全部</Button>}
          style={{ marginTop: 16 }}
        >
          <Table
            columns={restaurantColumns}
            dataSource={dashboardData.topRestaurants}
            rowKey="id"
            pagination={false}
          />
        </Card>

        {/* 库存预警 */}
        <Card
          title={
            <span>
              <ExclamationCircleOutlined style={{ color: '#faad14', marginRight: 8 }} />
              库存预警
            </span>
          }
          extra={<Button type="link" onClick={() => navigate('/restaurant/inventory')}>查看全部</Button>}
          style={{ marginTop: 16 }}
        >
          <Table
            columns={[
              { title: '物品', dataIndex: 'item', key: 'item' },
              { title: '分类', dataIndex: 'category', key: 'category' },
              { title: '当前库存', dataIndex: 'stock', key: 'stock' },
              { title: '预警阈值', dataIndex: 'threshold', key: 'threshold' },
              { title: '状态', dataIndex: 'status', key: 'status', render: (text) => getStatusTag(text) },
              {
                title: '操作',
                key: 'action',
                render: (_, record) => (
                  <Button type="primary" size="small" onClick={() => navigate('/restaurant/inventory/replenish')}>
                    补货
                  </Button>
                ),
              },
            ]}
            dataSource={dashboardData.inventoryAlerts}
            rowKey="id"
            pagination={false}
          />
        </Card>

        {/* 最近订单 */}
        <Card
          title="最近订单"
          extra={<Button type="link" onClick={() => navigate('/restaurant/orders')}>查看全部</Button>}
          style={{ marginTop: 16 }}
        >
          <Table
            columns={orderColumns}
            dataSource={dashboardData.recentOrders}
            rowKey="id"
            pagination={false}
          />
        </Card>
      </Spin>
    </div>
  );
};

export default RestaurantDashboard;
