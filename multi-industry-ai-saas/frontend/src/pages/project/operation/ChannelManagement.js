import React, { useState, useEffect } from 'react';
import dayjs from 'dayjs';
import {
  Card,
  Table,
  Button,
  Space,
  Tag,
  Input,
  Form,
  Select,
  Modal,
  message,
  Typography,
  Tabs,
  Row,
  Col,
  Radio,
  Descriptions,
  Badge,
  Divider,
  Spin,
  Statistic
} from 'antd';
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  SyncOutlined,
  SettingOutlined,
  AppstoreOutlined,
  ShopOutlined,
  HomeOutlined,
  BarChartOutlined,
  PieChartOutlined
} from '@ant-design/icons';
import apiService from '../../../services/api';

const { Title, Paragraph, Text } = Typography;
const { TabPane } = Tabs;
const { Option } = Select;
const { TextArea } = Input;

/**
 * 渠道管理组件
 * 用于管理各类销售渠道，包括到店渠道和到家渠道
 */
const ChannelManagement = () => {
  const [activeTab, setActiveTab] = useState('overview');
  const [loading, setLoading] = useState(false);
  const [channels, setChannels] = useState([]);
  const [offlineChannels, setOfflineChannels] = useState([]);
  const [onlineChannels, setOnlineChannels] = useState([]);
  const [stores, setStores] = useState([]);
  const [storesLoading, setStoresLoading] = useState(false);
  const [modalVisible, setModalVisible] = useState(false);
  const [editingChannel, setEditingChannel] = useState(null);
  const [form] = Form.useForm();
  const [channelOrders, setChannelOrders] = useState([]);
  const [channelStats, setChannelStats] = useState({
    total: 0,
    offline: 0,
    online: 0,
    active: 0,
    inactive: 0
  });

  // 获取渠道列表
  const fetchChannels = async () => {
    setLoading(true);
    try {
      const response = await apiService.project.channel.getList();
      if (response && response.items) {
        const channelList = response.items;
        setChannels(channelList);

        // 分类渠道
        setOfflineChannels(channelList.filter(channel => channel.channel_type === 'offline'));
        setOnlineChannels(channelList.filter(channel => channel.channel_type === 'online'));

        // 更新统计数据
        updateChannelStats(channelList);
      }
    } catch (error) {
      console.error('获取渠道列表失败:', error);
      message.error('获取渠道列表失败');
    } finally {
      setLoading(false);
    }
  };

  // 获取门店列表
  const fetchStores = async () => {
    setStoresLoading(true);
    try {
      const response = await apiService.project.store.getList();
      if (response && response.items) {
        setStores(response.items);
      }
    } catch (error) {
      console.error('获取门店列表失败:', error);
      message.error('获取门店列表失败');
    } finally {
      setStoresLoading(false);
    }
  };

  // 获取渠道订单
  const fetchChannelOrders = async () => {
    try {
      const response = await apiService.project.channel.getOrders();
      if (response && response.items) {
        setChannelOrders(response.items);
      } else {
        setChannelOrders([]);
      }
    } catch (error) {
      console.error('获取渠道订单失败:', error);
      setChannelOrders([]);
      // 不显示错误消息，因为这是次要功能
    }
  };

  // 更新渠道统计数据
  const updateChannelStats = (channelList) => {
    const stats = {
      total: channelList.length,
      offline: channelList.filter(channel => channel.channel_type === 'offline').length,
      online: channelList.filter(channel => channel.channel_type === 'online').length,
      active: channelList.filter(channel => channel.status === 'active').length,
      inactive: channelList.filter(channel => channel.status === 'inactive').length
    };
    setChannelStats(stats);
  };

  // 显示创建渠道模态框
  const showCreateModal = () => {
    setEditingChannel(null);
    form.resetFields();
    setModalVisible(true);
  };

  // 显示编辑渠道模态框
  const showEditModal = (channel) => {
    setEditingChannel(channel);
    form.setFieldsValue({
      label: channel.label,
      channel_type: channel.channel_type,
      channel_category: channel.channel_category,
      store_id: channel.store_id,
      description: channel.description,
      contact_person: channel.contact_person,
      contact_phone: channel.contact_phone,
      api_key: channel.api_key,
      api_secret: channel.api_secret,
      api_url: channel.api_url,
      daily_order_limit: channel.daily_order_limit,
      commission_rate: channel.commission_rate,
      status: channel.status
    });
    setModalVisible(true);
  };

  // 处理模态框取消
  const handleModalCancel = () => {
    setModalVisible(false);
    setEditingChannel(null);
    form.resetFields();
  };

  // 处理表单提交
  const handleFormSubmit = async () => {
    try {
      const values = await form.validateFields();
      setLoading(true);

      if (editingChannel) {
        // 更新渠道
        await apiService.project.channel.update(editingChannel.id, values);
        message.success('渠道更新成功');
      } else {
        // 创建渠道
        await apiService.project.channel.create(values);
        message.success('渠道创建成功');
      }

      setModalVisible(false);
      fetchChannels();
    } catch (error) {
      console.error('提交渠道表单失败:', error);
      message.error('提交失败: ' + (error.response?.data?.detail || error.message));
    } finally {
      setLoading(false);
    }
  };

  // 处理删除渠道
  const handleDeleteChannel = (channel) => {
    Modal.confirm({
      title: '确认删除',
      content: `确定要删除渠道 "${channel.label}" 吗？此操作不可恢复。`,
      okText: '删除',
      okType: 'danger',
      cancelText: '取消',
      onOk: async () => {
        setLoading(true);
        try {
          await apiService.project.channel.delete(channel.id);
          message.success('渠道删除成功');
          fetchChannels();
        } catch (error) {
          console.error('删除渠道失败:', error);
          message.error('删除失败: ' + (error.response?.data?.detail || error.message));
        } finally {
          setLoading(false);
        }
      }
    });
  };

  // 初始化
  useEffect(() => {
    fetchChannels();
    fetchStores();
    fetchChannelOrders();
  }, []);

  // 渲染渠道类型标签
  const renderTypeTag = (type) => {
    const typeColors = {
      offline: 'blue',
      online: 'green',
      other: 'orange'
    };

    const typeTexts = {
      offline: '到店渠道',
      online: '到家渠道',
      other: '其他'
    };

    return (
      <Tag color={typeColors[type] || 'default'}>
        {typeTexts[type] || type}
      </Tag>
    );
  };

  // 渲染渠道状态标签
  const renderStatusTag = (status) => {
    const statusColors = {
      active: 'success',
      inactive: 'error',
      pending: 'warning'
    };

    const statusTexts = {
      active: '活跃',
      inactive: '停用',
      pending: '待审核'
    };

    return (
      <Badge
        status={statusColors[status] || 'default'}
        text={statusTexts[status] || status}
      />
    );
  };

  // 渠道表格列定义
  const channelColumns = [
    {
      title: '渠道名称',
      dataIndex: 'label',
      key: 'label',
    },
    {
      title: '渠道类型',
      dataIndex: 'channel_type',
      key: 'channel_type',
      render: renderTypeTag
    },
    {
      title: '渠道分类',
      dataIndex: 'channel_category',
      key: 'channel_category',
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: renderStatusTag
    },
    {
      title: '操作',
      key: 'action',
      render: (_, record) => (
        <Space size="small">
          <Button
            type="text"
            icon={<EditOutlined />}
            onClick={() => showEditModal(record)}
          />
          <Button
            type="text"
            danger
            icon={<DeleteOutlined />}
            onClick={() => handleDeleteChannel(record)}
          />
        </Space>
      ),
    },
  ];

  // 订单表格列定义
  const orderColumns = [
    {
      title: '订单编号',
      dataIndex: 'order_number',
      key: 'order_number',
    },
    {
      title: '渠道',
      dataIndex: 'channel_name',
      key: 'channel_name',
    },
    {
      title: '订单金额',
      dataIndex: 'amount',
      key: 'amount',
      render: (amount) => `¥${parseFloat(amount).toFixed(2)}`
    },
    {
      title: '订单状态',
      dataIndex: 'status',
      key: 'status',
      render: (status) => {
        const statusMap = {
          pending: { color: 'blue', text: '待处理' },
          processing: { color: 'orange', text: '处理中' },
          completed: { color: 'green', text: '已完成' },
          cancelled: { color: 'red', text: '已取消' }
        };

        const { color, text } = statusMap[status] || { color: 'default', text: status };

        return <Tag color={color}>{text}</Tag>;
      }
    },
    {
      title: '创建时间',
      dataIndex: 'created_at',
      key: 'created_at',
    },
    {
      title: '操作',
      key: 'action',
      render: () => (
        <Button type="link" size="small">查看详情</Button>
      )
    }
  ];

  // 渲染概览标签页
  const renderOverviewTab = () => {
    return (
      <div className="channel-overview">
        <Row gutter={16} style={{ marginBottom: 16 }}>
          <Col span={6}>
            <Card>
              <Statistic
                title="渠道总数"
                value={channelStats.total}
                suffix="个"
              />
            </Card>
          </Col>
          <Col span={6}>
            <Card>
              <Statistic
                title="到店渠道"
                value={channelStats.offline}
                suffix="个"
                valueStyle={{ color: '#1890ff' }}
              />
            </Card>
          </Col>
          <Col span={6}>
            <Card>
              <Statistic
                title="到家渠道"
                value={channelStats.online}
                suffix="个"
                valueStyle={{ color: '#52c41a' }}
              />
            </Card>
          </Col>
          <Col span={6}>
            <Card>
              <Statistic
                title="活跃渠道"
                value={channelStats.active}
                suffix="个"
                valueStyle={{ color: '#722ed1' }}
              />
            </Card>
          </Col>
        </Row>

        <Card title="渠道分布" style={{ marginBottom: 16 }}>
          <Row gutter={16}>
            <Col span={12}>
              <Card title="渠道类型分布">
                <div style={{ height: 200, display: 'flex', justifyContent: 'center', alignItems: 'center' }}>
                  <Text>图表加载中...</Text>
                </div>
              </Card>
            </Col>
            <Col span={12}>
              <Card title="渠道状态分布">
                <div style={{ height: 200, display: 'flex', justifyContent: 'center', alignItems: 'center' }}>
                  <Text>图表加载中...</Text>
                </div>
              </Card>
            </Col>
          </Row>
        </Card>

        <Card title="最近渠道订单">
          <Table
            dataSource={channelOrders.slice(0, 5)}
            columns={orderColumns}
            rowKey="id"
            pagination={false}
          />
        </Card>
      </div>
    );
  };

  // 渲染到店渠道标签页
  const renderOfflineTab = () => {
    return (
      <div className="offline-channels">
        <Spin spinning={loading}>
          <Card
            title="到店渠道管理"
            extra={
              <Button
                type="primary"
                icon={<PlusOutlined />}
                onClick={() => {
                  form.setFieldsValue({ channel_type: 'offline' });
                  showCreateModal();
                }}
              >
                添加到店渠道
              </Button>
            }
          >
            {offlineChannels.length > 0 ? (
              <Tabs type="card">
                {offlineChannels.map(channel => (
                  <TabPane
                    tab={channel.label}
                    key={channel.id}
                  >
                    <Card
                      title={`${channel.label} - ${channel.channel_category || '未分类'}`}
                      extra={
                        <Space>
                          <Button
                            type="primary"
                            icon={<SyncOutlined />}
                            onClick={() => message.success(`已开始同步${channel.label}商品数据`)}
                          >
                            同步商品
                          </Button>
                          <Button
                            icon={<SettingOutlined />}
                            onClick={() => showEditModal(channel)}
                          >
                            渠道设置
                          </Button>
                        </Space>
                      }
                    >
                      <Descriptions bordered column={2}>
                        <Descriptions.Item label="渠道类型">{channel.channel_type === 'offline' ? '到店渠道' : '到家渠道'}</Descriptions.Item>
                        <Descriptions.Item label="渠道分类">{channel.channel_category || '未分类'}</Descriptions.Item>
                        <Descriptions.Item label="状态">
                          {renderStatusTag(channel.status)}
                        </Descriptions.Item>
                        <Descriptions.Item label="佣金比例">{channel.commission_rate || 0}%</Descriptions.Item>
                        <Descriptions.Item label="关联门店" span={2}>
                          {channel.store_id ? (
                            stores.find(store => store.id === channel.store_id)?.name || '未找到门店'
                          ) : '未关联门店'}
                        </Descriptions.Item>
                        {channel.description && (
                          <Descriptions.Item label="描述" span={2}>
                            {channel.description}
                          </Descriptions.Item>
                        )}
                        {channel.contact_person && (
                          <Descriptions.Item label="联系人">
                            {channel.contact_person}
                          </Descriptions.Item>
                        )}
                        {channel.contact_phone && (
                          <Descriptions.Item label="联系电话">
                            {channel.contact_phone}
                          </Descriptions.Item>
                        )}
                      </Descriptions>

                      <Divider>商品管理</Divider>
                      <div style={{ textAlign: 'center', padding: '20px 0' }}>
                        <Text type="secondary">暂无商品数据</Text>
                      </div>
                    </Card>
                  </TabPane>
                ))}
              </Tabs>
            ) : (
              <div style={{ textAlign: 'center', padding: '40px 0' }}>
                <Text type="secondary">暂无到店渠道，请点击右上角添加</Text>
              </div>
            )}
          </Card>
        </Spin>
      </div>
    );
  };

  // 渲染到家渠道标签页
  const renderOnlineTab = () => {
    return (
      <div className="online-channels">
        <Spin spinning={loading}>
          <Card
            title="到家渠道管理"
            extra={
              <Button
                type="primary"
                icon={<PlusOutlined />}
                onClick={() => {
                  form.setFieldsValue({ channel_type: 'online' });
                  showCreateModal();
                }}
              >
                添加到家渠道
              </Button>
            }
          >
            {onlineChannels.length > 0 ? (
              <Tabs type="card">
                {onlineChannels.map(channel => (
                  <TabPane
                    tab={channel.label}
                    key={channel.id}
                  >
                    <Card
                      title={`${channel.label} - ${channel.channel_category || '未分类'}`}
                      extra={
                        <Space>
                          <Button
                            type="primary"
                            icon={<SyncOutlined />}
                            onClick={() => message.success(`已开始同步${channel.label}商品数据`)}
                          >
                            同步商品
                          </Button>
                          <Button
                            icon={<SettingOutlined />}
                            onClick={() => showEditModal(channel)}
                          >
                            渠道设置
                          </Button>
                        </Space>
                      }
                    >
                      <Descriptions bordered column={2}>
                        <Descriptions.Item label="渠道类型">{channel.channel_type === 'offline' ? '到店渠道' : '到家渠道'}</Descriptions.Item>
                        <Descriptions.Item label="渠道分类">{channel.channel_category || '未分类'}</Descriptions.Item>
                        <Descriptions.Item label="状态">
                          {renderStatusTag(channel.status)}
                        </Descriptions.Item>
                        <Descriptions.Item label="佣金比例">{channel.commission_rate || 0}%</Descriptions.Item>
                        <Descriptions.Item label="关联门店" span={2}>
                          {channel.store_id ? (
                            stores.find(store => store.id === channel.store_id)?.name || '未找到门店'
                          ) : '未关联门店'}
                        </Descriptions.Item>
                        {channel.description && (
                          <Descriptions.Item label="描述" span={2}>
                            {channel.description}
                          </Descriptions.Item>
                        )}
                        {channel.contact_person && (
                          <Descriptions.Item label="联系人">
                            {channel.contact_person}
                          </Descriptions.Item>
                        )}
                        {channel.contact_phone && (
                          <Descriptions.Item label="联系电话">
                            {channel.contact_phone}
                          </Descriptions.Item>
                        )}
                        {channel.api_key && (
                          <Descriptions.Item label="API配置" span={2}>
                            <Tag color="green">已配置</Tag>
                          </Descriptions.Item>
                        )}
                      </Descriptions>

                      <Divider>商品管理</Divider>
                      <div style={{ textAlign: 'center', padding: '20px 0' }}>
                        <Text type="secondary">暂无商品数据</Text>
                      </div>
                    </Card>
                  </TabPane>
                ))}
              </Tabs>
            ) : (
              <div style={{ textAlign: 'center', padding: '40px 0' }}>
                <Text type="secondary">暂无到家渠道，请点击右上角添加</Text>
              </div>
            )}
          </Card>
        </Spin>
      </div>
    );
  };

  // 渲染渠道配置标签页
  const renderConfigTab = () => {
    return (
      <div className="channel-config">
        <Spin spinning={loading}>
          <Card
            title="渠道平台配置"
            extra={
              <Button
                type="primary"
                icon={<PlusOutlined />}
                onClick={showCreateModal}
              >
                添加渠道平台
              </Button>
            }
          >
            <Table
              dataSource={channels.map(item => ({...item, key: item.id}))}
              columns={[
                ...channelColumns,
                {
                  title: 'API配置',
                  key: 'api_config',
                  render: (_, record) => (
                    <Space>
                      <Tag color={record.api_key ? 'green' : 'red'}>
                        {record.api_key ? 'API已配置' : 'API未配置'}
                      </Tag>
                      <Button
                        type="link"
                        size="small"
                        onClick={() => showEditModal(record)}
                      >
                        配置API
                      </Button>
                    </Space>
                  )
                }
              ]}
              rowKey="id"
            />
          </Card>

          <Divider />

          <Card title="渠道订单管理">
            <Table
              dataSource={channelOrders.map(item => ({...item, key: item.id}))}
              columns={orderColumns}
              rowKey="id"
            />
          </Card>
        </Spin>
      </div>
    );
  };

  return (
    <div className="channel-management-container">
      <Card>
        <Title level={4}>渠道运维管理</Title>
        <Paragraph>
          管理各类销售渠道，包括到店渠道（抖音团购、美团团购、微信私域）和到家渠道（美团闪购、饿了么零售、京东外卖、抖音随心团）等。
        </Paragraph>

        <Tabs activeKey={activeTab} onChange={setActiveTab}>
          <TabPane
            tab={<span><AppstoreOutlined />渠道概览</span>}
            key="overview"
          >
            {renderOverviewTab()}
          </TabPane>
          <TabPane
            tab={<span><ShopOutlined />到店渠道</span>}
            key="offline"
          >
            {renderOfflineTab()}
          </TabPane>
          <TabPane
            tab={<span><HomeOutlined />到家渠道</span>}
            key="online"
          >
            {renderOnlineTab()}
          </TabPane>
          <TabPane
            tab={<span><SettingOutlined />渠道配置</span>}
            key="config"
          >
            {renderConfigTab()}
          </TabPane>
        </Tabs>
      </Card>

      {/* 渠道表单模态框 */}
      <Modal
        title={editingChannel ? '编辑渠道' : '添加渠道'}
        open={modalVisible}
        onCancel={handleModalCancel}
        onOk={handleFormSubmit}
        width={800}
        destroyOnHidden
      >
        <Form
          form={form}
          layout="vertical"
          initialValues={{
            channel_type: 'offline',
            status: 'active',
            commission_rate: 0
          }}
        >
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="label"
                label="渠道名称"
                rules={[{ required: true, message: '请输入渠道名称' }]}
              >
                <Input placeholder="请输入渠道名称" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="channel_type"
                label="渠道类型"
                rules={[{ required: true, message: '请选择渠道类型' }]}
              >
                <Radio.Group>
                  <Radio value="offline">到店渠道</Radio>
                  <Radio value="online">到家渠道</Radio>
                  <Radio value="other">其他</Radio>
                </Radio.Group>
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="channel_category"
                label="渠道分类"
                rules={[{ required: true, message: '请输入渠道分类' }]}
              >
                <Select placeholder="请选择渠道分类">
                  <Option value="抖音团购">抖音团购</Option>
                  <Option value="美团团购">美团团购</Option>
                  <Option value="微信私域">微信私域</Option>
                  <Option value="美团闪购">美团闪购</Option>
                  <Option value="饿了么零售">饿了么零售</Option>
                  <Option value="京东外卖">京东外卖</Option>
                  <Option value="抖音随心团">抖音随心团</Option>
                  <Option value="其他">其他</Option>
                </Select>
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="store_id"
                label="关联门店"
              >
                <Select
                  placeholder="请选择关联门店"
                  allowClear
                  showSearch
                  optionFilterProp="children"
                  loading={storesLoading}
                >
                  {stores.map(store => (
                    <Option key={store.id} value={store.id}>{store.name}</Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="status"
                label="渠道状态"
                rules={[{ required: true, message: '请选择渠道状态' }]}
              >
                <Select placeholder="请选择渠道状态">
                  <Option value="active">活跃</Option>
                  <Option value="inactive">停用</Option>
                  <Option value="pending">待审核</Option>
                </Select>
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="commission_rate"
                label="佣金比例(%)"
              >
                <Input type="number" min={0} max={100} step={0.1} />
              </Form.Item>
            </Col>
          </Row>

          <Form.Item
            name="description"
            label="渠道描述"
          >
            <TextArea rows={4} placeholder="请输入渠道描述" />
          </Form.Item>

          <Divider>联系信息</Divider>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="contact_person"
                label="联系人"
              >
                <Input placeholder="请输入联系人姓名" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="contact_phone"
                label="联系电话"
              >
                <Input placeholder="请输入联系电话" />
              </Form.Item>
            </Col>
          </Row>

          <Divider>API配置</Divider>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="api_key"
                label="API Key"
              >
                <Input placeholder="请输入API Key" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="api_secret"
                label="API Secret"
              >
                <Input.Password placeholder="请输入API Secret" />
              </Form.Item>
            </Col>
          </Row>

          <Form.Item
            name="api_url"
            label="API URL"
          >
            <Input placeholder="请输入API URL" />
          </Form.Item>

          <Form.Item
            name="daily_order_limit"
            label="每日订单限制"
          >
            <Input type="number" min={0} placeholder="请输入每日订单限制数量" />
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default ChannelManagement;
