import React, { useState, useEffect } from 'react';
import {
  Card,
  Table,
  Button,
  Space,
  Tag,
  Input,
  InputNumber,
  DatePicker,
  Form,
  Select,
  Modal,
  message,
  Typography,
  Tooltip,
  Popconfirm,
  Row,
  Col,
  Statistic,
  Tabs,
  Upload,
  Divider,
  Progress,
  Spin,
  Alert,
  Empty
} from 'antd';
import {
  PlusOutlined,
  SearchOutlined,
  ReloadOutlined,
  EditOutlined,
  DeleteOutlined,
  EyeOutlined,
  CheckCircleOutlined,
  CloseCircleOutlined,
  DollarOutlined,
  FileTextOutlined,
  InboxOutlined,
  UploadOutlined,
  DownloadOutlined,
  SwapOutlined,
  StopOutlined,
  LoadingOutlined
} from '@ant-design/icons';
import { useNavigate } from 'react-router-dom';
import dayjs from "dayjs";
import debounce from 'lodash/debounce';
import apiService from '../../../services/api';
import { getProjectId } from '../../../services/api/httpClient';
import { usePurchaseOrderProcessing } from '../../../hooks/usePurchaseOrderProcessing';
import dateTimeUtils from '../../../utils/dateTimeUtils';
import DistributionOrderManagement from './DistributionOrderManagement';
import CreateDistributionForm from './CreateDistributionForm';
import FileSpaceSelector from '../../../components/FileSpaceSelector';

const { Title, Text, Paragraph } = Typography;
const { RangePicker } = DatePicker;
const { Option } = Select;
const { TabPane } = Tabs;
const { Dragger } = Upload;

// 订单状态映射
const orderStatusMap = {
  'draft': { color: 'default', text: '草稿' },
  'confirmed': { color: 'processing', text: '已确认' },
  'received': { color: 'success', text: '已收货' },
  'cancelled': { color: 'error', text: '已取消' }
};

// 支付状态映射
const paymentStatusMap = {
  'unpaid': { color: 'default', text: '未支付' },
  'partial': { color: 'warning', text: '部分支付' },
  'paid': { color: 'success', text: '已支付' }
};

const PurchaseOrderList = () => {
  const navigate = useNavigate();

  // 使用混合模式处理Hook
  const {
    processing,
    progress,
    status: processingStatus,
    processingMessage,
    result: processingResult,
    error: processingError,
    taskInfo,
    processAIPreview,
    confirmUpload: hybridConfirmUpload,
    cancel: cancelProcessing,
    reset: resetProcessing,
    isCompleted,
    isError,
    isSyncTrying,
    isFallbackToAsync,
    isAsyncProcessing
  } = usePurchaseOrderProcessing();

  // 状态定义
  const [activeTab, setActiveTab] = useState('orders');
  const [orders, setOrders] = useState([]);
  const [loading, setLoading] = useState(false);
  const [uploading, setUploading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 10,
    total: 0
  });
  const [searchParams, setSearchParams] = useState({
    status: '',
    payment_status: '',
    supplier_id: '',
    warehouse_id: '',
    dateRange: null,
    search: ''
  });
  const [stats, setStats] = useState({
    total: 0,
    draft: 0,
    confirmed: 0,
    received: 0,
    cancelled: 0,
    unpaid: 0,
    partial: 0,
    paid: 0
  });

  // 模态框相关状态
  const [deleteModalVisible, setDeleteModalVisible] = useState(false);
  const [statusModalVisible, setStatusModalVisible] = useState(false);
  const [paymentModalVisible, setPaymentModalVisible] = useState(false);
  const [currentOrder, setCurrentOrder] = useState(null);
  const [previewVisible, setPreviewVisible] = useState(false);
  const [previewData, setPreviewData] = useState(null);
  const [productModalVisible, setProductModalVisible] = useState(false);

  // 分拨单相关状态
  const [distributionModalVisible, setDistributionModalVisible] = useState(false);
  const [relatedArrivalsModalVisible, setRelatedArrivalsModalVisible] = useState(false);
  const [selectedPurchaseOrder, setSelectedPurchaseOrder] = useState(null);
  const [relatedArrivals, setRelatedArrivals] = useState([]);
  const [createDistributionModalVisible, setCreateDistributionModalVisible] = useState(false);

  // 上传相关状态
  const [fileList, setFileList] = useState([]);
  const [uploadType, setUploadType] = useState('both');
  const [distributionMode, setDistributionMode] = useState('direct');
  const [selectedWarehouse, setSelectedWarehouse] = useState(null);
  const [distributionItems, setDistributionItems] = useState([]);
  const [editedData, setEditedData] = useState({});
  const [editMode, setEditMode] = useState(false);

  // 下拉列表数据
  const [suppliers, setSuppliers] = useState([]);
  const [warehouses, setWarehouses] = useState([]);
  const [stores, setStores] = useState([]);
  const [products, setProducts] = useState([]);
  const [categories, setCategories] = useState([]);
  const [brands, setBrands] = useState([]);

  // 产品相关状态
  const [searchingProducts, setSearchingProducts] = useState(false);
  const [currentProductIndex, setCurrentProductIndex] = useState(null);
  const [currentProductItemIndex, setCurrentProductItemIndex] = useState(null);

  // 表单实例
  const [form] = Form.useForm();
  const [statusForm] = Form.useForm();
  const [paymentForm] = Form.useForm();
  const [distributionForm] = Form.useForm();
  const [productForm] = Form.useForm();

  // 监听混合模式处理结果
  useEffect(() => {
    if (isCompleted && processingResult) {
      console.log('混合模式处理完成，结果:', processingResult);
      
      // 设置预览数据
      setPreviewData(processingResult);
      setPreviewVisible(true);
      
      // 重置上传状态
      setUploading(false);
      setUploadProgress(0);
      
      // 显示成功消息
      message.success('智能识别完成！请确认数据后提交');
    }
  }, [isCompleted, processingResult]);

  // 监听处理错误
  useEffect(() => {
    if (isError && processingError) {
      console.log('混合模式处理出错:', processingError);
      
      // 重置上传状态
      setUploading(false);
      setUploadProgress(0);
      
      // 显示错误消息
      message.error('处理失败: ' + (processingError.message || '未知错误'));
    }
  }, [isError, processingError]);

  // 获取供应商列表
  const fetchSuppliers = async () => {
    try {
      const response = await apiService.project.supplier.getList();
      if (response && response.data) {
        setSuppliers(response.data);
      }
    } catch (error) {
      console.error('获取供应商列表失败:', error);
      message.error('获取供应商列表失败');
    }
  };

  // 获取仓库列表
  const fetchWarehouses = async () => {
    try {
      const response = await apiService.project.warehouse.getList();
      
      if (response && response.items) {
        setWarehouses(response.items);
      } else if (response && response.data) {
        setWarehouses(response.data);
      } else if (Array.isArray(response)) {
        setWarehouses(response);
      } else {
        setWarehouses([]);
      }
    } catch (error) {
      console.error('获取仓库列表失败:', error);
      message.error('获取仓库列表失败');
      setWarehouses([]);
    }
  };

  // 获取门店列表
  const fetchStores = async () => {
    try {
      const response = await apiService.project.store.getList();
      
      if (response && response.items) {
        setStores(response.items);
      } else if (response && response.data) {
        setStores(response.data);
      } else if (Array.isArray(response)) {
        setStores(response);
      } else {
        setStores([]);
      }
    } catch (error) {
      console.error('获取门店列表失败:', error);
      message.error('获取门店列表失败');
      setStores([]);
    }
  };

  // 获取采购订单列表
  const fetchOrders = async (params = {}) => {
    setLoading(true);
    try {
      const { current, pageSize } = pagination;
      const { status, payment_status, supplier_id, warehouse_id, dateRange, search } = searchParams;

      // 构建查询参数
      const queryParams = {
        skip: (current - 1) * pageSize,
        limit: pageSize,
        ...params
      };

      // 添加非空参数
      if (status) queryParams.status = status;
      if (payment_status) queryParams.payment_status = payment_status;
      if (supplier_id) queryParams.supplier_id = supplier_id;
      if (warehouse_id) queryParams.warehouse_id = warehouse_id;
      if (search) queryParams.search = search;

      // 添加日期范围
      if (dateRange && dateRange[0] && dateRange[1]) {
        queryParams.start_date = dateRange[0].format('YYYY-MM-DD');
        queryParams.end_date = dateRange[1].format('YYYY-MM-DD');
      }

      const response = await apiService.project.purchaseOrder.getList(queryParams);

      // 处理响应数据
      if (response) {
        setOrders(response.items || []);
        setPagination({
          ...pagination,
          current: response.page || 1,
          pageSize: response.size || 10,
          total: response.total || 0
        });
        updateStats(response.items || []);
      }
    } catch (error) {
      console.error('获取采购订单列表失败:', error);
      message.error('获取采购订单列表失败');
    } finally {
      setLoading(false);
    }
  };

  // 更新统计数据
  const updateStats = (orderList = []) => {
    const newStats = {
      total: orderList.length,
      draft: 0,
      confirmed: 0,
      received: 0,
      cancelled: 0,
      unpaid: 0,
      partial: 0,
      paid: 0
    };

    orderList.forEach(order => {
      // 订单状态统计
      if (order.status) {
        newStats[order.status] = (newStats[order.status] || 0) + 1;
      }

      // 支付状态统计
      if (order.payment_status) {
        newStats[order.payment_status] = (newStats[order.payment_status] || 0) + 1;
      }
    });

    setStats(newStats);
  };

  // 处理表格分页变化
  const handleTableChange = (pagination, filters, sorter) => {
    setPagination(pagination);
    fetchOrders({
      skip: (pagination.current - 1) * pagination.pageSize,
      limit: pagination.pageSize
    });
  };

  // 处理搜索表单提交
  const handleSearch = (values) => {
    // 更新搜索参数状态
    setSearchParams(values);
    setPagination({ ...pagination, current: 1 });

    // 发起请求，重置分页
    fetchOrders({ skip: 0 });
  };

  // 重置搜索表单
  const handleReset = () => {
    // 重置表单字段
    form.resetFields();

    // 设置默认搜索参数
    const defaultParams = {
      status: '',
      payment_status: '',
      supplier_id: '',
      warehouse_id: '',
      dateRange: null,
      search: ''
    };

    // 更新搜索参数状态
    setSearchParams(defaultParams);
    setPagination({ ...pagination, current: 1 });

    // 重新获取数据
    fetchOrders({ skip: 0 });
  };

  // 创建采购订单
  const handleCreate = () => {
    navigate('/project/purchase/orders/create');
  };

  // 查看采购订单详情
  const handleView = (id) => {
    navigate(`/project/purchase/orders/detail/${id}`);
  };

  // 编辑采购订单
  const handleEdit = (id) => {
    navigate(`/project/purchase/orders/edit/${id}`);
  };

  // 删除采购订单
  const handleDelete = async () => {
    if (!currentOrder) return;

    setLoading(true);
    try {
      await apiService.project.purchaseOrder.delete(currentOrder.id);
      message.success('采购订单删除成功');
      setDeleteModalVisible(false);
      setCurrentOrder(null);
      fetchOrders();
    } catch (error) {
      console.error('删除采购订单失败:', error);
      message.error('删除采购订单失败');
    } finally {
      setLoading(false);
    }
  };

  // 更新订单状态
  const handleUpdateStatus = async () => {
    if (!currentOrder) return;

    try {
      const values = await statusForm.validateFields();
      setLoading(true);

      await apiService.project.purchaseOrder.updateStatus(currentOrder.id, values);

      message.success('订单状态更新成功');
      setStatusModalVisible(false);
      setCurrentOrder(null);
      statusForm.resetFields();
      fetchOrders();
    } catch (error) {
      console.error('更新订单状态失败:', error);
      if (error.errorFields) {
        // 表单验证错误
        return;
      }
      message.error('更新订单状态失败');
    } finally {
      setLoading(false);
    }
  };

  // 更新支付状态
  const handleUpdatePayment = async () => {
    if (!currentOrder) return;

    try {
      const values = await paymentForm.validateFields();
      setLoading(true);

      await apiService.project.purchaseOrder.updatePayment(currentOrder.id, values);

      message.success('支付状态更新成功');
      setPaymentModalVisible(false);
      setCurrentOrder(null);
      paymentForm.resetFields();
      fetchOrders();
    } catch (error) {
      console.error('更新支付状态失败:', error);
      if (error.errorFields) {
        // 表单验证错误
        return;
      }
      message.error('更新支付状态失败');
    } finally {
      setLoading(false);
    }
  };

  // 添加分拨商品
  const handleAddDistributionItem = () => {
    const newItems = [...distributionItems];
    newItems.push({
      id: Date.now(), // 临时ID
      product_id: null,
      product_name: '',
      product_specification: '',
      product_unit: '',
      quantity: 0,
      unit_price: 0,
      supplier_id: null,
      destinations: [] // 目的地列表，包含 type, target_id, quantity
    });
    setDistributionItems(newItems);

    // 清空商品搜索结果
    setProducts([]);
  };

  // 移除分拨商品
  const handleRemoveDistributionItem = (itemId) => {
    const newItems = distributionItems.filter(item => item.id !== itemId);
    setDistributionItems(newItems);
  };

  // 更新分拨商品
  const handleUpdateDistributionItem = (itemId, field, value) => {
    const newItems = distributionItems.map(item => {
      if (item.id === itemId) {
        return { ...item, [field]: value };
      }
      return item;
    });
    setDistributionItems(newItems);
  };

  // 添加分拨目的地
  const handleAddDestination = (itemId) => {
    const newItems = distributionItems.map(item => {
      if (item.id === itemId) {
        const destinations = [...item.destinations];
        destinations.push({
          id: Date.now(), // 临时ID
          type: 'store',
          target_id: null,
          quantity: 0
        });
        return { ...item, destinations };
      }
      return item;
    });
    setDistributionItems(newItems);
  };

  // 移除分拨目的地
  const handleRemoveDestination = (itemId, destinationId) => {
    const newItems = distributionItems.map(item => {
      if (item.id === itemId) {
        const destinations = item.destinations.filter(dest => dest.id !== destinationId);
        return { ...item, destinations };
      }
      return item;
    });
    setDistributionItems(newItems);
  };

  // 更新分拨目的地
  const handleUpdateDestination = (itemId, destinationId, field, value) => {
    console.log('handleUpdateDestination调用:', { itemId, destinationId, field, value });
    
    const newItems = distributionItems.map(item => {
      if (item.id === itemId) {
        const destinations = item.destinations.map(dest => {
          if (dest.id === destinationId) {
            const updatedDest = { ...dest, [field]: value };
            
            // 如果更新的是target_id，添加时间戳强制重新渲染
            if (field === 'target_id') {
              updatedDest.lastUpdate = Date.now();
            }
            
            console.log('更新目的地:', { 原始: dest, 更新后: updatedDest });
            return updatedDest;
          }
          return dest;
        });
        return { ...item, destinations };
      }
      return item;
    });
    
    console.log('分拨商品列表更新:', newItems);
    setDistributionItems(newItems);
  };

  // 更新预览数据中的分拨目标
  const handleUpdateDistributionDestination = (index, field, value) => {
    if (!previewData || !previewData.distribution_destinations) return;

    const newPreviewData = JSON.parse(JSON.stringify(previewData)); // Deep copy
    
    if (newPreviewData.distribution_destinations[index]) {
      if (field === 'target_store_id') {
        // If it's target_store_id, we also update target_store_name
        const selectedStore = stores.find(store => store.id === value);
        newPreviewData.distribution_destinations[index]['target_store_id'] = value;
        newPreviewData.distribution_destinations[index]['target_store_name'] = selectedStore ? selectedStore.name : '未知门店';
        newPreviewData.distribution_destinations[index]['is_confirmed'] = false; // Reset confirmation status
      } else {
        newPreviewData.distribution_destinations[index][field] = value;
      }
      newPreviewData.lastUpdate = Date.now();
      setPreviewData(newPreviewData); // Directly set the new deep-copied object
    }
  };

  const handleConfirmDistributionTarget = (index, targetStoreId) => {
    if (!previewData || !previewData.distribution_destinations) return;
    
    const newPreviewData = JSON.parse(JSON.stringify(previewData)); // Deep copy
    const dest = newPreviewData.distribution_destinations[index];

    if (dest) {
      if (targetStoreId) {
        const selectedStore = stores.find(store => store.id === targetStoreId);
        dest.target_store_id = targetStoreId;
        dest.target_store_name = selectedStore ? selectedStore.name : '未知门店';
        dest.is_confirmed = true;
        dest.recognition_status = 'confirmed_manual'; 
      } else {
        // This case might mean "confirm as is" if AI suggested something, or "confirm as unmapped" if nothing was suggested
        // For now, let's assume if targetStoreId is null/undefined, it's an explicit action to mark as confirmed without a specific store (if allowed by business logic)
        // Or, it might be that a targetStoreId was already set (e.g., by AI or previous edit) and we are just confirming it.
        if (dest.target_store_id) { // If a store is already selected (by AI or edit)
            dest.is_confirmed = true;
            dest.recognition_status = dest.recognition_status === 'recognized_auto' ? 'confirmed_auto' : 'confirmed_manual';
        } else { // If no store is selected, mark as confirmed_unmapped
            dest.is_confirmed = true;
            dest.target_store_name = '不指定门店'; // Or some other placeholder
            dest.recognition_status = 'confirmed_unmapped';
        }
      }
      newPreviewData.lastUpdate = Date.now(); // Ensure this is set before setPreviewData
          setPreviewData(newPreviewData);
    }
  };

  // 编辑分拨目标
  const handleEditDistributionTarget = (index) => {
    // 自动进入编辑模式，并滚动到对应的选择器
    if (!editMode) {
      setEditMode(true);
    }
    
    // 需要找到对应的分拨目的地在编辑模式中的位置
    // 由于编辑模式和预览模式的数据结构不同，需要特殊处理
    
    message.info('已进入编辑模式，请在下方分拨商品中选择门店');
    
    // 延迟滚动到分拨商品区域并高亮显示
    setTimeout(() => {
      // 滚动到分拨商品区域
      const distributionItemsSection = document.querySelector('.distribution-items-section');
      if (distributionItemsSection) {
        distributionItemsSection.scrollIntoView({ behavior: 'smooth', block: 'start' });
        
        // 高亮提示所有未选择的门店选择器
        const unselectedSelectors = document.querySelectorAll('[data-destination-index]');
        unselectedSelectors.forEach(selector => {
          selector.style.border = '2px solid #ff7875';
          selector.style.borderRadius = '4px';
          selector.style.padding = '4px';
        });
        
        // 3秒后移除高亮
        setTimeout(() => {
          unselectedSelectors.forEach(selector => {
            selector.style.border = '';
            selector.style.borderRadius = '';
            selector.style.padding = '';
          });
        }, 3000);
      }
    }, 100);
  };

  // 提交确认预览数据
  const handleConfirmPreview = async () => {
    if (!previewData) return;

    try {
      // 解析实际数据
      let actualData = null;
      
      if (previewData.result && previewData.result.data) {
        actualData = previewData.result.data.preview || previewData.result.data;
      } else if (previewData.data) {
        actualData = previewData.data.preview || previewData.data;
      } else {
        actualData = previewData;
      }
      
      console.log('handleConfirmPreview 解析的实际数据:', actualData);  // 调试日志
      
      // 检查是否有有效的分拨数据
      const hasValidDistributionData = actualData?.distribution_destinations && 
        actualData.distribution_destinations.length > 0 &&
        actualData.distribution_destinations.some(dest => dest.target_id);
      
      // 检查是否有采购数据
      const hasPurchaseData = actualData?.purchase_items && actualData.purchase_items.length > 0;
      
      // 根据上传类型和数据情况决定创建模式
      let creationMode = 'purchase_only'; // 默认只创建采购单
      
      if (uploadType === 'purchase') {
        // 用户指定只上传采购单
        creationMode = 'purchase_only';
        
        // 验证采购数据
        if (!hasPurchaseData) {
          message.error('未识别到有效的采购数据，无法创建采购单');
          return;
        }
      } else if (uploadType === 'distribution') {
        // 用户指定只上传分拨单，但需要有关联的采购单
        if (!hasValidDistributionData) {
          message.error('未识别到有效的分拨数据，无法创建分拨单');
          return;
        }
        
        // 对于仅分拨单，还需要检查是否有基础的商品信息
        if (!hasPurchaseData && (!actualData.purchase_order_id)) {
          message.error('分拨单需要关联采购订单或包含商品信息');
          return;
        }
        
        creationMode = 'distribution_only';
      } else if (uploadType === 'both') {
        // 用户期望同时创建，但要检查分拨数据的有效性
        if (!hasPurchaseData) {
          message.error('未识别到有效的采购数据，无法创建订单');
          return;
        }
        
        if (hasValidDistributionData) {
          creationMode = 'purchase_and_distribution';
        } else {
          // 分拨数据无效或缺失，提示用户选择
          Modal.confirm({
            title: '分拨数据检测',
            content: (
              <div>
                <p>识别到采购数据，但未检测到有效的分拨数据或分拨目标未完全确认。</p>
                <p>您可以选择：</p>
                <ul>
                  <li><strong>仅创建采购单</strong>：后续可在采购单管理中手动创建分拨单</li>
                  <li><strong>取消操作</strong>：返回完善分拨数据</li>
                </ul>
              </div>
            ),
            okText: '仅创建采购单',
            cancelText: '取消操作',
            onOk: () => {
              // 用户选择仅创建采购单
              handleConfirmPreviewWithMode('purchase_only');
            },
            onCancel: () => {
              // 用户选择取消，不做任何操作
              return;
            }
          });
          return; // 等待用户选择
        }
      }
      
      // 执行创建操作
      await handleConfirmPreviewWithMode(creationMode);
      
    } catch (error) {
      console.error('确认预览失败:', error);
      message.error('确认预览失败: ' + (error.response?.data?.detail || error.message));
    }
  };

  // 根据指定模式执行确认预览
  const handleConfirmPreviewWithMode = async (mode) => {
    try {
      setLoading(true);
      
      // 解析实际数据
      let actualData = null;
      
      if (previewData.result && previewData.result.data) {
        actualData = previewData.result.data.preview || previewData.result.data;
      } else if (previewData.data) {
        actualData = previewData.data.preview || previewData.data;
      } else {
        actualData = previewData;
      }
      
      // 使用编辑后的数据（如果有的话）或原始数据
      const finalData = editedData || actualData;
      
      // 根据创建模式验证必填项
      const validationErrors = [];
      
      if (mode !== 'distribution_only') {
        // 需要创建采购单时的验证
        if (!finalData?.purchase_date) {
          validationErrors.push('采购日期');
        }
        
        if (!finalData?.purchase_items || finalData.purchase_items.length === 0) {
          validationErrors.push('采购商品信息');
        }
      }
      
      if (mode === 'purchase_and_distribution' || mode === 'distribution_only') {
        // 需要创建分拨单时的验证
        if (!finalData?.distribution_date) {
          validationErrors.push('分拨日期');
        }
        
        if (finalData?.distribution_destinations) {
          const unconfirmedTargets = finalData.distribution_destinations.filter(dest => !dest.target_id);
          if (unconfirmedTargets.length > 0) {
            validationErrors.push(`${unconfirmedTargets.length}个分拨目标未选择具体门店/仓库`);
          }
        }
      }
      
      if (validationErrors.length > 0) {
        Modal.error({
          title: '提交前检查',
          content: (
            <div>
              <p>请完善以下必填信息后再提交：</p>
              <ul>
                {validationErrors.map((error, index) => (
                  <li key={index} style={{ color: '#ff4d4f' }}>{error}</li>
                ))}
              </ul>
              <p style={{ marginTop: 16, color: '#666' }}>
                提示：点击右上角的"编辑数据"按钮可以修改这些信息
              </p>
            </div>
          ),
          width: 500
        });
        return;
      }
      
      console.log('发送到confirmPreview的最终数据:', finalData);  // 调试日志
      console.log('创建模式:', mode);  // 调试日志
      
      // 查找任务ID，兼容不同的数据结构
      let taskId = null;
      if (previewData.task_id) {
        taskId = previewData.task_id;
      } else if (previewData.data?.task_id) {
        taskId = previewData.data.task_id;
      } else if (previewData.result?.data?.task_id) {
        taskId = previewData.result.data.task_id;
      }
      
      if (!taskId) {
        message.error('无法获取任务ID，请重新处理');
        return;
      }
      
      // 添加创建模式到最终数据
      const finalDataWithMode = {
        ...finalData,
        creation_mode: mode,
        upload_type: uploadType
      };
      
      await apiService.project.purchaseOrder.confirmPreview(taskId, finalDataWithMode);
      
      // 根据创建模式显示不同的成功消息
      if (mode === 'purchase_only') {
        message.success('采购单创建成功！您可以在采购单管理中进行后续的分拨或入库操作。');
      } else if (mode === 'distribution_only') {
        message.success('分拨单创建成功！');
      } else {
        message.success('采购分拨单创建成功！');
      }
      
      setPreviewVisible(false);
      setPreviewData(null);
      setEditedData(null);
      setEditMode(false);
      
      // 刷新订单列表
      fetchOrders();
    } catch (error) {
      console.error('确认预览失败:', error);
      message.error('确认预览失败: ' + (error.response?.data?.detail || error.message));
    } finally {
      setLoading(false);
    }
  };

  // 自动上传（智能处理）
  const handleAutoUpload = async (file) => {
    if (!file) {
      message.warning('请选择要上传的文件');
      return;
    }

    setUploading(true);
    setUploadProgress(0);

    try {
      // 第一步：上传文件
      const uploadResponse = await apiService.project.space.uploadFile(
        file,
        '/purchase-distribution',
        '采购分拨单文件',
        false,
        (percent) => {
          setUploadProgress(percent);
        }
      );

      if (!uploadResponse || !uploadResponse.data) {
        throw new Error('文件上传失败');
      }

      console.log('文件上传成功:', uploadResponse.data);
      const uploadResult = uploadResponse;

      // 第二步：使用混合模式处理AI预览
      console.log('开始混合模式处理，参数:', {
        fileId: uploadResult.data.id,
        uploadType: uploadType,
        warehouseId: uploadType !== 'distribution' && distributionMode === 'partial' ? selectedWarehouse : undefined,
        distributionMode: uploadType !== 'distribution' ? distributionMode : undefined,
        distributionItems: uploadType !== 'purchase' ? JSON.stringify(distributionItems) : '[]',
        processingMode: 'auto'
      });

      // 使用混合模式处理
      await processAIPreview(
        uploadResult.data.id,           // fileId
        uploadType,                     // uploadType  
        uploadType !== 'distribution' && distributionMode === 'partial' ? selectedWarehouse : undefined, // warehouseId
        uploadType !== 'distribution' ? distributionMode : undefined, // distributionMode
        uploadType !== 'purchase' ? JSON.stringify(distributionItems) : '[]', // distributionItems
        'auto'                          // processingMode
      );

    } catch (error) {
      console.error('上传失败:', error);
      console.error('错误详情:', {
        name: error.name,
        message: error.message,
        stack: error.stack,
        response: error.response?.data
      });
      
      let errorMessage = '上传失败: ';
      if (error.response?.data?.detail) {
        errorMessage += error.response.data.detail;
      } else if (error.response?.data?.message) {
        errorMessage += error.response.data.message;
      } else if (error.message) {
        errorMessage += error.message;
      } else {
        errorMessage += '未知错误';
      }
      
      message.error(errorMessage);
      
      // 确保在错误时重置上传状态
      setUploading(false);
      setUploadProgress(0);
      
      // 更新文件状态
      if (fileList.length > 0) {
        setFileList(prev => prev.map(file => ({
          ...file,
          status: 'error'
        })));
      }
    }
  };

  // 手动上传（备用方案）
  const handleManualUpload = async () => {
    if (fileList.length === 0) {
      message.warning('请选择要上传的文件');
      return;
    }

    const file = fileList[0].originFileObj || fileList[0];
    await handleAutoUpload(file);
  };

  // 更新编辑数据
  const updatePurchaseItem = (index, field, value) => {
    const newData = { ...editedData };
    if (!newData.purchase_items) newData.purchase_items = [];
    if (!newData.purchase_items[index]) newData.purchase_items[index] = {};
    
    newData.purchase_items[index][field] = value;
    
    // 自动计算总金额
    if (field === 'quantity' || field === 'unit_price') {
      const quantity = parseFloat(newData.purchase_items[index].quantity || 0);
      const unitPrice = parseFloat(newData.purchase_items[index].unit_price || 0);
      newData.purchase_items[index].total_amount = quantity * unitPrice;
    }
    
    setEditedData(newData);
  };

  // 添加供应商选择
  const addSupplierToPurchaseItem = (index, supplierId) => {
    updatePurchaseItem(index, 'supplier_id', supplierId);
  };

  // 删除采购项
  const removePurchaseItem = (index) => {
    const newData = { ...editedData };
    newData.purchase_items = newData.purchase_items.filter((_, i) => i !== index);
    setEditedData(newData);
  };

  // 添加新的采购项
  const addPurchaseItem = () => {
    const newData = { ...editedData };
    if (!newData.purchase_items) newData.purchase_items = [];
    newData.purchase_items.push({
      product_name: '',
      product_code: '',
      product_unit: '',
      product_specification: '',
      quantity: 0,
      unit_price: 0,
      total_amount: 0,
      supplier_id: null,
      category_id: null,
      notes: ''
    });
    setEditedData(newData);
  };

  const handleConfirmUpload = async () => {
    if (!previewData) {
      message.warning('没有预览数据，请先预览');
      return;
    }

    console.log('确认上传，预览数据:', previewData); // 调试日志

    setUploading(true);

    try {
      // 智能提取file_id - 兼容不同的数据结构
      let fileId = null;
      
      if (previewData.file_id) {
        fileId = previewData.file_id;
      } else if (previewData.data && previewData.data.file_id) {
        fileId = previewData.data.file_id;
      } else if (previewData.result && previewData.result.data && previewData.result.data.file_id) {
        fileId = previewData.result.data.file_id;
      }

      console.log('提取的file_id:', fileId); // 调试日志

      if (!fileId) {
        message.error('无法获取文件ID，请重新上传');
        return;
      }

      // 提取编辑后的预览数据
      let editedPreviewData = null;
      if (previewData.result && previewData.result.data) {
        if (previewData.result.data.preview) {
          editedPreviewData = previewData.result.data.preview;
        } else {
          editedPreviewData = previewData.result.data;
        }
      } else if (previewData.data) {
        if (previewData.data.preview) {
          editedPreviewData = previewData.data.preview;
        } else {
          editedPreviewData = previewData.data;
        }
      } else {
        editedPreviewData = previewData;
      }

      const confirmParams = {
        file_id: fileId,
        upload_type: uploadType,
        warehouse_id: uploadType !== 'distribution' && distributionMode === 'partial' ? selectedWarehouse : undefined,
        distribution_mode: uploadType !== 'distribution' ? distributionMode : undefined,
        distribution_items: uploadType !== 'purchase' ? JSON.stringify(distributionItems) : undefined,
        preview_data: editedPreviewData ? JSON.stringify(editedPreviewData) : undefined  // 传递编辑后的数据
      };

      console.log('确认上传参数:', confirmParams); // 调试日志

      const response = await apiService.project.purchaseOrder.confirmUpload(confirmParams);

      if (response && response.success) {
        message.success(response.message || '上传成功');
        setFileList([]);
        setPreviewData(null);
        setPreviewVisible(false);

        // 重置表单
        distributionForm.resetFields();
        setSelectedWarehouse(null);
        setDistributionMode('direct');
        setDistributionItems([]);
        setUploadType('both');

        // 刷新订单列表
        fetchOrders();
      } else {
        message.error(response?.message || '上传失败');
      }
    } catch (error) {
      console.error('上传失败:', error);
      message.error('上传失败: ' + (error.response?.data?.detail || error.message));
    } finally {
      setUploading(false);
    }
  };

  // 下载模板
  const handleDownloadTemplate = async () => {
    try {
      const response = await apiService.project.purchaseOrder.downloadTemplate(uploadType);

      // 创建一个下载链接
      const url = window.URL.createObjectURL(new Blob([response]));
      const link = document.createElement('a');
      link.href = url;
      link.setAttribute('download', `${uploadType === 'both' ? '采购分拨' : uploadType === 'purchase' ? '采购' : '分拨'}单模板.xlsx`);
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    } catch (error) {
      console.error('下载模板失败:', error);
      message.error('下载模板失败');
    }
  };

  // 处理项目空间文件选择
  const handleSpaceFileSelect = async (selectedFile) => {
    if (!selectedFile) {
      message.warning('请选择一个文件');
      return;
    }

    // 检查文件类型
    const allowedExtensions = ['.xlsx', '.xls', '.jpg', '.jpeg', '.png'];
    const fileExtension = selectedFile.name.toLowerCase().substring(selectedFile.name.lastIndexOf('.'));
    
    if (!allowedExtensions.includes(fileExtension)) {
      message.error('不支持的文件类型，请选择图片或Excel文件');
      return;
    }

    setUploading(true);
    setUploadProgress(0);

    try {
      // 使用项目空间的文件ID直接进行混合模式处理
      console.log('开始处理项目空间文件，参数:', {
        fileId: selectedFile.id,
        uploadType: uploadType,
        warehouseId: uploadType !== 'distribution' && distributionMode === 'partial' ? selectedWarehouse : undefined,
        distributionMode: uploadType !== 'distribution' ? distributionMode : undefined,
        distributionItems: uploadType !== 'purchase' ? JSON.stringify(distributionItems) : '[]',
        processingMode: 'auto'
      });

      // 开始混合模式处理
      await processAIPreview(
        selectedFile.id,
        uploadType,
        uploadType !== 'distribution' && distributionMode === 'partial' ? selectedWarehouse : undefined,
        uploadType !== 'distribution' ? distributionMode : undefined,
        uploadType !== 'purchase' ? JSON.stringify(distributionItems) : '[]',
        'auto'
      );

      message.success(`已选择文件：${selectedFile.name}，开始智能处理...`);
    } catch (error) {
      console.error('处理项目空间文件失败:', error);
      message.error('处理文件失败: ' + error.message);
      setUploading(false);
      setUploadProgress(0);
    }
  };

  // 初始化
  useEffect(() => {
    // 初始化时区工具
    dateTimeUtils.initDateTimeUtils();
    
    // 添加全局错误监听器
    const handleGlobalError = (event) => {
      console.error('全局错误捕获:', event.error);
      if (event.error && event.error.message && event.error.message.includes('转换AI结果格式失败')) {
        message.error('AI识别失败：数据处理出错，请重试', 5);
      }
    };
    
    const handleUnhandledRejection = (event) => {
      console.error('未处理的Promise拒绝:', event.reason);
      if (event.reason && typeof event.reason === 'object') {
        if (event.reason.message && event.reason.message.includes('转换AI结果格式失败')) {
          message.error('AI识别失败：数据处理出错，请重试', 5);
        }
      }
    };
    
    window.addEventListener('error', handleGlobalError);
    window.addEventListener('unhandledrejection', handleUnhandledRejection);
    
    fetchSuppliers();
    fetchWarehouses();
    fetchStores();
    fetchOrders();
    fetchCategories();
    fetchBrands();
    
    // 清理函数
    return () => {
      window.removeEventListener('error', handleGlobalError);
      window.removeEventListener('unhandledrejection', handleUnhandledRejection);
    };
  }, []);

  // 获取商品分类
  const fetchCategories = async () => {
    try {
      const response = await apiService.project.product.getCategories();
      if (response && response.success) {
        setCategories(response.data || []);
      } else {
        message.error(response?.message || '获取分类列表失败');
      }
    } catch (error) {
      console.error('获取分类失败:', error);
      message.error('获取分类列表失败: ' + (error.response?.data?.message || '服务器错误'));
    }
  };

  // 获取品牌列表
  const fetchBrands = async () => {
    try {
      const response = await apiService.project.product.getBrands();
      if (response && response.success) {
        setBrands(response.data || []);
      } else {
        message.error(response?.message || '获取品牌列表失败');
      }
    } catch (error) {
      console.error('获取品牌失败:', error);
      message.error('获取品牌列表失败: ' + (error.response?.data?.message || '服务器错误'));
    }
  };

  // 搜索商品 - 使用debounce延迟搜索
  const searchProducts = debounce(async (value) => {
    if (!value || value.length < 2) {
      // 清空搜索结果但不显示错误
      setProducts([]);
      setSearchingProducts(false);
      return;
    }

    setSearchingProducts(true);
    try {
      const response = await apiService.project.product.getList({
        search: value,
        limit: 20
      });

      console.log('搜索商品返回数据:', response); // 调试日志

      // 检查响应结构，处理不同的数据格式
      let productItems = [];
      if (response && response.items) {
        productItems = response.items;
      } else if (response && response.data) {
        // 处理数据在data字段的情况
        productItems = response.data;
      } else if (Array.isArray(response)) {
        // 处理直接返回数组的情况
        productItems = response;
      }

      if (productItems && productItems.length > 0) {
        // 确保返回的数据格式正确
        const formattedProducts = productItems.map(item => ({
          id: item.id,
          name: item.name,
          specification: item.specification || '',
          unit: item.unit || '', // 使用API返回的单位字段
          retail_price: item.retail_price || 0,
          cost_price: item.cost_price || 0,
          // 新增多规格字段
          is_variant: item.is_variant || false,
          product_group_id: item.product_group_id,
          display_name: item.display_name || `${item.name}${item.specification ? ` (${item.specification})` : ''}`
        }));
        setProducts(formattedProducts);
        console.log('格式化后的商品:', formattedProducts); // 调试日志
      } else {
        console.log('没有找到匹配的商品');
        setProducts([]); // 确保在没有结果时清空列表
      }
    } catch (error) {
      console.error('搜索商品失败:', error);
      setProducts([]); // 确保在错误时清空列表
    } finally {
      setSearchingProducts(false);
    }
  }, 500); // 500ms延迟，避免频繁请求

  // 处理商品选择
  const handleProductSelect = (value, option, index) => {
    if (!value) {
      console.log('未选择商品，value为空');
      return;
    }

    console.log('选择的商品ID:', value);
    console.log('选择的商品选项:', option);
    console.log('当前商品列表:', products);

    // 查找选中的商品
    const selectedProduct = products.find(p => p.id === value);
    if (!selectedProduct) {
      console.warn('未找到选中的商品:', value);

      // 尝试从option中获取商品信息
      if (option && option.label) {
        console.log('尝试从option中获取商品信息:', option);

        // 更新分拨商品信息，使用option中的数据
        const newItems = [...distributionItems];
        newItems[index] = {
          ...newItems[index],
          product_id: value,
          product_name: option.label || '',
          product_specification: '',
          product_unit: '',
          unit_price: 0
        };

        setDistributionItems(newItems);
        return;
      }

      return;
    }

    console.log('找到选中的商品:', selectedProduct);

    // 更新分拨商品信息
    const newItems = [...distributionItems];
    newItems[index] = {
      ...newItems[index],
      product_id: selectedProduct.id,
      product_name: selectedProduct.name,
      product_specification: selectedProduct.specification || '',
      product_unit: selectedProduct.unit || '',
      unit_price: selectedProduct.cost_price || selectedProduct.retail_price || 0
    };

    setDistributionItems(newItems);

    // 清空商品列表，避免干扰下一次选择
    // setProducts([]);
  };

  // 处理分拨操作
  const handleDistributeClick = async (record) => {
    try {
      setLoading(true);

      // 获取采购单详情
      const response = await apiService.project.purchaseOrder.getDetail(record.id);

      if (response) {
        // 切换到分拨管理标签页
        setActiveTab('distribution');

        // 设置上传类型为仅分拨
        setUploadType('distribution');
        distributionForm.setFieldsValue({ upload_type: 'distribution' });

        // 清空现有分拨商品
        setDistributionItems([]);

        // 将采购单商品添加到分拨商品列表
        const items = response.items || [];
        const newItems = items.map(item => ({
          id: Date.now() + Math.random(), // 临时ID
          product_id: item.product_id,
          product_name: item.product_name,
          product_specification: item.product_specification,
          product_unit: item.product_unit,
          quantity: item.quantity - (item.received_quantity || 0), // 剩余可分拨数量
          unit_price: item.unit_price,
          supplier_id: item.supplier_id, // 现在供应商在商品级别
          destinations: [] // 目的地列表，包含 type, target_id, quantity
        }));

        setDistributionItems(newItems);

        // 滚动到分拨商品区域
        setTimeout(() => {
          const element = document.querySelector('.purchase-order-list-page .ant-tabs-tabpane-active');
          if (element) {
            element.scrollIntoView({ behavior: 'smooth', block: 'start' });
          }
        }, 100);

        message.success(`已加载采购单 ${record.order_number} 的商品，请设置分拨目的地`);
      } else {
        message.error('获取采购单详情失败');
      }
    } catch (error) {
      console.error('获取采购单详情失败:', error);
      message.error('获取采购单详情失败: ' + (error.response?.data?.detail || error.message));
    } finally {
      setLoading(false);
    }
  };

  // 打开新增商品模态框
  const handleAddProduct = (index) => {
    setCurrentProductItemIndex(index);
    productForm.resetFields();
    productForm.setFieldsValue({ unit: '个' });
    setProductModalVisible(true);
  };

  // 处理新增商品表单提交
  const handleProductFormSubmit = async () => {
    try {
      const values = await productForm.validateFields();
      setLoading(true);

      // 创建产品
      const response = await apiService.project.product.create(values);

      if (response && response.success) {
        message.success('产品创建成功');
        setProductModalVisible(false);

        // 将新创建的商品添加到分拨商品中
        if (currentProductItemIndex !== null && response.data) {
          const newProduct = response.data;
          const newItems = [...distributionItems];
          newItems[currentProductItemIndex] = {
            ...newItems[currentProductItemIndex],
            product_id: newProduct.id,
            product_name: newProduct.name,
            product_specification: newProduct.specification || '',
            product_unit: newProduct.unit || '',
            unit_price: newProduct.retail_price || 0
          };

          setDistributionItems(newItems);
        }

        // 重置状态
        setCurrentProductItemIndex(null);
        productForm.resetFields();
      } else {
        message.error(response?.message || '创建产品失败');
      }
    } catch (error) {
      console.error('创建产品失败:', error);
      if (error.errorFields) {
        // 表单验证错误
        return;
      }
      message.error('创建产品失败: ' + (error.response?.data?.message || '服务器错误'));
    } finally {
      setLoading(false);
    }
  };

  // 渲染预览模态框
  const renderPreviewModal = () => {
    if (!previewData) return null;

    console.log('预览数据结构:', previewData); // 调试日志

    // 根据实际的后端数据结构解析
    let actualData = null;
    let processingInfo = null;
    let validationResults = null;

    if (previewData.result && previewData.result.data) {
      // 标准的异步任务结果格式
      if (previewData.result.data.preview) {
        actualData = previewData.result.data.preview;
      } else {
        actualData = previewData.result.data;
      }
      processingInfo = previewData.result.data.processing_info;
      validationResults = previewData.result.data.validation_results;
    } else if (previewData.data) {
      // 直接的数据格式
      if (previewData.data.preview) {
        actualData = previewData.data.preview;
      } else {
        actualData = previewData.data;
      }
      processingInfo = previewData.data.processing_info;
      validationResults = previewData.data.validation_results;
    } else {
      // 顶层数据格式
      actualData = previewData;
      processingInfo = previewData.processing_info;
      validationResults = previewData.validation_results;
    }

    console.log('解析后的实际数据:', actualData); // 调试日志

    const hasPurchaseData = actualData && actualData.purchase_items && actualData.purchase_items.length > 0;
    const hasDistributionData = actualData && actualData.distribution_destinations && actualData.distribution_destinations.length > 0;

    // 检查是否有有效的分拨数据
    const hasValidDistributionData = hasDistributionData && 
      actualData.distribution_destinations.some(dest => dest.target_id);

    // 确定创建模式
    let creationMode = 'purchase_only';
    let confirmButtonText = '创建采购单';
    let modeDescription = '';
    let buttonDisabled = false;

    if (uploadType === 'purchase') {
      creationMode = 'purchase_only';
      confirmButtonText = '创建采购单';
      modeDescription = '将创建采购订单，后续可在采购单管理中进行分拨或入库操作';
      buttonDisabled = !hasPurchaseData;
    } else if (uploadType === 'distribution') {
      if (hasValidDistributionData) {
        creationMode = 'distribution_only';
        confirmButtonText = '创建分拨单';
        modeDescription = '将创建分拨单';
        buttonDisabled = false;
      } else {
        confirmButtonText = '数据不完整';
        modeDescription = '分拨数据不完整，无法创建分拨单';
        buttonDisabled = true;
      }
    } else if (uploadType === 'both') {
      if (!hasPurchaseData) {
        confirmButtonText = '数据不完整';
        modeDescription = '缺少采购数据，无法创建订单';
        buttonDisabled = true;
      } else if (hasValidDistributionData) {
        creationMode = 'purchase_and_distribution';
        confirmButtonText = '创建采购分拨单';
        modeDescription = '将同时创建采购订单和分拨单';
        buttonDisabled = false;
      } else {
        confirmButtonText = '智能创建';
        modeDescription = '将创建采购订单，点击确认时您可选择是否同时创建分拨单';
        buttonDisabled = false;
      }
    }

    // 初始化编辑数据
    if (!editedData && actualData) {
      setEditedData(actualData);
    }

    // 采购商品表格列定义
    const purchaseColumns = [
      {
        title: '商品名称',
        dataIndex: 'product_name',
        key: 'product_name',
        render: (text, record, index) => editMode ? (
          <Input
            value={text}
            onChange={(e) => updatePurchaseItem(index, 'product_name', e.target.value)}
            placeholder="请输入商品名称"
          />
        ) : text
      },
      {
        title: '商品编码',
        dataIndex: 'product_code',
        key: 'product_code',
        render: (text, record, index) => editMode ? (
          <Input
            value={text}
            onChange={(e) => updatePurchaseItem(index, 'product_code', e.target.value)}
            placeholder="商品编码"
          />
        ) : text
      },
      {
        title: '供应商',
        dataIndex: 'supplier_id',
        key: 'supplier_id',
        render: (supplierId, record, index) => editMode ? (
          <Select
            value={supplierId}
            onChange={(value) => updatePurchaseItem(index, 'supplier_id', value)}
            placeholder="选择供应商"
            allowClear
            style={{ width: '100%' }}
          >
            {suppliers.map(supplier => (
              <Option key={supplier.id} value={supplier.id}>
                {supplier.name}
              </Option>
            ))}
          </Select>
        ) : (
          suppliers.find(s => s.id === supplierId)?.name || '-'
        )
      },
      {
        title: '商品分类',
        dataIndex: 'category_id',
        key: 'category_id',
        render: (categoryId, record, index) => editMode ? (
          <Select
            value={categoryId}
            onChange={(value) => updatePurchaseItem(index, 'category_id', value)}
            placeholder="选择分类"
            allowClear
            style={{ width: '100%' }}
          >
            {categories.map(category => (
              <Option key={category.id} value={category.id}>
                {category.name}
              </Option>
            ))}
          </Select>
        ) : (
          categories.find(c => c.id === categoryId)?.name || '-'
        )
      },
      {
        title: '规格',
        dataIndex: 'product_specification',
        key: 'product_specification',
        render: (text, record, index) => editMode ? (
          <Input
            value={text}
            onChange={(e) => updatePurchaseItem(index, 'product_specification', e.target.value)}
            placeholder="规格"
          />
        ) : text
      },
      {
        title: '单位',
        dataIndex: 'product_unit',
        key: 'product_unit',
        render: (text, record, index) => editMode ? (
          <Input
            value={text}
            onChange={(e) => updatePurchaseItem(index, 'product_unit', e.target.value)}
            placeholder="单位"
          />
        ) : text
      },
      {
        title: '数量',
        dataIndex: 'quantity',
        key: 'quantity',
        render: (text, record, index) => editMode ? (
          <InputNumber
            value={text}
            onChange={(value) => updatePurchaseItem(index, 'quantity', value)}
            placeholder="数量"
            min={0}
            style={{ width: '100%' }}
          />
        ) : text
      },
      {
        title: '单价',
        dataIndex: 'unit_price',
        key: 'unit_price',
        render: (text, record, index) => editMode ? (
          <InputNumber
            value={text}
            onChange={(value) => updatePurchaseItem(index, 'unit_price', value)}
            placeholder="单价"
            min={0}
            precision={2}
            style={{ width: '100%' }}
          />
        ) : `¥${text}`
      },
      {
        title: '总金额',
        dataIndex: 'total_amount',
        key: 'total_amount',
        render: (text, record) => {
          const amount = (record.quantity || 0) * (record.unit_price || 0);
          return `¥${amount.toFixed(2)}`;
        }
      }
    ];

    if (editMode) {
      purchaseColumns.push({
        title: '操作',
        key: 'action',
        render: (text, record, index) => (
          <Button
            type="link"
            danger
            onClick={() => removePurchaseItem(index)}
            icon={<DeleteOutlined />}
          >
            删除
          </Button>
        )
      });
    }

    return (
      <Modal
        title={
          <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            <span>数据预览</span>
            <Space>
              {editMode ? (
                <>
                  <Button onClick={() => setEditMode(false)}>取消编辑</Button>
                  <Button type="primary" onClick={() => setEditMode(false)}>保存编辑</Button>
                </>
              ) : (
                <Button type="primary" onClick={() => setEditMode(true)}>编辑数据</Button>
              )}
            </Space>
          </div>
        }
        open={previewVisible}
        onCancel={() => {
          setPreviewVisible(false);
          setEditMode(false);
          setEditedData(null);
        }}
        footer={[
          <Button
            key="edit"
            icon={<EditOutlined />}
            onClick={() => setEditMode(!editMode)}
          >
            {editMode ? '完成编辑' : '编辑数据'}
          </Button>,
          <Button key="cancel" onClick={() => {
            setPreviewVisible(false);
            setPreviewData(null);
            setEditedData(null);
            setEditMode(false);
          }}>
            取消
          </Button>,
          <Button
            key="confirm"
            type="primary"
            loading={loading}
            onClick={handleConfirmPreview}
            disabled={buttonDisabled}
          >
            {confirmButtonText}
          </Button>
        ]}
        width={1200}
        style={{ top: 20 }}
      >
        <div style={{ maxHeight: '70vh', overflowY: 'auto' }}>
          {/* 基本信息编辑区域 */}
          {editMode && (
            <Card title="基本信息" style={{ marginBottom: 16 }}>
              <Row gutter={16}>
                <Col span={8}>
                  <Form.Item label="采购日期">
                    <DatePicker
                      value={editedData?.purchase_date ? dayjs(editedData.purchase_date) : null}
                      onChange={(date) => {
                        const newData = { ...editedData };
                        newData.purchase_date = date ? date.format('YYYY-MM-DD') : null;
                        setEditedData(newData);
                      }}
                      style={{ width: '100%' }}
                    />
                  </Form.Item>
                </Col>
                <Col span={8}>
                  <Form.Item label="分拨日期">
                    <DatePicker
                      value={editedData?.distribution_date ? dayjs(editedData.distribution_date) : null}
                      onChange={(date) => {
                        const newData = { ...editedData };
                        newData.distribution_date = date ? date.format('YYYY-MM-DD') : null;
                        setEditedData(newData);
                      }}
                      style={{ width: '100%' }}
                    />
                  </Form.Item>
                </Col>
                <Col span={8}>
                  <Form.Item label="备注">
                    <Input
                      value={editedData?.notes || ''}
                      onChange={(e) => {
                        const newData = { ...editedData };
                        newData.notes = e.target.value;
                        setEditedData(newData);
                      }}
                      placeholder="请输入备注"
                    />
                  </Form.Item>
                </Col>
              </Row>
            </Card>
          )}

          {/* 处理信息 */}
          {processingInfo && (
            <Alert
              message={`处理方式: ${processingInfo.method === 'ai' ? 'AI智能识别' : '模板匹配'}`}
              description={`识别置信度: ${(processingInfo.confidence * 100).toFixed(1)}% | 总行数: ${processingInfo.total_rows} | 有效行数: ${processingInfo.valid_rows}`}
              type="info"
              style={{ marginBottom: 16 }}
            />
          )}

          {/* 验证结果 */}
          {validationResults && validationResults.errors && validationResults.errors.length > 0 && (
            <Alert
              message="数据验证错误"
              description={
                <div>
                  {validationResults.errors.map((error, index) => (
                    <div key={index} style={{ marginBottom: index < validationResults.errors.length - 1 ? 4 : 0 }}>
                      {typeof error === 'string' ? error : 
                       typeof error === 'object' ? (
                         error.error_message ? 
                           `第${error.row_index || ''}行 ${error.column || ''}列: ${error.error_message}` :
                           JSON.stringify(error)
                       ) : 
                       String(error)}
                    </div>
                  ))}
                </div>
              }
              type="error"
              style={{ marginBottom: 16 }}
            />
          )}

          {/* 采购数据 */}
          {hasPurchaseData ? (
            <Card 
              title={
                <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                  <span>采购商品 ({(editedData?.purchase_items || actualData.purchase_items).length}项)</span>
                  {editMode && (
                    <Button type="dashed" onClick={addPurchaseItem} icon={<PlusOutlined />}>
                      添加商品
                    </Button>
                  )}
                </div>
              }
              style={{ marginBottom: 16 }}
            >
              <Table
                columns={purchaseColumns}
                dataSource={editedData?.purchase_items || actualData.purchase_items}
                rowKey={(record, index) => index}
                pagination={false}
                scroll={{ x: 1000 }}
                size="small"
              />
              
              {/* 汇总信息 */}
              <div style={{ marginTop: 16, textAlign: 'right' }}>
                <Space size="large">
                  <Text strong>
                    总数量: {(editedData?.purchase_items || actualData.purchase_items).reduce((sum, item) => sum + (item.quantity || 0), 0)}
                  </Text>
                  <Text strong>
                    总金额: ¥{(editedData?.purchase_items || actualData.purchase_items).reduce((sum, item) => sum + ((item.quantity || 0) * (item.unit_price || 0)), 0).toFixed(2)}
                  </Text>
                </Space>
              </div>
            </Card>
          ) : (
            <Card title="采购数据" style={{ marginBottom: 16 }}>
              <Empty description="未识别到采购数据" />
            </Card>
          )}

          {/* 分拨数据 */}
          {hasDistributionData ? (
            <Card title={`分拨目标 (${actualData.distribution_destinations.length}个)`} style={{ marginBottom: 16 }}>
              {actualData.distribution_destinations.map((dest, index) => (
                <Card
                  key={index}
                  type="inner"
                  title={
                    <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                      <div>
                        <Tag color={dest.type === 'store' ? 'blue' : 'green'}>
                          {dest.type === 'store' ? '门店' : '仓库'}
                        </Tag>
                        <span>{dest.target_name}</span>
                        {dest.matched_name && dest.matched_name !== dest.target_name && (
                          <span style={{ marginLeft: 8, color: '#666' }}>
                            → {dest.matched_name}
                          </span>
                        )}
                        {dest.confidence !== undefined && (
                          <Tag 
                            color={dest.confidence >= 0.9 ? 'green' : dest.confidence >= 0.7 ? 'orange' : 'red'}
                            style={{ marginLeft: 8 }}
                          >
                            匹配度: {(dest.confidence * 100).toFixed(0)}%
                          </Tag>
                        )}
                      </div>
                      <div>
                        {!dest.target_id && (
                          <Tag color="red">需要手动选择</Tag>
                        )}
                        <Button 
                          type="link" 
                          size="small"
                          onClick={() => handleEditDistributionTarget(index)}
                        >
                          编辑
                        </Button>
                      </div>
                    </div>
                  }
                  style={{ marginBottom: 8 }}
                >
                  {/* 如果没有匹配到目标，显示选择器 */}
                  {!dest.target_id && (
                    <div 
                      style={{ marginBottom: 16, padding: 12, backgroundColor: '#fff7e6', border: '1px solid #ffd591', borderRadius: 4 }}
                      data-destination-index={index}
                    >
                      <div style={{ marginBottom: 8 }}>
                        <Text type="warning">未找到匹配的{dest.type === 'store' ? '门店' : '仓库'}，请手动选择：</Text>
                      </div>
                      <Row gutter={8}>
                        <Col span={6}>
                          <Select
                            value={dest.type}
                            onChange={(value) => handleUpdateDistributionDestination(index, 'type', value)}
                            style={{ width: '100%' }}
                          >
                            <Option value="store">门店</Option>
                            <Option value="warehouse">仓库</Option>
                          </Select>
                        </Col>
                        <Col span={12}>
                          <Select
                            key={`target-select-${index}-${dest.type}-${previewData?.lastUpdate || 0}`}
                            value={dest.target_id}
                            onChange={(value) => {
                              console.log('门店选择onChange触发:', { index, value, dest });
                              handleUpdateDistributionDestination(index, 'target_id', value);
                            }}
                            placeholder={`选择${dest.type === 'store' ? '门店' : '仓库'}`}
                            style={{ width: '100%' }}
                            showSearch
                            filterOption={(input, option) =>
                              option.children.toLowerCase().indexOf(input.toLowerCase()) >= 0
                            }
                            onSelect={(value) => {
                              console.log('门店选择onSelect触发:', { index, value });
                              // 立即更新target_id
                              handleUpdateDistributionDestination(index, 'target_id', value);
                              // 选中后自动确认
                              setTimeout(() => {
                                handleConfirmDistributionTarget(index, value);
                              }, 100);
                            }}
                            dropdownRender={(menu) => {
                              console.log('下拉框渲染:', { 
                                type: dest.type, 
                                storesCount: stores.length, 
                                warehousesCount: warehouses.length,
                                targetList: dest.type === 'store' ? stores : warehouses
                              });
                              return menu;
                            }}
                          >
                            {(dest.type === 'store' ? stores : warehouses).map(target => (
                              <Option key={target.id} value={target.id}>
                                {target.name}
                              </Option>
                            ))}
                          </Select>
                        </Col>
                        <Col span={6}>
                          <Button 
                            type="primary" 
                            size="small"
                            onClick={() => handleConfirmDistributionTarget(index, dest.target_id)}
                            disabled={!dest.target_id}
                          >
                            确认选择
                          </Button>
                        </Col>
                      </Row>
                    </div>
                  )}
                  
                  <Table
                    columns={[
                      { title: '商品名称', dataIndex: 'product_name', key: 'product_name' },
                      { title: '规格', dataIndex: 'product_specification', key: 'product_specification' },
                      { title: '单位', dataIndex: 'product_unit', key: 'product_unit' },
                      { title: '数量', dataIndex: 'quantity', key: 'quantity' },
                      { title: '单价', dataIndex: 'unit_price', key: 'unit_price', render: text => `¥${text}` },
                      { title: '金额', dataIndex: 'total_amount', key: 'total_amount', render: text => `¥${text}` }
                    ]}
                    dataSource={dest.items}
                    rowKey={(record, idx) => idx}
                    pagination={false}
                    size="small"
                  />
                  <div style={{ marginTop: 8, textAlign: 'right' }}>
                    <Text strong>小计: ¥{dest.total_amount.toFixed(2)}</Text>
                  </div>
                </Card>
              ))}
            </Card>
          ) : (
            <Card title="分拨数据" style={{ marginBottom: 16 }}>
              <Empty description="未识别到分拨数据" />
            </Card>
          )}
        </div>
      </Modal>
    );
  };

  // 渲染新增商品模态框
  const renderProductModal = () => {
    return (
      <Modal
        title="添加商品"
        open={productModalVisible}
        onCancel={() => setProductModalVisible(false)}
        footer={[
          <Button key="cancel" onClick={() => setProductModalVisible(false)}>
            取消
          </Button>,
          <Button
            key="submit"
            type="primary"
            loading={loading}
            onClick={handleProductFormSubmit}
          >
            保存
          </Button>,
        ]}
        width={720}
      >
        <Form
          form={productForm}
          layout="vertical"
        >
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="name"
                label="商品名称"
                rules={[{ required: true, message: '请输入商品名称' }]}
              >
                <Input placeholder="请输入商品名称" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="sku"
                label="SKU"
                rules={[{ required: true, message: '请输入商品SKU' }]}
              >
                <Input placeholder="请输入商品SKU" />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="category_id"
                label="分类"
                rules={[{ required: true, message: '请选择分类' }]}
              >
                <Select placeholder="请选择分类">
                  {categories.map(category => (
                    <Option key={category.id} value={category.id}>{category.name}</Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="brand_id"
                label="品牌"
              >
                <Select placeholder="请选择品牌" allowClear>
                  {brands.map(brand => (
                    <Option key={brand.id} value={brand.id}>{brand.name}</Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="cost_price"
                label="成本价"
              >
                <InputNumber
                  style={{ width: '100%' }}
                  placeholder="请输入成本价"
                  min={0}
                  precision={2}
                  prefix="¥"
                />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="retail_price"
                label="零售价"
              >
                <InputNumber
                  style={{ width: '100%' }}
                  placeholder="请输入零售价"
                  min={0}
                  precision={2}
                  prefix="¥"
                />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="unit"
                label="单位"
                initialValue="个"
                rules={[{ required: true, message: '请输入商品单位' }]}
              >
                <Input placeholder="如：个、箱、瓶、公斤" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="specification"
                label="规格"
              >
                <Input placeholder="请输入商品规格" />
              </Form.Item>
            </Col>
          </Row>

          <Form.Item
            name="description"
            label="描述"
          >
            <Input.TextArea rows={4} placeholder="请输入商品描述" />
          </Form.Item>
        </Form>
      </Modal>
    );
  };

  // 删除采购订单
  const handleDeleteOrder = async (orderId) => {
    try {
      await apiService.project.purchaseOrder.delete(orderId);
      message.success('删除成功');
      fetchOrders();
    } catch (error) {
      console.error('删除失败:', error);
      message.error('删除失败');
    }
  };

  // 创建分拨单
  const handleCreateDistribution = async (record) => {
    try {
      setLoading(true);
      
      // 获取采购订单详情
      const orderResponse = await apiService.project.purchaseOrder.getDetail(record.id);
      if (!orderResponse || !orderResponse.success) {
        message.error('获取采购订单详情失败');
        return;
      }
      
      const order = orderResponse.data;
      
      // 创建分拨单数据
      const distributionData = {
        destination_type: "store", // 默认分拨到门店
        destination_id: stores[0]?.id, // 默认选择第一个门店
        total_amount: order.final_amount || 0,
        items: order.items?.map(item => ({
          product_id: item.product_id,
          product_name: item.product_name,
          product_code: item.product_code || item.sku,
          specification: item.specification,
          unit: item.unit,
          expected_quantity: item.quantity,
          price: item.price,
          notes: `来自采购单 ${order.order_number}`
        })) || []
      };
      
      // 调用创建分拨单API
      const response = await apiService.project.purchaseOrder.createDistribution(record.id, distributionData);
      
      if (response && response.success) {
        message.success('创建分拨单成功');
        fetchOrders(); // 刷新列表
        
        // 跳转到分拨单管理页面
        setActiveTab('distribution');
      } else {
        message.error(response?.message || '创建分拨单失败');
      }
    } catch (error) {
      console.error('创建分拨单失败:', error);
      message.error('创建分拨单失败: ' + (error.response?.data?.detail || error.message || '服务器错误'));
    } finally {
      setLoading(false);
    }
  };

  // 获取关联分拨单
  const fetchRelatedArrivals = async (purchaseOrderId) => {
    try {
      setLoading(true);
      const response = await apiService.project.purchaseOrder.getArrivals(purchaseOrderId);
      
      if (response.data?.data?.arrivals) {
        setRelatedArrivals(response.data.data.arrivals);
      } else {
        setRelatedArrivals([]);
      }
    } catch (error) {
      console.error('获取关联分拨单失败:', error);
      message.error('获取关联分拨单失败');
      setRelatedArrivals([]);
    } finally {
      setLoading(false);
    }
  };

  // 查看关联分拨单
  const handleViewRelatedArrivals = async (record) => {
    setSelectedPurchaseOrder(record);
    await fetchRelatedArrivals(record.id);
    setRelatedArrivalsModalVisible(true);
  };

  // 创建分拨单
  const handleCreateDistributionFromOrder = (record) => {
    setSelectedPurchaseOrder(record);
    setCreateDistributionModalVisible(true);
  };

  // 确认创建分拨单
  const handleConfirmCreateDistribution = async (distributionData) => {
    try {
      setLoading(true);
      
      const response = await apiService.project.purchaseOrder.createDistribution(selectedPurchaseOrder.id, distributionData);

      if (response && response.success) {
        message.success('分拨单创建成功');
        setCreateDistributionModalVisible(false);
        setSelectedPurchaseOrder(null);
        
        // 刷新关联分拨单列表
        if (relatedArrivalsModalVisible) {
          await fetchRelatedArrivals(selectedPurchaseOrder.id);
        }
      } else {
        message.error(response.data?.message || '创建分拨单失败');
      }
    } catch (error) {
      console.error('创建分拨单失败:', error);
      message.error('创建分拨单失败');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="purchase-order-list-page">
      <Card>
        <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: 16 }}>
          <Title level={4}>采购管理</Title>
        </div>

        <Tabs activeKey={activeTab} onChange={setActiveTab}>
          <TabPane tab="采购订单管理" key="orders">
            <Card>
              {/* 搜索表单 */}
              <Card style={{ marginBottom: 16 }}>
                <Form
                  form={form}
                  layout="inline"
                  onFinish={handleSearch}
                  style={{ marginBottom: 16 }}
                >
                  <Form.Item name="search">
                    <Input
                      placeholder="订单编号"
                      prefix={<SearchOutlined />}
                      style={{ width: 200 }}
                    />
                  </Form.Item>
                  <Form.Item name="status">
                    <Select placeholder="订单状态" style={{ width: 120 }} allowClear>
                      <Option value="draft">草稿</Option>
                      <Option value="confirmed">已确认</Option>
                      <Option value="received">已收货</Option>
                      <Option value="cancelled">已取消</Option>
                    </Select>
                  </Form.Item>
                  <Form.Item name="payment_status">
                    <Select placeholder="支付状态" style={{ width: 120 }} allowClear>
                      <Option value="unpaid">未支付</Option>
                      <Option value="partial">部分支付</Option>
                      <Option value="paid">已支付</Option>
                    </Select>
                  </Form.Item>
                  <Form.Item name="warehouse_id">
                    <Select placeholder="选择仓库" style={{ width: 150 }} allowClear>
                      {warehouses.map(warehouse => (
                        <Option key={warehouse.id} value={warehouse.id}>
                          {warehouse.name}
                        </Option>
                      ))}
                    </Select>
                  </Form.Item>
                  <Form.Item name="dateRange">
                    <RangePicker placeholder={['开始日期', '结束日期']} />
                  </Form.Item>
                  <Form.Item>
                    <Button type="primary" htmlType="submit" icon={<SearchOutlined />}>
                      搜索
                    </Button>
                  </Form.Item>
                  <Form.Item>
                    <Button onClick={handleReset} icon={<ReloadOutlined />}>
                      重置
                    </Button>
                  </Form.Item>
                  <Form.Item>
                    <Button type="primary" icon={<PlusOutlined />} onClick={handleCreate}>
                      新建采购订单
                    </Button>
                  </Form.Item>
                </Form>
              </Card>

              {/* 统计信息 */}
              <Row gutter={16} style={{ marginBottom: 16 }}>
                <Col span={6}>
                  <Card>
                    <Statistic title="总订单" value={stats.total} />
                  </Card>
                </Col>
                <Col span={6}>
                  <Card>
                    <Statistic title="已确认" value={stats.confirmed} valueStyle={{ color: '#1890ff' }} />
                  </Card>
                </Col>
                <Col span={6}>
                  <Card>
                    <Statistic title="已收货" value={stats.received} valueStyle={{ color: '#52c41a' }} />
                  </Card>
                </Col>
                <Col span={6}>
                  <Card>
                    <Statistic title="已支付" value={stats.paid} valueStyle={{ color: '#52c41a' }} />
                  </Card>
                </Col>
              </Row>

              {/* 状态快捷筛选 */}
              <Card style={{ marginBottom: 16 }}>
                <div style={{ display: 'flex', alignItems: 'center', gap: '8px', flexWrap: 'wrap' }}>
                  <span style={{ marginRight: '8px', fontWeight: 'bold' }}>快捷筛选:</span>
                  <Button 
                    type={searchParams.status === '' ? 'primary' : 'default'}
                    size="small"
                    onClick={() => {
                      const newParams = { ...searchParams, status: '' };
                      setSearchParams(newParams);
                      form.setFieldValue('status', undefined);
                      fetchOrders();
                    }}
                  >
                    全部 ({stats.total})
                  </Button>
                  <Button 
                    type={searchParams.status === 'draft' ? 'primary' : 'default'}
                    size="small"
                    onClick={() => {
                      const newParams = { ...searchParams, status: 'draft' };
                      setSearchParams(newParams);
                      form.setFieldValue('status', 'draft');
                      fetchOrders();
                    }}
                  >
                    草稿 ({stats.draft || 0})
                  </Button>
                  <Button 
                    type={searchParams.status === 'confirmed' ? 'primary' : 'default'}
                    size="small"
                    onClick={() => {
                      const newParams = { ...searchParams, status: 'confirmed' };
                      setSearchParams(newParams);
                      form.setFieldValue('status', 'confirmed');
                      fetchOrders();
                    }}
                  >
                    已确认 ({stats.confirmed})
                  </Button>
                  <Button 
                    type={searchParams.status === 'received' ? 'primary' : 'default'}
                    size="small"
                    onClick={() => {
                      const newParams = { ...searchParams, status: 'received' };
                      setSearchParams(newParams);
                      form.setFieldValue('status', 'received');
                      fetchOrders();
                    }}
                  >
                    已收货 ({stats.received})
                  </Button>
                  <Button 
                    type={searchParams.status === 'cancelled' ? 'primary' : 'default'}
                    size="small"
                    onClick={() => {
                      const newParams = { ...searchParams, status: 'cancelled' };
                      setSearchParams(newParams);
                      form.setFieldValue('status', 'cancelled');
                      fetchOrders();
                    }}
                  >
                    已取消 ({stats.cancelled || 0})
                  </Button>
                </div>
              </Card>

              {/* 采购订单表格 */}
              <Table
                columns={[
                  {
                    title: '订单编号',
                    dataIndex: 'order_number',
                    key: 'order_number',
                    render: (text, record) => (
                      <Button type="link" onClick={() => handleView(record.id)}>
                        {text}
                      </Button>
                    )
                  },
                  {
                    title: '仓库',
                    dataIndex: 'warehouse_name',
                    key: 'warehouse_name'
                  },
                  {
                    title: '商品详情',
                    key: 'items',
                    render: (text, record) => {
                      if (!record.items || record.items.length === 0) {
                        return <span style={{ color: '#999' }}>暂无商品</span>;
                      }
                      
                      // 按供应商分组显示
                      const supplierGroups = {};
                      record.items.forEach(item => {
                        const supplierName = item.supplier_name || '未指定供应商';
                        if (!supplierGroups[supplierName]) {
                          supplierGroups[supplierName] = [];
                        }
                        supplierGroups[supplierName].push(item);
                      });

                      return (
                        <div>
                          {Object.entries(supplierGroups).map(([supplierName, items]) => (
                            <div key={supplierName} style={{ marginBottom: 8 }}>
                              <Text strong style={{ color: '#1890ff' }}>{supplierName}:</Text>
                              <div style={{ marginLeft: 16 }}>
                                {items.slice(0, 3).map((item, index) => (
                                  <div key={index}>
                                    <Text>{item.product_name}</Text>
                                    {item.product_specification && (
                                      <Text type="secondary"> ({item.product_specification})</Text>
                                    )}
                                    <Text type="secondary"> x{item.quantity}{item.product_unit}</Text>
                                  </div>
                                ))}
                                {items.length > 3 && (
                                  <Text type="secondary">等{items.length}个商品</Text>
                                )}
                              </div>
                            </div>
                          ))}
                        </div>
                      );
                    }
                  },
                  {
                    title: '订单状态',
                    dataIndex: 'status',
                    key: 'status',
                    render: (status) => {
                      const statusConfig = {
                        draft: { color: 'default', text: '草稿' },
                        confirmed: { color: 'processing', text: '已确认' },
                        received: { color: 'success', text: '已收货' },
                        cancelled: { color: 'error', text: '已取消' }
                      };
                      const config = statusConfig[status] || { color: 'default', text: status };
                      return <Tag color={config.color}>{config.text}</Tag>;
                    }
                  },
                  {
                    title: '支付状态',
                    dataIndex: 'payment_status',
                    key: 'payment_status',
                    render: (status) => {
                      const statusConfig = {
                        unpaid: { color: 'default', text: '未支付' },
                        partial: { color: 'warning', text: '部分支付' },
                        paid: { color: 'success', text: '已支付' }
                      };
                      const config = statusConfig[status] || { color: 'default', text: status };
                      return <Tag color={config.color}>{config.text}</Tag>;
                    }
                  },
                  {
                    title: '分拨状态',
                    key: 'distribution_status',
                    render: (_, record) => {
                      // 计算分拨状态
                      const distributedQuantity = record.distributed_quantity || 0;
                      const totalQuantity = record.total_quantity || 0;
                      const distributionCount = record.distribution_count || 0;
                      
                      if (distributionCount === 0) {
                        return <Tag color="default">未分拨</Tag>;
                      } else if (distributedQuantity >= totalQuantity) {
                        return <Tag color="success">已完成</Tag>;
                      } else {
                        return (
                          <div>
                            <Tag color="processing">部分分拨</Tag>
                            <div style={{ fontSize: '12px', color: '#666', marginTop: 2 }}>
                              {distributedQuantity}/{totalQuantity}
                            </div>
                          </div>
                        );
                      }
                    }
                  },
                  {
                    title: '关联分拨单',
                    key: 'related_distributions',
                    render: (_, record) => {
                      const distributionCount = record.distribution_count || 0;
                      if (distributionCount === 0) {
                        return <Text type="secondary">无</Text>;
                      }
                      return (
                        <Button 
                          type="link" 
                          size="small"
                          onClick={() => handleViewRelatedArrivals(record)}
                        >
                          {distributionCount} 个分拨单
                        </Button>
                      );
                    }
                  },
                  {
                    title: '订单金额',
                    dataIndex: 'final_amount',
                    key: 'final_amount',
                    align: 'right',
                    render: (amount) => `¥${parseFloat(amount || 0).toFixed(2)}`
                  },
                  {
                    title: '创建时间',
                    dataIndex: 'created_at',
                    key: 'created_at',
                    render: (time) => time ? new Date(time).toLocaleString() : '-'
                  },
                  {
                    title: '操作',
                    key: 'action',
                    render: (_, record) => (
                      <Space size="small">
                        <Tooltip title="查看详情">
                          <Button
                            type="text"
                            icon={<EyeOutlined />}
                            onClick={() => handleView(record.id)}
                          />
                        </Tooltip>
                        <Tooltip title="编辑订单">
                          <Button
                            type="text"
                            icon={<EditOutlined />}
                            onClick={() => handleEdit(record.id)}
                          />
                        </Tooltip>
                        {(record.status === 'confirmed' || record.status === 'received') && (
                          <>
                            <Tooltip title="查看关联分拨单">
                              <Button
                                type="text"
                                icon={<SwapOutlined />}
                                onClick={() => handleViewRelatedArrivals(record)}
                              />
                            </Tooltip>
                            <Tooltip title="创建分拨单">
                              <Button
                                type="text"
                                icon={<PlusOutlined />}
                                onClick={() => handleCreateDistributionFromOrder(record)}
                              />
                            </Tooltip>
                            <Tooltip title="入库">
                              <Button
                                type="text"
                                icon={<InboxOutlined />}
                                onClick={() => {
                                  // 跳转到仓库管理的入库页面
                                  navigate(`/project/warehouse/inbound?purchase_order_id=${record.id}`);
                                }}
                              />
                            </Tooltip>
                          </>
                        )}
                        <Popconfirm
                          title="删除采购订单"
                          description="确定要删除此采购订单吗？"
                          onConfirm={() => handleDeleteOrder(record.id)}
                          okText="确定"
                          cancelText="取消"
                        >
                          <Tooltip title="删除订单">
                            <Button
                              type="text"
                              danger
                              icon={<DeleteOutlined />}
                            />
                          </Tooltip>
                        </Popconfirm>
                      </Space>
                    )
                  }
                ]}
                dataSource={orders}
                rowKey="id"
                pagination={{
                  current: pagination.current,
                  pageSize: pagination.pageSize,
                  total: pagination.total,
                  showSizeChanger: true,
                  showQuickJumper: true,
                  showTotal: (total) => `共 ${total} 条记录`,
                  onChange: (page, size) => {
                    setPagination(prev => ({
                      ...prev,
                      current: page,
                      pageSize: size
                    }));
                    fetchOrders(page, size);
                  }
                }}
                loading={loading}
              />
            </Card>
          </TabPane>
          
          <TabPane tab="分拨订单管理" key="distribution">
            <DistributionOrderManagement 
              stores={stores}
              warehouses={warehouses}
            />
          </TabPane>

          <TabPane tab="智能上传" key="upload">
            <Card title="智能上传采购分拨单" style={{ marginBottom: 16 }}>
              <Form form={distributionForm} layout="vertical">
                <Row gutter={16}>
                  <Col span={8}>
                    <Form.Item
                      name="upload_type"
                      label="上传类型"
                      initialValue="both"
                      rules={[{ required: true, message: '请选择上传类型' }]}
                    >
                      <Select onChange={setUploadType}>
                        <Option value="both">采购和分拨</Option>
                        <Option value="purchase">仅采购</Option>
                        <Option value="distribution">仅分拨</Option>
                      </Select>
                    </Form.Item>
                  </Col>
                  {uploadType !== 'distribution' && (
                    <Col span={8}>
                      <Form.Item
                        name="distribution_mode"
                        label="分拨模式"
                        initialValue="direct"
                      >
                        <Select onChange={setDistributionMode}>
                          <Option value="direct">直接分拨</Option>
                          <Option value="partial">部分入库</Option>
                        </Select>
                      </Form.Item>
                    </Col>
                  )}
                  {uploadType !== 'distribution' && distributionMode === 'partial' && (
                    <Col span={8}>
                      <Form.Item
                        name="warehouse_id"
                        label="入库仓库"
                        rules={[{ required: true, message: '请选择入库仓库' }]}
                      >
                        <Select
                          placeholder="选择仓库"
                          onChange={setSelectedWarehouse}
                        >
                          {warehouses.map(warehouse => (
                            <Option key={warehouse.id} value={warehouse.id}>
                              {warehouse.name}
                            </Option>
                          ))}
                        </Select>
                      </Form.Item>
                    </Col>
                  )}
                </Row>

                <Form.Item label="上传文件">
                  <Card>
                    <Tabs defaultActiveKey="upload" size="small">
                      <TabPane tab="本地上传" key="upload">
                        <Dragger
                          name="file"
                          multiple={false}
                          fileList={fileList}
                          onChange={({ fileList }) => setFileList(fileList)}
                          beforeUpload={(file) => {
                            // 自动开始上传处理
                            handleAutoUpload(file);
                            return false; // 阻止默认上传
                          }}
                          accept=".xlsx,.xls,.jpg,.jpeg,.png"
                        >
                          <p className="ant-upload-drag-icon">
                            <InboxOutlined />
                          </p>
                          <p className="ant-upload-text">点击或拖拽文件到此区域上传</p>
                          <p className="ant-upload-hint">
                            支持上传采购分拨单的图片（JPG、PNG）或Excel文件。系统将优先使用模板匹配进行快速识别，如果模板匹配失败或置信度较低，则自动切换到AI视觉识别模式，确保准确提取商品信息和分拨数据。
                          </p>
                        </Dragger>
                      </TabPane>
                      <TabPane tab="项目空间选择" key="space">
                        <FileSpaceSelector 
                          onFileSelect={handleSpaceFileSelect}
                          accept={['.xlsx', '.xls', '.jpg', '.jpeg', '.png']}
                          description="从项目空间选择已上传的采购分拨单文件"
                        />
                      </TabPane>
                    </Tabs>
                  </Card>
                </Form.Item>

                <Form.Item>
                  <Space>
                    <Button
                      type="primary"
                      onClick={handleManualUpload}
                      loading={uploading}
                      disabled={fileList.length === 0}
                      icon={<UploadOutlined />}
                    >
                      手动上传
                    </Button>
                    <Button
                      onClick={handleDownloadTemplate}
                      icon={<DownloadOutlined />}
                    >
                      下载模板
                    </Button>
                  </Space>
                </Form.Item>

                {uploading && (
                  <Form.Item>
                    <Progress percent={uploadProgress} status="active" />
                    <div style={{ marginTop: 8 }}>
                      {processing && (
                        <div>
                          <Spin size="small" style={{ marginRight: 8 }} />
                          {processingMessage || '正在处理...'}
                        </div>
                      )}
                    </div>
                  </Form.Item>
                )}

                {/* AI识别进度显示 */}
                {(processing || uploading) && (
                  <Card style={{ marginTop: 16 }}>
                    <div style={{ textAlign: 'center', padding: '20px 0' }}>
                      <Spin size="large" />
                      <div style={{ marginTop: 16 }}>
                        <Title level={4} style={{ color: '#1890ff' }}>
                          {uploading ? '正在上传文件...' : 'AI智能识别中...'}
                        </Title>
                        <Paragraph type="secondary">
                          {uploading 
                            ? '文件上传中，请稍候...' 
                            : '正在使用AI技术智能识别采购分拨单内容，请勿刷新页面或关闭浏览器'
                          }
                        </Paragraph>
                        
                        {/* 进度条 */}
                        {uploading && uploadProgress > 0 && (
                          <Progress 
                            percent={Math.round(uploadProgress)} 
                            status="active"
                            style={{ maxWidth: 400, margin: '0 auto' }}
                          />
                        )}
                        
                        {/* 处理状态信息 */}
                        {processing && processingMessage && (
                          <Alert
                            message={processingMessage}
                            type="info"
                            showIcon
                            style={{ marginTop: 16, maxWidth: 500, margin: '16px auto 0' }}
                          />
                        )}
                        
                        {/* 持续提示 */}
                        {processing && (
                          <div style={{ 
                            marginTop: 20, 
                            padding: '12px 20px',
                            background: '#fff7e6',
                            border: '1px solid #ffd591',
                            borderRadius: 6,
                            maxWidth: 600,
                            margin: '20px auto 0'
                          }}>
                            <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
                              <LoadingOutlined style={{ color: '#fa8c16', marginRight: 8 }} />
                              <Text strong style={{ color: '#fa8c16' }}>
                                AI正在智能识别中，请保持页面打开
                              </Text>
                            </div>
                            <div style={{ marginTop: 8, fontSize: '12px', color: '#8c8c8c', textAlign: 'center' }}>
                              识别过程通常需要30-60秒，请耐心等待
                            </div>
                          </div>
                        )}
                        
                        {/* 取消按钮 */}
                        {processing && (
                          <div style={{ marginTop: 16 }}>
                            <Button 
                              onClick={cancelProcessing}
                              icon={<StopOutlined />}
                            >
                              取消处理
                            </Button>
                          </div>
                        )}
                      </div>
                    </div>
                  </Card>
                )}

                {/* 处理状态提示 */}
                {isError && processingError && (
                  <Alert
                    message="AI识别失败"
                    description={processingError.message || '识别过程中出现错误，请检查文件格式后重试'}
                    type="error"
                    showIcon
                    style={{ marginTop: 16 }}
                    action={
                      <Button size="small" onClick={resetProcessing}>
                        重新上传
                      </Button>
                    }
                  />
                )}

                {isCompleted && processingResult && (
                  <Alert
                    message="AI识别成功"
                    description="采购分拨单已成功识别，请查看预览结果并确认"
                    type="success"
                    showIcon
                    style={{ marginTop: 16 }}
                    action={
                      <Button 
                        size="small" 
                        type="primary"
                        onClick={() => setPreviewVisible(true)}
                      >
                        查看结果
                      </Button>
                    }
                  />
                )}
              </Form>
            </Card>

            {/* 分拨商品管理 */}
            {uploadType !== 'purchase' && (
              <Card title="分拨商品管理" style={{ marginBottom: 16 }} className="distribution-items-section">
                <div style={{ marginBottom: 16 }}>
                  <Button
                    type="dashed"
                    onClick={handleAddDistributionItem}
                    icon={<PlusOutlined />}
                    block
                  >
                    添加分拨商品
                  </Button>
                </div>

                {distributionItems.map((item, index) => (
                  <Card
                    key={item.id}
                    type="inner"
                    title={
                      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                        <span>
                          {item.product_name ? (
                            <span>
                              <Text strong>{item.product_name}</Text>
                              {item.product_specification && (
                                <Text type="secondary"> ({item.product_specification})</Text>
                              )}
                              <Text type="secondary"> - 数量: {item.quantity} {item.product_unit}</Text>
                            </span>
                          ) : (
                            `商品 ${index + 1}`
                          )}
                        </span>
                      </div>
                    }
                    extra={
                      <Button
                        type="text"
                        danger
                        icon={<DeleteOutlined />}
                        onClick={() => handleRemoveDistributionItem(item.id)}
                      >
                        删除
                      </Button>
                    }
                    style={{ marginBottom: 16 }}
                  >
                    <Row gutter={16}>
                      <Col span={8}>
                        <Form.Item label="商品名称">
                          {item.product_name ? (
                            // 如果已有商品名称，显示为只读文本，并提供修改选项
                            <div>
                              <div style={{ 
                                padding: '4px 11px', 
                                border: '1px solid #d9d9d9', 
                                borderRadius: '6px',
                                background: '#fafafa',
                                marginBottom: 8
                              }}>
                                <Text strong>{item.product_name}</Text>
                                {item.product_specification && (
                                  <Text type="secondary"> ({item.product_specification})</Text>
                                )}
                              </div>
                              <Button 
                                size="small" 
                                type="link" 
                                onClick={() => {
                                  // 清空商品信息，允许重新选择
                                  handleUpdateDistributionItem(item.id, 'product_id', null);
                                  handleUpdateDistributionItem(item.id, 'product_name', '');
                                  handleUpdateDistributionItem(item.id, 'product_specification', '');
                                  handleUpdateDistributionItem(item.id, 'product_unit', '');
                                }}
                              >
                                重新选择商品
                              </Button>
                            </div>
                          ) : (
                            // 如果没有商品名称，显示选择器
                            <Select
                              value={item.product_id}
                              onChange={(value, option) => handleProductSelect(value, option, index)}
                              placeholder="搜索并选择商品"
                              showSearch
                              filterOption={false}
                              onSearch={searchProducts}
                              loading={searchingProducts}
                              notFoundContent={searchingProducts ? <Spin size="small" /> : '暂无数据'}
                              dropdownRender={menu => (
                                <div>
                                  {menu}
                                  <Divider style={{ margin: '4px 0' }} />
                                  <div style={{ padding: '4px 8px', cursor: 'pointer' }} onClick={() => handleAddProduct(index)}>
                                    <PlusOutlined /> 添加新商品
                                  </div>
                                </div>
                              )}
                            >
                              {products.map(product => (
                                <Option key={product.id} value={product.id}>
                                  {product.display_name || `${product.name}${product.specification ? ` (${product.specification})` : ''}`}
                                  {product.is_variant && (
                                    <Tag color="blue" size="small" style={{ marginLeft: 8 }}>
                                      多规格
                                    </Tag>
                                  )}
                                </Option>
                              ))}
                            </Select>
                          )}
                        </Form.Item>
                      </Col>
                      <Col span={3}>
                        <Form.Item label="规格">
                          <Input
                            value={item.product_specification}
                            onChange={(e) => handleUpdateDistributionItem(item.id, 'product_specification', e.target.value)}
                            placeholder="规格"
                          />
                        </Form.Item>
                      </Col>
                      <Col span={3}>
                        <Form.Item label="单位">
                          <Input
                            value={item.product_unit}
                            onChange={(e) => handleUpdateDistributionItem(item.id, 'product_unit', e.target.value)}
                            placeholder="单位"
                          />
                        </Form.Item>
                      </Col>
                      <Col span={3}>
                        <Form.Item label="数量">
                          <InputNumber
                            value={item.quantity}
                            onChange={(value) => handleUpdateDistributionItem(item.id, 'quantity', value)}
                            min={0}
                            style={{ width: '100%' }}
                          />
                        </Form.Item>
                      </Col>
                      <Col span={3}>
                        <Form.Item label="单价">
                          <InputNumber
                            value={item.unit_price}
                            onChange={(value) => handleUpdateDistributionItem(item.id, 'unit_price', value)}
                            min={0}
                            precision={2}
                            style={{ width: '100%' }}
                          />
                        </Form.Item>
                      </Col>
                      <Col span={4}>
                        <Form.Item label="供应商">
                          <Select
                            value={item.supplier_id}
                            onChange={(value) => handleUpdateDistributionItem(item.id, 'supplier_id', value)}
                            placeholder="选择供应商"
                            allowClear
                          >
                            {suppliers.map(supplier => (
                              <Option key={supplier.id} value={supplier.id}>
                                {supplier.name}
                              </Option>
                            ))}
                          </Select>
                        </Form.Item>
                      </Col>
                    </Row>

                    {/* 分拨目的地 */}
                    <div style={{ marginTop: 16 }}>
                      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: 8 }}>
                        <Text strong>分拨目的地</Text>
                        <Button
                          type="dashed"
                          size="small"
                          onClick={() => handleAddDestination(item.id)}
                          icon={<PlusOutlined />}
                        >
                          添加目的地
                        </Button>
                      </div>

                      {item.destinations.map((dest, destIndex) => (
                        <Row key={dest.id} gutter={8} style={{ marginBottom: 8 }}>
                          <Col span={6}>
                            <Select
                              value={dest.type}
                              onChange={(value) => {
                                // 更新类型时清空目标ID
                                handleUpdateDestination(item.id, dest.id, 'type', value);
                                handleUpdateDestination(item.id, dest.id, 'target_id', null);
                              }}
                              placeholder="目的地类型"
                            >
                              <Option value="store">门店</Option>
                              <Option value="warehouse">仓库</Option>
                            </Select>
                          </Col>
                          <Col span={8}>
                            <div data-destination-index={`${item.id}-${dest.id}`}>
                              <Select
                                key={`target-select-${item.id}-${dest.id}-${dest.type}-${dest.lastUpdate || Date.now()}`}
                                value={dest.target_id}
                                onChange={(value) => {
                                  console.log('门店选择onChange触发:', { itemId: item.id, destId: dest.id, value, dest });
                                  handleUpdateDestination(item.id, dest.id, 'target_id', value);
                                  
                                  // 选择后立即显示选中状态
                                  if (value) {
                                    const targets = dest.type === 'store' ? stores : warehouses;
                                    const target = targets.find(t => t.id === value);
                                    if (target) {
                                      message.success(`已选择${dest.type === 'store' ? '门店' : '仓库'}: ${target.name}`);
                                    }
                                  }
                                }}
                                placeholder={`选择${dest.type === 'store' ? '门店' : '仓库'}`}
                                style={{ 
                                  width: '100%',
                                  // 如果已选择，添加成功的边框颜色
                                  ...(dest.target_id ? { borderColor: '#52c41a' } : {})
                                }}
                                showSearch
                                filterOption={(input, option) =>
                                  option.children.toLowerCase().indexOf(input.toLowerCase()) >= 0
                                }
                                onSelect={(value) => {
                                  console.log('门店选择onSelect触发:', { itemId: item.id, destId: dest.id, value });
                                }}
                                dropdownRender={(menu) => {
                                  console.log('下拉框渲染:', { 
                                    type: dest.type, 
                                    storesCount: stores.length, 
                                    warehousesCount: warehouses.length,
                                    targetList: dest.type === 'store' ? stores : warehouses
                                  });
                                  return menu;
                                }}
                                notFoundContent={
                                  (dest.type === 'store' ? stores : warehouses).length === 0 ? 
                                  `暂无可用${dest.type === 'store' ? '门店' : '仓库'}` : 
                                  '暂无匹配项'
                                }
                              >
                                {(dest.type === 'store' ? stores : warehouses).map(target => (
                                  <Option key={target.id} value={target.id}>
                                    {target.name}
                                  </Option>
                                ))}
                              </Select>
                              {/* 显示当前选中的名称 */}
                              {dest.target_id && (
                                <div style={{ 
                                  fontSize: '12px', 
                                  color: '#52c41a', 
                                  marginTop: 4,
                                  display: 'flex',
                                  alignItems: 'center'
                                }}>
                                  ✓ 已选择: {(() => {
                                    const targets = dest.type === 'store' ? stores : warehouses;
                                    const target = targets.find(t => t.id === dest.target_id);
                                    return target?.name || '未知';
                                  })()}
                                </div>
                              )}
                            </div>
                          </Col>
                          <Col span={6}>
                            <InputNumber
                              value={dest.quantity}
                              onChange={(value) => handleUpdateDestination(item.id, dest.id, 'quantity', value)}
                              placeholder="分拨数量"
                              min={0}
                              style={{ width: '100%' }}
                            />
                          </Col>
                          <Col span={4}>
                            <Button
                              type="text"
                              danger
                              icon={<DeleteOutlined />}
                              onClick={() => handleRemoveDestination(item.id, dest.id)}
                            />
                          </Col>
                        </Row>
                      ))}
                    </div>
                  </Card>
                ))}
              </Card>
            )}
          </TabPane>
        </Tabs>
      </Card>

      {/* 删除确认弹窗 */}
      <Modal
        title="删除采购订单"
        open={deleteModalVisible}
        onOk={handleDelete}
        onCancel={() => {
          setDeleteModalVisible(false);
          setCurrentOrder(null);
        }}
        confirmLoading={loading}
      >
        <p>确定要删除此采购订单吗？此操作不可恢复。</p>
        {currentOrder && (
          <div>
            <p><strong>订单编号:</strong> {currentOrder.order_number}</p>
            <p><strong>供应商:</strong> {
              currentOrder.items && currentOrder.items.length > 0 
                ? [...new Set(currentOrder.items.map(item => item.supplier_name || '未指定').filter(Boolean))].join('、') || '未指定供应商'
                : '未指定供应商'
            }</p>
            <p><strong>总金额:</strong> ¥{parseFloat(currentOrder.final_amount || 0).toFixed(2)}</p>
          </div>
        )}
      </Modal>

      {/* 更新订单状态弹窗 */}
      <Modal
        title="更新订单状态"
        open={statusModalVisible}
        onOk={handleUpdateStatus}
        onCancel={() => {
          setStatusModalVisible(false);
          setCurrentOrder(null);
          statusForm.resetFields();
        }}
        confirmLoading={loading}
      >
        {currentOrder && (
          <Form
            form={statusForm}
            layout="vertical"
          >
            <Form.Item
              name="status"
              label="订单状态"
              rules={[{ required: true, message: '请选择订单状态' }]}
            >
              <Select>
                <Option value="draft">草稿</Option>
                <Option value="confirmed">已确认</Option>
                <Option value="received">已收货</Option>
                <Option value="cancelled">已取消</Option>
              </Select>
            </Form.Item>

            <Form.Item
              name="actual_delivery_date"
              label="实际交货日期"
            >
              <DatePicker style={{ width: '100%' }} />
            </Form.Item>

            <Form.Item
              name="notes"
              label="备注"
            >
              <Input.TextArea rows={4} />
            </Form.Item>
          </Form>
        )}
      </Modal>

      {/* 更新支付状态弹窗 */}
      <Modal
        title="更新支付状态"
        open={paymentModalVisible}
        onOk={handleUpdatePayment}
        onCancel={() => {
          setPaymentModalVisible(false);
          setCurrentOrder(null);
          paymentForm.resetFields();
        }}
        confirmLoading={loading}
      >
        {currentOrder && (
          <Form
            form={paymentForm}
            layout="vertical"
          >
            <Form.Item
              name="payment_status"
              label="支付状态"
              rules={[{ required: true, message: '请选择支付状态' }]}
            >
              <Select>
                <Option value="unpaid">未支付</Option>
                <Option value="partial">部分支付</Option>
                <Option value="paid">已支付</Option>
              </Select>
            </Form.Item>

            <Form.Item
              name="payment_method"
              label="支付方式"
            >
              <Select allowClear>
                <Option value="cash">现金</Option>
                <Option value="bank_transfer">银行转账</Option>
                <Option value="credit_card">信用卡</Option>
                <Option value="alipay">支付宝</Option>
                <Option value="wechat">微信支付</Option>
                <Option value="other">其他</Option>
              </Select>
            </Form.Item>

            <Form.Item
              name="payment_date"
              label="支付日期"
            >
              <DatePicker style={{ width: '100%' }} />
            </Form.Item>

            <Form.Item
              name="notes"
              label="备注"
            >
              <Input.TextArea rows={4} />
            </Form.Item>
          </Form>
        )}
      </Modal>

      {/* 预览模态框 */}
      {renderPreviewModal()}

      {/* 新增商品模态框 */}
      {renderProductModal()}

      {/* 查看关联分拨单模态框 */}
      <Modal
        title={`采购订单关联分拨单 - ${selectedPurchaseOrder?.order_number || ''}`}
        open={relatedArrivalsModalVisible}
        onCancel={() => setRelatedArrivalsModalVisible(false)}
        footer={[
          <Button key="close" onClick={() => setRelatedArrivalsModalVisible(false)}>
            关闭
          </Button>,
          <Button 
            key="create" 
            type="primary" 
            onClick={() => {
              setRelatedArrivalsModalVisible(false);
              handleCreateDistributionFromOrder(selectedPurchaseOrder);
            }}
          >
            创建新分拨单
          </Button>
        ]}
        width={1000}
      >
        <Table
          columns={[
            {
              title: '分拨单号',
              dataIndex: 'arrival_number',
              key: 'arrival_number',
              width: 150
            },
            {
              title: '目标类型',
              key: 'target_type',
              width: 100,
              render: (_, record) => {
                return record.store_id ? '门店' : '仓库';
              }
            },
            {
              title: '目标名称',
              key: 'target_name',
              render: (_, record) => {
                return record.store_name || record.warehouse_name || '-';
              }
            },
            {
              title: '状态',
              dataIndex: 'status',
              key: 'status',
              width: 100,
              render: (status) => {
                const statusMap = {
                  'pending': { text: '待确认', color: 'orange' },
                  'confirmed': { text: '已确认', color: 'green' },
                  'rejected': { text: '已拒绝', color: 'red' },
                  'partial': { text: '部分确认', color: 'blue' }
                };
                const config = statusMap[status] || { text: status, color: 'default' };
                return <Tag color={config.color}>{config.text}</Tag>;
              }
            },
            {
              title: '总金额',
              dataIndex: 'total_amount',
              key: 'total_amount',
              width: 120,
              align: 'right',
              render: (amount) => `¥${parseFloat(amount || 0).toFixed(2)}`
            },
            {
              title: '到货时间',
              dataIndex: 'arrival_date',
              key: 'arrival_date',
              width: 150,
              render: (time) => time ? new Date(time).toLocaleString() : '-'
            },
            {
              title: '操作',
              key: 'action',
              width: 150,
              render: (_, record) => (
                <Space size="small">
                  <Button 
                    type="link" 
                    size="small"
                    onClick={() => {
                      // 跳转到门店运营页面查看详情
                      navigate(`/project/store-operations?arrival_id=${record.id}`);
                    }}
                  >
                    查看详情
                  </Button>
                  {record.status === 'pending' && (
                    <Button 
                      type="link" 
                      size="small"
                      onClick={() => {
                        // 跳转到门店运营页面进行确认操作
                        navigate(`/project/store-operations?arrival_id=${record.id}&action=confirm`);
                      }}
                    >
                      确认到货
                    </Button>
                  )}
                </Space>
              )
            }
          ]}
          dataSource={relatedArrivals}
          rowKey="id"
          pagination={false}
          size="small"
          loading={loading}
        />
      </Modal>

      {/* 创建分拨单模态框 */}
      <Modal
        title={`创建分拨单 - ${selectedPurchaseOrder?.order_number || ''}`}
        open={createDistributionModalVisible}
        onCancel={() => setCreateDistributionModalVisible(false)}
        footer={null}
        width={800}
      >
        <CreateDistributionForm
          purchaseOrder={selectedPurchaseOrder}
          stores={stores}
          warehouses={warehouses}
          onSubmit={handleConfirmCreateDistribution}
          onCancel={() => setCreateDistributionModalVisible(false)}
          loading={loading}
        />
      </Modal>
    </div>
  );
};

export default PurchaseOrderList;
