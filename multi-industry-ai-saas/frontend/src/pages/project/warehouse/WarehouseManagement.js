import React, { useState, useEffect } from 'react';
import {
  Card, Tabs, Typography, message
} from 'antd';
import {
  InboxOutlined, ImportOutlined, BankOutlined
} from '@ant-design/icons';
import apiService from '../../../services/api';
import WarehouseList from './components/WarehouseList';
import PurchaseInboundManagement from './components/PurchaseInboundManagement';

const { Title } = Typography;
const { TabPane } = Tabs;

/**
 * 仓库管理页面
 * 包含采购入库管理和多仓管理两个标签页
 */
const WarehouseManagement = () => {
  const [activeTab, setActiveTab] = useState('1');
  const [loading, setLoading] = useState(false);
  const [warehouses, setWarehouses] = useState([]);

  // 初始化数据
  useEffect(() => {
    fetchWarehouses();
  }, []);

  // 获取仓库列表
  const fetchWarehouses = async () => {
    setLoading(true);
    try {
      // 使用仓库管理API
      const response = await apiService.project.warehouse.getList();
      if (response && response.items) {
        setWarehouses(response.items || []);
      } else {
        console.log('获取仓库列表返回数据:', response);
        message.error('获取仓库列表失败');
      }
    } catch (error) {
      console.error('获取仓库列表失败:', error);
      message.error('获取仓库列表失败: ' + (error.message || '未知错误'));
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="warehouse-management-page">
      <Card>
        <div style={{ marginBottom: 16 }}>
          <Title level={4}>仓库管理</Title>
        </div>

        <Tabs activeKey={activeTab} onChange={setActiveTab}>
          <TabPane
            tab={
              <span>
                <ImportOutlined />
                采购入库管理
              </span>
            }
            key="1"
          >
            <PurchaseInboundManagement
              warehouses={warehouses}
              loading={loading}
              refreshWarehouses={fetchWarehouses}
            />
          </TabPane>

          <TabPane
            tab={
              <span>
                <BankOutlined />
                多仓管理
              </span>
            }
            key="2"
          >
            <WarehouseList
              warehouses={warehouses}
              loading={loading}
              refreshWarehouses={fetchWarehouses}
            />
          </TabPane>
        </Tabs>
      </Card>
    </div>
  );
};

export default WarehouseManagement;
