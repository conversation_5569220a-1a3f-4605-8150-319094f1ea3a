import React, { useState } from 'react';
import { Card, Tabs, Typography } from 'antd';
import { Bar<PERSON>hartOutlined, LineChartOutlined, PieChartOutlined } from '@ant-design/icons';
import SalesAnalysis from './SalesAnalysis';
import RechargeAnalysis from './RechargeAnalysis';
import ProfitAnalysis from './ProfitAnalysis';
import { useNavigate, useLocation } from 'react-router-dom';

const { TabPane } = Tabs;
const { Title } = Typography;

/**
 * 数据分析组件
 */
const DataAnalysis = () => {
  const navigate = useNavigate();
  const location = useLocation();

  // 根据当前路径确定激活的标签页
  const getActiveKey = () => {
    const path = location.pathname;
    if (path.includes('/recharge')) return '2';
    if (path.includes('/profit')) return '3';
    return '1'; // 默认为销售分析
  };

  // 处理标签页切换
  const handleTabChange = (key) => {
    switch (key) {
      case '1':
        navigate('/project/finance/data-analysis/sales');
        break;
      case '2':
        navigate('/project/finance/data-analysis/recharge');
        break;
      case '3':
        navigate('/project/finance/data-analysis/profit');
        break;
      default:
        navigate('/project/finance/data-analysis/sales');
    }
  };

  return (
    <div className="data-analysis-container">
      <Card>
        <div style={{ marginBottom: 16, display: 'flex', justifyContent: 'space-between' }}>
          <Title level={4} style={{ margin: 0 }}>数据分析</Title>
        </div>

        <Tabs activeKey={getActiveKey()} onChange={handleTabChange}>
          <TabPane
            tab={
              <span>
                <BarChartOutlined />
                销售分析
              </span>
            }
            key="1"
          >
            <SalesAnalysis />
          </TabPane>
          <TabPane
            tab={
              <span>
                <LineChartOutlined />
                储值分析
              </span>
            }
            key="2"
          >
            <RechargeAnalysis />
          </TabPane>
          <TabPane
            tab={
              <span>
                <PieChartOutlined />
                利润分析
              </span>
            }
            key="3"
          >
            <ProfitAnalysis />
          </TabPane>
        </Tabs>
      </Card>
    </div>
  );
};

export default DataAnalysis;
