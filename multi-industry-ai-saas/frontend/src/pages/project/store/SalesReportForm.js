import React, { useState, useEffect } from 'react';
import {
  Card,
  Form,
  Input,
  Button,
  Select,
  DatePicker,
  Space,
  Table,
  InputNumber,
  Typography,
  Divider,
  message,
  Row,
  Col,
  Tabs,
  Statistic
} from 'antd';
import {
  PlusOutlined,
  DeleteOutlined,
  SaveOutlined,
  ArrowLeftOutlined
} from '@ant-design/icons';
import { useParams, useNavigate } from 'react-router-dom';
import dayjs from 'dayjs';
import apiService from '../../../services/api';

const { Title, Text } = Typography;
const { Option } = Select;
const { TextArea } = Input;
const { TabPane } = Tabs;

/**
 * 销售上报表单组件 - 门店维度
 * 主要上报每日各渠道销售额和充值/售卡金额
 * 每个销售渠道可以有多种支付方式
 */
const SalesReportForm = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const isEditing = !!id;

  // 状态管理
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [initialLoading, setInitialLoading] = useState(isEditing);
  const [stores, setStores] = useState([]);
  const [activeTab, setActiveTab] = useState('1');

  // 销售渠道相关状态
  const [channels, setChannels] = useState([]);
  const [channelSales, setChannelSales] = useState([]);

  // 充值/售卡相关状态
  const [rechargeSales, setRechargeSales] = useState([]);

  // 支付方式列表
  const [paymentMethodOptions, setPaymentMethodOptions] = useState([
    { value: 'cash', label: '现金' },
    { value: 'wechat', label: '微信支付' },
    { value: 'alipay', label: '支付宝' },
    { value: 'card', label: '刷卡' },
    { value: 'member', label: '会员余额' },
    { value: 'gift_card', label: '礼品卡' },
    { value: 'other', label: '其他' }
  ]);

  // 获取门店列表
  const fetchStores = async () => {
    try {
      const response = await apiService.project.store.getList();
      if (response && response.items) {
        setStores(response.items);
      }
    } catch (error) {
      console.error('获取门店列表失败:', error);
      message.error('获取门店列表失败');
    }
  };

  // 获取销售渠道列表
  const fetchChannels = async () => {
    try {
      const response = await apiService.project.channel.getList();
      if (response && response.items) {
        setChannels(response.items);

        // 如果没有渠道销售记录，添加默认的线下渠道
        if (channelSales.length === 0) {
          const offlineChannel = response.items.find(c => c.type === 'offline' || c.name.includes('线下') || c.name.includes('门店'));
          if (offlineChannel) {
            handleAddChannelSale(offlineChannel.id);
          } else if (response.items.length > 0) {
            handleAddChannelSale(response.items[0].id);
          }
        }
      }
    } catch (error) {
      console.error('获取渠道列表失败:', error);
      message.error('获取渠道列表失败');
    }
  };

  // 获取支付方式列表
  const fetchPaymentMethods = async () => {
    try {
      const response = await apiService.project.paymentMethod.getList();
      if (response && response.items) {
        // 转换为选项格式
        const options = response.items
          .filter(item => item.is_active)
          .map(item => ({
            value: item.code || item.name,
            label: item.name
          }));

        if (options.length > 0) {
          setPaymentMethodOptions(options);
        }
      }
    } catch (error) {
      console.error('获取支付方式列表失败:', error);
      // 使用默认支付方式
    }
  };

  // 获取销售上报详情
  const fetchReportDetail = async () => {
    if (!isEditing) return;

    setInitialLoading(true);
    try {
      const response = await apiService.project.salesReport.getDetail(id);
      if (response) {
        // 设置表单初始值
        form.setFieldsValue({
          store_id: response.store_id,
          report_date: response.report_date ? dayjs(response.report_date) : dayjs(),
          report_type: response.report_type || 'daily',
          total_orders: response.total_orders || 0,
          total_customers: response.total_customers || 0,
          notes: response.notes
        });

        // 设置渠道销售
        if (response.channel_sales && response.channel_sales.length > 0) {
          // 确保每个渠道销售记录都有支付方式数组
          const formattedChannelSales = response.channel_sales.map(channel => ({
            ...channel,
            payment_methods: channel.payment_methods || []
          }));
          setChannelSales(formattedChannelSales);
        }

        // 设置充值/售卡
        if (response.recharge_sales && response.recharge_sales.length > 0) {
          // 确保每个充值/售卡记录都有支付方式数组
          const formattedRechargeSales = response.recharge_sales.map(recharge => ({
            ...recharge,
            payment_methods: recharge.payment_methods || []
          }));
          setRechargeSales(formattedRechargeSales);
        }
      } else {
        message.error('获取销售上报详情失败');
        navigate('/project/store/sales-report');
      }
    } catch (error) {
      console.error('获取销售上报详情失败:', error);
      message.error('获取销售上报详情失败: ' + (error.response?.data?.detail || error.message));
      navigate('/project/store/sales-report');
    } finally {
      setInitialLoading(false);
    }
  };

  // 添加渠道销售记录
  const handleAddChannelSale = (channelId = '') => {
    const newChannelSale = {
      id: `temp_${Date.now()}`,
      channel_id: channelId,
      amount: 0,
      customer_count: 0,
      order_count: 0,
      payment_methods: [] // 初始化空的支付方式数组
    };
    setChannelSales([...channelSales, newChannelSale]);
  };

  // 删除渠道销售记录
  const handleDeleteChannelSale = (index) => {
    const newChannelSales = [...channelSales];
    newChannelSales.splice(index, 1);
    setChannelSales(newChannelSales);
  };

  // 更新渠道销售记录
  const handleUpdateChannelSale = (index, field, value) => {
    const newChannelSales = [...channelSales];
    newChannelSales[index][field] = value;
    setChannelSales(newChannelSales);
  };

  // 添加渠道销售支付方式
  const handleAddChannelPayment = (channelIndex) => {
    const newChannelSales = [...channelSales];
    if (!newChannelSales[channelIndex].payment_methods) {
      newChannelSales[channelIndex].payment_methods = [];
    }

    newChannelSales[channelIndex].payment_methods.push({
      id: `temp_${Date.now()}`,
      method: '',
      amount: 0
    });

    setChannelSales(newChannelSales);
  };

  // 删除渠道销售支付方式
  const handleDeleteChannelPayment = (channelIndex, paymentIndex) => {
    const newChannelSales = [...channelSales];
    newChannelSales[channelIndex].payment_methods.splice(paymentIndex, 1);
    setChannelSales(newChannelSales);
  };

  // 更新渠道销售支付方式
  const handleUpdateChannelPayment = (channelIndex, paymentIndex, field, value) => {
    const newChannelSales = [...channelSales];
    newChannelSales[channelIndex].payment_methods[paymentIndex][field] = value;
    setChannelSales(newChannelSales);
  };

  // 添加充值/售卡记录
  const handleAddRechargeSale = () => {
    const newRechargeSale = {
      id: `temp_${Date.now()}`,
      type: 'recharge', // recharge: 充值, card: 售卡
      amount: 0,
      count: 0,
      description: '',
      payment_methods: [] // 初始化空的支付方式数组
    };
    setRechargeSales([...rechargeSales, newRechargeSale]);
  };

  // 删除充值/售卡记录
  const handleDeleteRechargeSale = (index) => {
    const newRechargeSales = [...rechargeSales];
    newRechargeSales.splice(index, 1);
    setRechargeSales(newRechargeSales);
  };

  // 更新充值/售卡记录
  const handleUpdateRechargeSale = (index, field, value) => {
    const newRechargeSales = [...rechargeSales];
    newRechargeSales[index][field] = value;
    setRechargeSales(newRechargeSales);
  };

  // 添加充值/售卡支付方式
  const handleAddRechargePayment = (rechargeIndex) => {
    const newRechargeSales = [...rechargeSales];
    if (!newRechargeSales[rechargeIndex].payment_methods) {
      newRechargeSales[rechargeIndex].payment_methods = [];
    }

    newRechargeSales[rechargeIndex].payment_methods.push({
      id: `temp_${Date.now()}`,
      method: '',
      amount: 0
    });

    setRechargeSales(newRechargeSales);
  };

  // 删除充值/售卡支付方式
  const handleDeleteRechargePayment = (rechargeIndex, paymentIndex) => {
    const newRechargeSales = [...rechargeSales];
    newRechargeSales[rechargeIndex].payment_methods.splice(paymentIndex, 1);
    setRechargeSales(newRechargeSales);
  };

  // 更新充值/售卡支付方式
  const handleUpdateRechargePayment = (rechargeIndex, paymentIndex, field, value) => {
    const newRechargeSales = [...rechargeSales];
    newRechargeSales[rechargeIndex].payment_methods[paymentIndex][field] = value;
    setRechargeSales(newRechargeSales);
  };

  // 计算渠道销售总额
  const calculateChannelSalesTotal = () => {
    return channelSales.reduce((sum, item) => sum + parseFloat(item.amount || 0), 0);
  };

  // 计算充值/售卡总额
  const calculateRechargeSalesTotal = () => {
    return rechargeSales.reduce((sum, item) => sum + parseFloat(item.amount || 0), 0);
  };

  // 计算总销售额
  const calculateTotalSales = () => {
    const channelTotal = calculateChannelSalesTotal();
    const rechargeTotal = calculateRechargeSalesTotal();
    return channelTotal + rechargeTotal;
  };

  // 计算总订单数
  const calculateTotalOrders = () => {
    return channelSales.reduce((sum, item) => sum + parseInt(item.order_count || 0), 0);
  };

  // 计算总客流数
  const calculateTotalCustomers = () => {
    // 如果没有填写客流数，则使用订单数作为客流数
    return channelSales.reduce((sum, item) => {
      const customerCount = parseInt(item.customer_count || 0);
      return sum + (customerCount > 0 ? customerCount : parseInt(item.order_count || 0));
    }, 0);
  };

  // 验证支付方式总额是否等于销售/充值总额
  const validatePaymentTotal = (amount, paymentMethods) => {
    if (!paymentMethods || paymentMethods.length === 0) {
      return false;
    }

    const totalAmount = parseFloat(amount || 0);
    const paymentsTotal = paymentMethods.reduce(
      (sum, payment) => sum + parseFloat(payment.amount || 0),
      0
    );

    // 允许0.01的误差
    return Math.abs(totalAmount - paymentsTotal) < 0.01;
  };

  // 提交表单
  const handleSubmit = async () => {
    try {
      const values = await form.validateFields();

      // 检查渠道销售
      if (channelSales.length === 0) {
        message.warning('请至少添加一个销售渠道');
        return;
      }

      // 检查每个渠道销售的支付方式
      for (let i = 0; i < channelSales.length; i++) {
        const channel = channelSales[i];
        if (!channel.channel_id) {
          message.warning(`请选择第 ${i + 1} 个销售渠道`);
          return;
        }

        if (parseFloat(channel.amount || 0) <= 0) {
          message.warning(`请输入第 ${i + 1} 个销售渠道的销售金额`);
          return;
        }

        // 检查支付方式
        if (!channel.payment_methods || channel.payment_methods.length === 0) {
          message.warning(`请为第 ${i + 1} 个销售渠道添加支付方式`);
          return;
        }

        // 检查支付方式总额是否等于渠道销售总额
        if (!validatePaymentTotal(channel.amount, channel.payment_methods)) {
          const channelAmount = parseFloat(channel.amount || 0);
          const paymentsTotal = channel.payment_methods.reduce(
            (sum, payment) => sum + parseFloat(payment.amount || 0),
            0
          );
          message.warning(`第 ${i + 1} 个销售渠道的支付方式总额(${paymentsTotal.toFixed(2)})与销售金额(${channelAmount.toFixed(2)})不一致`);
          return;
        }
      }

      // 检查充值/售卡的支付方式
      for (let i = 0; i < rechargeSales.length; i++) {
        const recharge = rechargeSales[i];
        if (parseFloat(recharge.amount || 0) <= 0) {
          continue; // 跳过金额为0的充值/售卡
        }

        // 检查支付方式
        if (!recharge.payment_methods || recharge.payment_methods.length === 0) {
          message.warning(`请为第 ${i + 1} 个充值/售卡添加支付方式`);
          return;
        }

        // 检查支付方式总额是否等于充值/售卡总额
        if (!validatePaymentTotal(recharge.amount, recharge.payment_methods)) {
          const rechargeAmount = parseFloat(recharge.amount || 0);
          const paymentsTotal = recharge.payment_methods.reduce(
            (sum, payment) => sum + parseFloat(payment.amount || 0),
            0
          );
          message.warning(`第 ${i + 1} 个充值/售卡的支付方式总额(${paymentsTotal.toFixed(2)})与金额(${rechargeAmount.toFixed(2)})不一致`);
          return;
        }
      }

      setLoading(true);

      // 计算最新的订单总数和客流总数
      const totalOrders = calculateTotalOrders();
      const totalCustomers = calculateTotalCustomers();

      // 更新表单值以确保使用最新计算的值
      form.setFieldsValue({
        total_orders: totalOrders,
        total_customers: totalCustomers
      });

      // 获取更新后的表单值
      const updatedValues = form.getFieldsValue();

      // 准备提交数据
      const reportData = {
        ...updatedValues,
        report_date: updatedValues.report_date ? updatedValues.report_date.format('YYYY-MM-DD') : dayjs().format('YYYY-MM-DD'),
        total_sales: calculateTotalSales(),
        total_orders: totalOrders,
        total_customers: totalCustomers,
        online_sales: channelSales
          .filter(channel => {
            const channelObj = channels.find(c => c.id === channel.channel_id);
            return channelObj && (channelObj.type === 'online' || channelObj.name.includes('线上'));
          })
          .reduce((sum, channel) => sum + parseFloat(channel.amount || 0), 0),
        offline_sales: channelSales
          .filter(channel => {
            const channelObj = channels.find(c => c.id === channel.channel_id);
            return !channelObj || channelObj.type !== 'online';
          })
          .reduce((sum, channel) => sum + parseFloat(channel.amount || 0), 0),
        channel_sales: channelSales.map(item => ({
          channel_id: item.channel_id,
          amount: parseFloat(item.amount || 0),
          customer_count: parseInt(item.customer_count || 0),
          order_count: parseInt(item.order_count || 0),
          payment_methods: (item.payment_methods || []).map(payment => ({
            method: payment.method,
            amount: parseFloat(payment.amount || 0)
          }))
        })),
        recharge_sales: rechargeSales.map(item => ({
          type: item.type,
          amount: parseFloat(item.amount || 0),
          count: parseInt(item.count || 0),
          description: item.description || '',
          payment_methods: (item.payment_methods || []).map(payment => ({
            method: payment.method,
            amount: parseFloat(payment.amount || 0)
          }))
        })),
        // 添加空的items数组以满足后端schema要求
        items: []
      };

      if (isEditing) {
        // 更新销售上报
        await apiService.project.salesReport.update(id, reportData);
        message.success('销售上报更新成功');
      } else {
        // 创建销售上报
        await apiService.project.salesReport.create(reportData);
        message.success('销售上报创建成功');
      }

      navigate('/project/store/sales-report');
    } catch (error) {
      console.error('提交销售上报失败:', error);
      if (error.errorFields) {
        // 表单验证错误
        return;
      }
      message.error('提交销售上报失败: ' + (error.response?.data?.detail || error.message));
    } finally {
      setLoading(false);
    }
  };

  // 返回列表
  const handleBack = () => {
    navigate('/project/store/sales-report');
  };

  // 初始化
  useEffect(() => {
    fetchStores();
    fetchChannels();
    fetchPaymentMethods();

    if (isEditing) {
      fetchReportDetail();
    } else {
      // 设置默认值
      const today = dayjs();
      form.setFieldsValue({
        report_date: today,
        report_type: 'daily',
        total_orders: 0,
        total_customers: 0
      });
    }
  }, []);

  // 当渠道销售变化时，自动更新订单总数和客流总数
  useEffect(() => {
    if (!isEditing && channelSales.length > 0) {
      const totalOrders = calculateTotalOrders();
      const totalCustomers = calculateTotalCustomers();

      form.setFieldsValue({
        total_orders: totalOrders,
        total_customers: totalCustomers
      });
    }
  }, [channelSales]);

  if (initialLoading) {
    return (
      <div style={{ textAlign: 'center', padding: '50px' }}>
        <Title level={4}>{isEditing ? '加载销售上报...' : '创建销售上报'}</Title>
      </div>
    );
  }

  return (
    <div className="sales-report-form-page">
      <Card>
        <div style={{ marginBottom: 16 }}>
          <Space>
            <Button icon={<ArrowLeftOutlined />} onClick={handleBack}>返回</Button>
            <Button
              type="primary"
              icon={<SaveOutlined />}
              onClick={handleSubmit}
              loading={loading}
            >
              保存
            </Button>
          </Space>
        </div>

        <Title level={4}>{isEditing ? '编辑销售上报' : '创建销售上报'}</Title>

        <Form
          form={form}
          layout="vertical"
        >
          <Row gutter={16}>
            <Col span={8}>
              <Form.Item
                name="store_id"
                label="门店"
                rules={[{ required: true, message: '请选择门店' }]}
              >
                <Select
                  placeholder="请选择门店"
                  showSearch
                  optionFilterProp="children"
                  filterOption={(input, option) =>
                    option.children.toLowerCase().indexOf(input.toLowerCase()) >= 0
                  }
                >
                  {stores.map(store => (
                    <Option key={store.id} value={store.id}>{store.name}</Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>

            <Col span={8}>
              <Form.Item
                name="report_date"
                label="上报日期"
                rules={[{ required: true, message: '请选择上报日期' }]}
                initialValue={dayjs()}
              >
                <DatePicker
                  style={{ width: '100%' }}
                  format="YYYY-MM-DD"
                  disabledDate={(current) => current && current > dayjs().endOf('day')}
                  getPopupContainer={(trigger) => trigger.parentElement}
                  allowClear={false}
                  onChange={(date) => {
                    if (date) {
                      // 确保只有日期部分
                      const dateOnly = date.startOf('day');
                      form.setFieldsValue({ report_date: dateOnly });
                    }
                  }}
                />
              </Form.Item>
            </Col>

            <Col span={8}>
              <Form.Item
                name="report_type"
                label="上报类型"
                rules={[{ required: true, message: '请选择上报类型' }]}
              >
                <Select placeholder="请选择上报类型">
                  <Option value="shift">班次</Option>
                  <Option value="daily">日报</Option>
                  <Option value="weekly">周报</Option>
                  <Option value="monthly">月报</Option>
                </Select>
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="total_orders"
                label="订单总数"
                rules={[{ required: true, message: '请输入订单总数' }]}
              >
                <InputNumber
                  min={0}
                  style={{ width: '100%' }}
                />
              </Form.Item>
            </Col>

            <Col span={12}>
              <Form.Item
                name="total_customers"
                label="顾客总数"
                rules={[{ required: true, message: '请输入顾客总数' }]}
              >
                <InputNumber
                  min={0}
                  style={{ width: '100%' }}
                />
              </Form.Item>
            </Col>
          </Row>

          <Tabs activeKey={activeTab} onChange={setActiveTab}>
            <TabPane tab="渠道销售" key="1">
              <div style={{ marginBottom: 16 }}>
                <Button
                  type="primary"
                  icon={<PlusOutlined />}
                  onClick={() => handleAddChannelSale()}
                >
                  添加渠道销售
                </Button>
              </div>

              {channelSales.map((channel, channelIndex) => (
                <Card
                  key={channel.id}
                  style={{ marginBottom: 16 }}
                  title={
                    <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                      <span>渠道销售 #{channelIndex + 1}</span>
                      <Button
                        type="text"
                        danger
                        icon={<DeleteOutlined />}
                        onClick={() => handleDeleteChannelSale(channelIndex)}
                        disabled={channelSales.length <= 1}
                      />
                    </div>
                  }
                >
                  <Row gutter={16}>
                    <Col span={8}>
                      <Form.Item
                        label="销售渠道"
                        required
                      >
                        <Select
                          style={{ width: '100%' }}
                          value={channel.channel_id}
                          onChange={(value) => handleUpdateChannelSale(channelIndex, 'channel_id', value)}
                          placeholder="请选择销售渠道"
                        >
                          {channels.map(c => (
                            <Option key={c.id} value={c.id}>{c.name}</Option>
                          ))}
                        </Select>
                      </Form.Item>
                    </Col>
                    <Col span={8}>
                      <Form.Item
                        label="销售金额"
                        required
                      >
                        <InputNumber
                          style={{ width: '100%' }}
                          value={channel.amount}
                          onChange={(value) => handleUpdateChannelSale(channelIndex, 'amount', value)}
                          min={0}
                          precision={2}
                          prefix="¥"
                        />
                      </Form.Item>
                    </Col>
                    <Col span={8}>
                      <Form.Item
                        label="订单数"
                      >
                        <InputNumber
                          style={{ width: '100%' }}
                          value={channel.order_count}
                          onChange={(value) => handleUpdateChannelSale(channelIndex, 'order_count', value)}
                          min={0}
                          precision={0}
                        />
                      </Form.Item>
                    </Col>
                  </Row>

                  <Divider orientation="left">支付方式</Divider>

                  <div style={{ marginBottom: 16 }}>
                    <Button
                      type="primary"
                      icon={<PlusOutlined />}
                      onClick={() => handleAddChannelPayment(channelIndex)}
                    >
                      添加支付方式
                    </Button>
                  </div>

                  <Table
                    dataSource={channel.payment_methods || []}
                    columns={[
                      {
                        title: '支付方式',
                        dataIndex: 'method',
                        key: 'method',
                        render: (text, _, index) => (
                          <Select
                            style={{ width: '100%' }}
                            value={text}
                            onChange={(value) => handleUpdateChannelPayment(channelIndex, index, 'method', value)}
                            placeholder="请选择支付方式"
                          >
                            {paymentMethodOptions.map(option => (
                              <Option key={option.value} value={option.value}>{option.label}</Option>
                            ))}
                          </Select>
                        )
                      },
                      {
                        title: '金额',
                        dataIndex: 'amount',
                        key: 'amount',
                        render: (text, _, index) => (
                          <InputNumber
                            style={{ width: '100%' }}
                            value={text}
                            onChange={(value) => handleUpdateChannelPayment(channelIndex, index, 'amount', value)}
                            min={0}
                            precision={2}
                            prefix="¥"
                          />
                        )
                      },
                      {
                        title: '操作',
                        key: 'action',
                        render: (_, __, index) => (
                          <Button
                            type="text"
                            danger
                            icon={<DeleteOutlined />}
                            onClick={() => handleDeleteChannelPayment(channelIndex, index)}
                          />
                        )
                      }
                    ]}
                    rowKey="id"
                    pagination={false}
                    summary={() => {
                      const paymentsTotal = (channel.payment_methods || []).reduce(
                        (sum, payment) => sum + parseFloat(payment.amount || 0),
                        0
                      );
                      const channelAmount = parseFloat(channel.amount || 0);
                      const isValid = Math.abs(channelAmount - paymentsTotal) < 0.01;

                      return (
                        <Table.Summary fixed>
                          <Table.Summary.Row>
                            <Table.Summary.Cell index={0} align="right">
                              <Text strong>总计:</Text>
                            </Table.Summary.Cell>
                            <Table.Summary.Cell index={1}>
                              <Text
                                strong
                                type={isValid ? 'success' : 'danger'}
                                style={{ display: 'flex', alignItems: 'center' }}
                              >
                                ¥{paymentsTotal.toFixed(2)}
                                {!isValid && (
                                  <span style={{ marginLeft: 8, color: '#ff4d4f' }}>
                                    (与销售金额不一致)
                                  </span>
                                )}
                              </Text>
                            </Table.Summary.Cell>
                            <Table.Summary.Cell index={2} />
                          </Table.Summary.Row>
                        </Table.Summary>
                      );
                    }}
                  />
                </Card>
              ))}
            </TabPane>

            <TabPane tab="充值/售卡" key="2">
              <div style={{ marginBottom: 16 }}>
                <Button
                  type="primary"
                  icon={<PlusOutlined />}
                  onClick={handleAddRechargeSale}
                >
                  添加充值/售卡
                </Button>
              </div>

              {rechargeSales.map((recharge, rechargeIndex) => (
                <Card
                  key={recharge.id}
                  style={{ marginBottom: 16 }}
                  title={
                    <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                      <span>
                        {recharge.type === 'recharge' ? '会员充值' : '售卡'} #{rechargeIndex + 1}
                      </span>
                      <Button
                        type="text"
                        danger
                        icon={<DeleteOutlined />}
                        onClick={() => handleDeleteRechargeSale(rechargeIndex)}
                      />
                    </div>
                  }
                >
                  <Row gutter={16}>
                    <Col span={6}>
                      <Form.Item
                        label="类型"
                        required
                      >
                        <Select
                          style={{ width: '100%' }}
                          value={recharge.type}
                          onChange={(value) => handleUpdateRechargeSale(rechargeIndex, 'type', value)}
                        >
                          <Option value="recharge">会员充值</Option>
                          <Option value="card">售卡</Option>
                        </Select>
                      </Form.Item>
                    </Col>
                    <Col span={6}>
                      <Form.Item
                        label="金额"
                        required
                      >
                        <InputNumber
                          style={{ width: '100%' }}
                          value={recharge.amount}
                          onChange={(value) => handleUpdateRechargeSale(rechargeIndex, 'amount', value)}
                          min={0}
                          precision={2}
                          prefix="¥"
                        />
                      </Form.Item>
                    </Col>
                    <Col span={6}>
                      <Form.Item
                        label="数量"
                      >
                        <InputNumber
                          style={{ width: '100%' }}
                          value={recharge.count}
                          onChange={(value) => handleUpdateRechargeSale(rechargeIndex, 'count', value)}
                          min={0}
                          precision={0}
                        />
                      </Form.Item>
                    </Col>
                    <Col span={6}>
                      <Form.Item
                        label="描述"
                      >
                        <Input
                          value={recharge.description}
                          onChange={(e) => handleUpdateRechargeSale(rechargeIndex, 'description', e.target.value)}
                          placeholder="请输入描述"
                        />
                      </Form.Item>
                    </Col>
                  </Row>

                  <Divider orientation="left">支付方式</Divider>

                  <div style={{ marginBottom: 16 }}>
                    <Button
                      type="primary"
                      icon={<PlusOutlined />}
                      onClick={() => handleAddRechargePayment(rechargeIndex)}
                    >
                      添加支付方式
                    </Button>
                  </div>

                  <Table
                    dataSource={recharge.payment_methods || []}
                    columns={[
                      {
                        title: '支付方式',
                        dataIndex: 'method',
                        key: 'method',
                        render: (text, _, index) => (
                          <Select
                            style={{ width: '100%' }}
                            value={text}
                            onChange={(value) => handleUpdateRechargePayment(rechargeIndex, index, 'method', value)}
                            placeholder="请选择支付方式"
                          >
                            {paymentMethodOptions.map(option => (
                              <Option key={option.value} value={option.value}>{option.label}</Option>
                            ))}
                          </Select>
                        )
                      },
                      {
                        title: '金额',
                        dataIndex: 'amount',
                        key: 'amount',
                        render: (text, _, index) => (
                          <InputNumber
                            style={{ width: '100%' }}
                            value={text}
                            onChange={(value) => handleUpdateRechargePayment(rechargeIndex, index, 'amount', value)}
                            min={0}
                            precision={2}
                            prefix="¥"
                          />
                        )
                      },
                      {
                        title: '操作',
                        key: 'action',
                        render: (_, __, index) => (
                          <Button
                            type="text"
                            danger
                            icon={<DeleteOutlined />}
                            onClick={() => handleDeleteRechargePayment(rechargeIndex, index)}
                          />
                        )
                      }
                    ]}
                    rowKey="id"
                    pagination={false}
                    summary={() => {
                      const paymentsTotal = (recharge.payment_methods || []).reduce(
                        (sum, payment) => sum + parseFloat(payment.amount || 0),
                        0
                      );
                      const rechargeAmount = parseFloat(recharge.amount || 0);
                      const isValid = Math.abs(rechargeAmount - paymentsTotal) < 0.01;

                      return (
                        <Table.Summary fixed>
                          <Table.Summary.Row>
                            <Table.Summary.Cell index={0} align="right">
                              <Text strong>总计:</Text>
                            </Table.Summary.Cell>
                            <Table.Summary.Cell index={1}>
                              <Text
                                strong
                                type={isValid ? 'success' : 'danger'}
                                style={{ display: 'flex', alignItems: 'center' }}
                              >
                                ¥{paymentsTotal.toFixed(2)}
                                {!isValid && (
                                  <span style={{ marginLeft: 8, color: '#ff4d4f' }}>
                                    (与充值金额不一致)
                                  </span>
                                )}
                              </Text>
                            </Table.Summary.Cell>
                            <Table.Summary.Cell index={2} />
                          </Table.Summary.Row>
                        </Table.Summary>
                      );
                    }}
                  />
                </Card>
              ))}
            </TabPane>
          </Tabs>

          <Divider />

          <Form.Item
            name="notes"
            label="备注"
          >
            <TextArea rows={4} placeholder="请输入备注信息" />
          </Form.Item>

          <div style={{ marginTop: 16, textAlign: 'right' }}>
            <Statistic
              title="销售总额"
              value={calculateTotalSales()}
              precision={2}
              prefix="¥"
              style={{ display: 'inline-block' }}
            />
          </div>

          <Divider />

          <div style={{ marginTop: 16, textAlign: 'center' }}>
            <Space size="large">
              <Button onClick={handleBack}>取消</Button>
              <Button
                type="primary"
                icon={<SaveOutlined />}
                onClick={handleSubmit}
                loading={loading}
              >
                保存
              </Button>
            </Space>
          </div>
        </Form>
      </Card>
    </div>
  );
};

export default SalesReportForm;
