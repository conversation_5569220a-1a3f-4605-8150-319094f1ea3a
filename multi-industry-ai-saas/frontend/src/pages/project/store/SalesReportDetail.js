import React, { useState, useEffect } from 'react';
import {
  Card,
  Button,
  Space,
  Tag,
  Typography,
  Descriptions,
  Table,
  Divider,
  message,
  Spin,
  Empty,
  Tabs,
  Modal,
  Form,
  Input,
  DatePicker,
  Select,
  Row,
  Col,
  Statistic,
  Badge,
  Tooltip
} from 'antd';
import {
  ArrowLeftOutlined,
  EditOutlined,
  DeleteOutlined,
  CheckCircleOutlined,
  CloseCircleOutlined,
  RollbackOutlined,
  SendOutlined
} from '@ant-design/icons';
import { useParams, useNavigate } from 'react-router-dom';
import dayjs from "dayjs";
import apiService from '../../../services/api';
import dateTimeUtils from '../../../utils/dateTimeUtils';

const { Title, Text } = Typography;
const { TabPane } = Tabs;
const { Option } = Select;

// 状态标签颜色映射
const statusColors = {
  draft: 'default',
  submitted: 'processing',
  approved: 'success',
  rejected: 'error'
};

// 状态文本映射
const statusTexts = {
  draft: '草稿',
  submitted: '已提交',
  approved: '已通过',
  rejected: '已拒绝'
};

// 上报类型映射
const reportTypeTexts = {
  shift: '班次',
  daily: '日报',
  weekly: '周报',
  monthly: '月报'
};

const SalesReportDetail = () => {
  const { id } = useParams();
  const navigate = useNavigate();

  // 状态管理
  const [loading, setLoading] = useState(true);
  const [report, setReport] = useState(null);
  const [activeTab, setActiveTab] = useState('1');
  const [submitModalVisible, setSubmitModalVisible] = useState(false);
  const [withdrawModalVisible, setWithdrawModalVisible] = useState(false);
  const [statusForm] = Form.useForm();
  const [paymentMethods, setPaymentMethods] = useState({});
  const [channels, setChannels] = useState([]);

  // 获取销售上报详情
  const fetchReportDetail = async () => {
    setLoading(true);
    try {
      const response = await apiService.project.salesReport.getDetail(id);
      if (response) {
        // 确保充值/售卡数据正确显示
        if (response.recharge_payment_methods &&
            Object.keys(response.recharge_payment_methods).length > 0 &&
            (!response.recharge_amount || response.recharge_amount === 0)) {
          // 如果有充值支付方式但没有充值金额，计算充值金额
          const totalRechargeAmount = Object.values(response.recharge_payment_methods)
            .reduce((sum, amount) => sum + parseFloat(amount || 0), 0);

          if (totalRechargeAmount > 0) {
            response.recharge_amount = totalRechargeAmount;
            response.recharge_count = response.recharge_count || 1;
          }
        }

        // 确保 channel_sales 字段存在
        if (!response.channel_sales) {
          response.channel_sales = {};
        }

        // 设置报告数据
        setReport(response);
      } else {
        message.error('获取销售上报详情失败');
      }
    } catch (error) {
      console.error('获取销售上报详情失败:', error);
      message.error('获取销售上报详情失败: ' + (error.response?.data?.detail || error.message));
    } finally {
      setLoading(false);
    }
  };

  // 返回列表
  const handleBack = () => {
    navigate('/project/store/sales-report');
  };

  // 编辑销售上报
  const handleEdit = () => {
    navigate(`/project/store/sales-report/edit/${id}`);
  };

  // 提交销售上报
  const handleSubmit = async () => {
    setLoading(true);
    try {
      await apiService.project.salesReport.updateStatus(id, { status: 'submitted' });
      message.success('销售上报提交成功');
      setSubmitModalVisible(false);
      fetchReportDetail();
    } catch (error) {
      console.error('提交销售上报失败:', error);
      message.error('提交销售上报失败: ' + (error.response?.data?.detail || error.message));
    } finally {
      setLoading(false);
    }
  };

  // 获取支付方式列表
  const fetchPaymentMethods = async () => {
    try {
      const response = await apiService.project.paymentMethod.getList();
      if (response && response.items) {
        // 转换为 code -> name 的映射
        const methodMap = {};
        response.items.forEach(method => {
          methodMap[method.code] = method.name;
        });
        setPaymentMethods(methodMap);
      }
    } catch (error) {
      console.error('获取支付方式列表失败:', error);
    }
  };

  // 撤回销售上报
  const handleWithdraw = async () => {
    setLoading(true);
    try {
      await apiService.project.salesReport.updateStatus(id, { status: 'draft' });
      message.success('销售上报撤回成功');
      setWithdrawModalVisible(false);
      fetchReportDetail();
    } catch (error) {
      console.error('撤回销售上报失败:', error);
      message.error('撤回销售上报失败: ' + (error.response?.data?.detail || error.message));
    } finally {
      setLoading(false);
    }
  };

  // 获取销售渠道列表
  const fetchChannels = async () => {
    try {
      const response = await apiService.project.channel.getList();
      if (response && response.items) {
        setChannels(response.items);
      }
    } catch (error) {
      console.error('获取渠道列表失败:', error);
    }
  };

  // 初始化
  useEffect(() => {
    // 初始化日期时间工具
    dateTimeUtils.initDateTimeUtils();

    // 获取支付方式列表
    fetchPaymentMethods();

    // 获取渠道列表
    fetchChannels();

    if (id) {
      fetchReportDetail();
    }
  }, [id]);

  // 销售上报项表格列定义
  const itemColumns = [
    {
      title: '产品名称',
      dataIndex: 'product_name',
      key: 'product_name',
    },
    {
      title: '产品编码',
      dataIndex: 'product_code',
      key: 'product_code',
    },
    {
      title: '产品类别',
      dataIndex: 'product_category',
      key: 'product_category',
    },
    {
      title: '单位',
      dataIndex: 'product_unit',
      key: 'product_unit',
    },
    {
      title: '数量',
      dataIndex: 'quantity',
      key: 'quantity',
    },
    {
      title: '单价',
      dataIndex: 'unit_price',
      key: 'unit_price',
      render: (text) => `¥${parseFloat(text || 0).toFixed(2)}`,
    },
    {
      title: '折扣金额',
      dataIndex: 'discount_amount',
      key: 'discount_amount',
      render: (text) => `¥${parseFloat(text || 0).toFixed(2)}`,
    },
    {
      title: '总金额',
      dataIndex: 'total_amount',
      key: 'total_amount',
      render: (text) => `¥${parseFloat(text || 0).toFixed(2)}`,
    }
  ];

  if (loading) {
    return (
      <div style={{ textAlign: 'center', padding: '50px' }}>
        <Spin size="large" />
      </div>
    );
  }

  if (!report) {
    return (
      <Empty
        description="销售上报不存在或已被删除"
        style={{ margin: '50px 0' }}
      >
        <Button type="primary" onClick={handleBack}>返回列表</Button>
      </Empty>
    );
  }

  // 获取支付方式名称
  const getPaymentMethodName = (method) => {
    // 如果在后端获取的支付方式中找到，则使用后端的名称
    if (paymentMethods[method]) {
      return paymentMethods[method];
    }
    // 否则直接返回方法代码
    return method;
  };

  // 渲染支付方式统计 (销售+充值/售卡的总数)
  const renderPaymentMethods = () => {
    if (!report.payment_methods) return <Empty description="暂无支付方式数据" />;

    // 使用后端返回的合并后的支付方式数据
    let paymentData = [];

    // 如果后端已经返回了合并后的数据，直接使用
    if (Array.isArray(report.payment_methods)) {
      paymentData = report.payment_methods;
    }
    // 如果后端返回的是原始格式，需要在前端处理
    else {
      // 合并销售和充值/售卡的支付方式数据
      const combinedPaymentMethods = {};

      // 处理销售支付方式
      if (report.raw_payment_methods && typeof report.raw_payment_methods === 'object') {
        // 计算总销售额（线上+线下）
        const totalSalesAmount = parseFloat(report.online_sales || 0) + parseFloat(report.offline_sales || 0);

        // 如果有支付方式数据
        if (Object.keys(report.raw_payment_methods).length > 0) {
          // 获取总销售额用于每个支付方式
          Object.entries(report.raw_payment_methods).forEach(([method, amount]) => {
            const methodName = getPaymentMethodName(method);

            // 使用总销售额作为销售金额
            const salesAmount = totalSalesAmount;

            combinedPaymentMethods[method] = {
              method_id: method,
              method_name: methodName,
              sales_amount: salesAmount,
              recharge_amount: 0,
              total_amount: salesAmount
            };
          });
        } else {
          // 如果没有支付方式数据但有销售额，添加默认支付方式
          if (totalSalesAmount > 0) {
            combinedPaymentMethods['default'] = {
              method_id: 'default',
              method_name: '其他',
              sales_amount: totalSalesAmount,
              recharge_amount: 0,
              total_amount: totalSalesAmount
            };
          }
        }
      }

      // 处理充值/售卡支付方式
      if (report.recharge_payment_methods && typeof report.recharge_payment_methods === 'object') {
        Object.entries(report.recharge_payment_methods).forEach(([method, amount]) => {
          // 直接使用支付方式代码作为键
          const paymentMethod = method;

          // 获取支付方式的显示名称
          const methodName = getPaymentMethodName(paymentMethod);

          if (!combinedPaymentMethods[paymentMethod]) {
            combinedPaymentMethods[paymentMethod] = {
              method_id: paymentMethod,
              method_name: methodName,
              sales_amount: 0,
              recharge_amount: parseFloat(amount || 0),
              total_amount: parseFloat(amount || 0)
            };
          } else {
            combinedPaymentMethods[paymentMethod].recharge_amount += parseFloat(amount || 0);
            combinedPaymentMethods[paymentMethod].total_amount += parseFloat(amount || 0);
          }
        });
      }

      // 转换为数组
      paymentData = Object.values(combinedPaymentMethods);
    }

    // 如果没有支付方式数据，添加一个默认条目
    if (paymentData.length === 0) {
      // 检查是否有销售额
      if (report.offline_sales > 0 || report.online_sales > 0) {
        paymentData.push({
          method_id: 'default',
          method_name: '其他',
          sales_amount: parseFloat(report.offline_sales || 0) + parseFloat(report.online_sales || 0),
          recharge_amount: 0,
          total_amount: parseFloat(report.offline_sales || 0) + parseFloat(report.online_sales || 0)
        });
      }

      // 检查是否有充值/售卡金额
      if (report.recharge_amount > 0) {
        paymentData.push({
          method_id: 'recharge',
          method_name: '会员充值',
          sales_amount: 0,
          recharge_amount: parseFloat(report.recharge_amount || 0),
          total_amount: parseFloat(report.recharge_amount || 0)
        });
      }

      if (report.card_sales_amount > 0) {
        paymentData.push({
          method_id: 'card',
          method_name: '储值卡',
          sales_amount: 0,
          recharge_amount: parseFloat(report.card_sales_amount || 0),
          total_amount: parseFloat(report.card_sales_amount || 0)
        });
      }
    }

    const columns = [
      {
        title: '支付方式',
        dataIndex: 'method_name',
        key: 'method_name',
      },
      {
        title: '销售金额',
        dataIndex: 'sales_amount',
        key: 'sales_amount',
        render: (text) => {
          const value = parseFloat(text || 0);
          return isNaN(value) ? '¥0.00' : `¥${value.toFixed(2)}`;
        }
      },
      {
        title: '充值/售卡金额',
        dataIndex: 'recharge_amount',
        key: 'recharge_amount',
        render: (text) => {
          const value = parseFloat(text || 0);
          return isNaN(value) ? '¥0.00' : `¥${value.toFixed(2)}`;
        }
      },
      {
        title: '总金额',
        dataIndex: 'total_amount',
        key: 'total_amount',
        render: (text) => {
          const value = parseFloat(text || 0);
          return isNaN(value) ? '¥0.00' : `¥${value.toFixed(2)}`;
        }
      }
    ];

    return (
      <Table
        columns={columns}
        dataSource={paymentData}
        rowKey="method_id"
        pagination={false}
        summary={() => {
          // 计算总和，确保不会出现 NaN
          const salesTotal = paymentData.reduce((sum, item) => {
            const value = parseFloat(item.sales_amount || 0);
            return sum + (isNaN(value) ? 0 : value);
          }, 0);

          const rechargeTotal = paymentData.reduce((sum, item) => {
            const value = parseFloat(item.recharge_amount || 0);
            return sum + (isNaN(value) ? 0 : value);
          }, 0);

          // 总金额应该是销售金额和充值/售卡金额的总和
          const grandTotal = salesTotal + rechargeTotal;

          return (
            <Table.Summary.Row>
              <Table.Summary.Cell index={0}>
                <Text strong>总计</Text>
              </Table.Summary.Cell>
              <Table.Summary.Cell index={1}>
                <Text strong>
                  ¥{salesTotal.toFixed(2)}
                </Text>
              </Table.Summary.Cell>
              <Table.Summary.Cell index={2}>
                <Text strong>
                  ¥{rechargeTotal.toFixed(2)}
                </Text>
              </Table.Summary.Cell>
              <Table.Summary.Cell index={3}>
                <Text strong>
                  ¥{grandTotal.toFixed(2)}
                </Text>
              </Table.Summary.Cell>
            </Table.Summary.Row>
          );
        }}
      />
    );
  };



  return (
    <div className="sales-report-detail-page">
      <Card>
        <div style={{ marginBottom: 16 }}>
          <Space>
            <Button icon={<ArrowLeftOutlined />} onClick={handleBack}>返回</Button>
            {report.status === 'draft' && (
              <>
                <Button type="primary" icon={<EditOutlined />} onClick={handleEdit}>编辑</Button>
                <Button
                  type="primary"
                  icon={<SendOutlined />}
                  onClick={() => setSubmitModalVisible(true)}
                >
                  提交
                </Button>
              </>
            )}
            {report.status === 'submitted' && (
              <Button
                icon={<RollbackOutlined />}
                onClick={() => setWithdrawModalVisible(true)}
              >
                撤回
              </Button>
            )}
          </Space>
        </div>

        <div className="report-header" style={{ marginBottom: 24 }}>
          <Title level={4}>销售上报详情</Title>
          <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            <Text strong>上报日期: {dateTimeUtils.formatDate(report.report_date)}</Text>
            <Space>
              <Tag color={statusColors[report.status] || 'default'}>
                {statusTexts[report.status] || report.status}
              </Tag>
              <Tag color="blue">
                {reportTypeTexts[report.report_type] || report.report_type}
              </Tag>
            </Space>
          </div>
        </div>

        <Row gutter={16} style={{ marginBottom: 24 }}>
          <Col span={6}>
            <Card>
              <Statistic
                title="门店"
                value={report.store_name || '未指定'}
                valueStyle={{ fontSize: '16px' }}
              />
            </Card>
          </Col>
          <Col span={6}>
            <Card>
              <Statistic
                title="销售总额"
                value={parseFloat(report.total_sales || 0).toFixed(2)}
                prefix="¥"
                precision={2}
              />
            </Card>
          </Col>
          <Col span={6}>
            <Card>
              <Statistic
                title="订单数"
                value={report.total_orders || 0}
                suffix="单"
              />
            </Card>
          </Col>
          <Col span={6}>
            <Card>
              <Statistic
                title="充值售卡总额"
                value={parseFloat((report.recharge_amount || 0) + (report.card_sales_amount || 0)).toFixed(2)}
                prefix="¥"
                precision={2}
              />
            </Card>
          </Col>
        </Row>

        <Tabs activeKey={activeTab} onChange={setActiveTab}>
          <TabPane tab="基本信息" key="1">
            <Descriptions bordered column={2}>
              <Descriptions.Item label="上报类型">
                {reportTypeTexts[report.report_type] || report.report_type}
              </Descriptions.Item>
              <Descriptions.Item label="上报日期">
                {dateTimeUtils.formatDate(report.report_date)}
              </Descriptions.Item>
              <Descriptions.Item label="线上销售额">
                ¥{parseFloat(report.online_sales || 0).toFixed(2)}
              </Descriptions.Item>
              <Descriptions.Item label="线下销售额">
                ¥{parseFloat(report.offline_sales || 0).toFixed(2)}
              </Descriptions.Item>
              <Descriptions.Item label="会员充值金额">
                ¥{parseFloat(report.recharge_amount || 0).toFixed(2)}
              </Descriptions.Item>
              <Descriptions.Item label="储值卡销售金额">
                ¥{parseFloat(report.card_sales_amount || 0).toFixed(2)}
              </Descriptions.Item>
              <Descriptions.Item label="创建人">
                {report.creator_name || '-'}
              </Descriptions.Item>
              <Descriptions.Item label="创建时间">
                {report.created_at ? dateTimeUtils.formatDateTime(report.created_at) : '-'}
              </Descriptions.Item>
              <Descriptions.Item label="审核人">
                {report.approver_name || '-'}
              </Descriptions.Item>
              <Descriptions.Item label="审核时间">
                {report.approved_at ? dateTimeUtils.formatDateTime(report.approved_at) : '-'}
              </Descriptions.Item>
              <Descriptions.Item label="备注" span={2}>
                {report.notes || '-'}
              </Descriptions.Item>
              {report.status === 'rejected' && (
                <Descriptions.Item label="拒绝原因" span={2}>
                  <Text type="danger">{report.reject_reason || '-'}</Text>
                </Descriptions.Item>
              )}
            </Descriptions>
          </TabPane>

          <TabPane tab="渠道销售" key="2">
            {(() => {
              // 检查渠道销售数据是否存在且不为空
              if (!report.channel_sales || typeof report.channel_sales !== 'object' || Object.keys(report.channel_sales).length === 0) {
                return <Empty description="暂无渠道销售数据" />;
              }

              // 处理渠道销售数据，确保渠道名称正确显示
              const channelDataSource = Object.entries(report.channel_sales).map(([channelId, amount]) => {
                // 从渠道列表中查找名称
                const foundChannel = channels.find(c => c.id === channelId);
                return {
                  key: channelId || String(Math.random()),
                  channel_id: channelId,
                  channel_name: foundChannel ? foundChannel.name : (channelId || '未知渠道'),
                  amount: parseFloat(amount || 0)
                };
              });

              // 渲染表格
              return (
                <Table
                  dataSource={channelDataSource}
                  columns={[
                    {
                      title: '渠道',
                      dataIndex: 'channel_name',
                      key: 'channel_name',
                    },
                    {
                      title: '销售金额',
                      dataIndex: 'amount',
                      key: 'amount',
                      render: (value) => `¥${parseFloat(value || 0).toFixed(2)}`,
                      sorter: (a, b) => a.amount - b.amount
                    }
                  ]}
                  pagination={false}
                  summary={(pageData) => {
                    // 计算总金额
                    const total = pageData.reduce((sum, item) => sum + (isNaN(item.amount) ? 0 : item.amount), 0);

                    // 检查总金额是否与报告的总销售额匹配
                    const totalSales = parseFloat(report.total_sales || 0);
                    const isMatched = Math.abs(total - totalSales) < 0.01; // 允许0.01的误差

                    return (
                      <Table.Summary.Row>
                        <Table.Summary.Cell index={0}>
                          <Text strong>总计</Text>
                          {!isMatched && (
                            <Tooltip title="渠道销售总额与销售总额不匹配">
                              <Badge status="warning" style={{ marginLeft: 8 }} />
                            </Tooltip>
                          )}
                        </Table.Summary.Cell>
                        <Table.Summary.Cell index={1}>
                          <Text strong>¥{total.toFixed(2)}</Text>
                        </Table.Summary.Cell>
                      </Table.Summary.Row>
                    );
                  }}
                />
              );
            })()}
          </TabPane>

          <TabPane tab="充值/售卡" key="3">
            <Descriptions bordered column={2}>
              <Descriptions.Item label="会员充值金额">
                ¥{parseFloat(report.recharge_amount || 0).toFixed(2)}
              </Descriptions.Item>
              <Descriptions.Item label="会员充值笔数">
                {report.recharge_count || 0} 笔
              </Descriptions.Item>
              <Descriptions.Item label="储值卡销售金额">
                ¥{parseFloat(report.card_sales_amount || 0).toFixed(2)}
              </Descriptions.Item>
              <Descriptions.Item label="储值卡销售数量">
                {report.card_sales_count || 0} 张
              </Descriptions.Item>
              <Descriptions.Item label="充值/售卡总额">
                ¥{parseFloat((report.recharge_amount || 0) + (report.card_sales_amount || 0)).toFixed(2)}
              </Descriptions.Item>
            </Descriptions>
          </TabPane>

          <TabPane tab="支付方式" key="4">
            {renderPaymentMethods()}
          </TabPane>
        </Tabs>
      </Card>

      {/* 提交确认弹窗 */}
      <Modal
        title="提交销售上报"
        open={submitModalVisible}
        onOk={handleSubmit}
        onCancel={() => setSubmitModalVisible(false)}
        confirmLoading={loading}
      >
        <p>确定要提交此销售上报吗？提交后将无法修改，需等待审核。</p>
        <div>
          <p><strong>门店:</strong> {report.store_name}</p>
          <p><strong>上报日期:</strong> {dateTimeUtils.formatDate(report.report_date)}</p>
          <p><strong>上报类型:</strong> {reportTypeTexts[report.report_type]}</p>
          <p><strong>销售总额:</strong> ¥{parseFloat(report.total_sales || 0).toFixed(2)}</p>
          <p><strong>充值/售卡总额:</strong> ¥{parseFloat((report.recharge_amount || 0) + (report.card_sales_amount || 0)).toFixed(2)}</p>
        </div>
      </Modal>

      {/* 撤回确认弹窗 */}
      <Modal
        title="撤回销售上报"
        open={withdrawModalVisible}
        onOk={handleWithdraw}
        onCancel={() => setWithdrawModalVisible(false)}
        confirmLoading={loading}
      >
        <p>确定要撤回此销售上报吗？撤回后将变为草稿状态，可以继续编辑。</p>
        <div>
          <p><strong>门店:</strong> {report.store_name}</p>
          <p><strong>上报日期:</strong> {dateTimeUtils.formatDate(report.report_date)}</p>
          <p><strong>上报类型:</strong> {reportTypeTexts[report.report_type]}</p>
        </div>
      </Modal>
    </div>
  );
};

export default SalesReportDetail;
