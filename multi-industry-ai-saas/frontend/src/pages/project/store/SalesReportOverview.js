import React, { useState, useEffect } from 'react';
import { 
  Card, 
  Row, 
  Col, 
  Statistic, 
  Table, 
  DatePicker, 
  Select, 
  Spin, 
  Empty, 
  Typography,
  Divider,
  Tabs,
  Progress
} from 'antd';
import { 
  ArrowUpOutlined, 
  ArrowDownOutlined, 
  ShopOutlined, 
  UserOutlined, 
  ShoppingOutlined, 
  DollarOutlined 
} from '@ant-design/icons';
import dayjs from "dayjs";
import apiService from '../../../services/api';
import { Column, Pie } from '@ant-design/charts';

const { Title, Text } = Typography;
const { RangePicker } = DatePicker;
const { Option } = Select;
const { TabPane } = Tabs;

const SalesReportOverview = ({ selectedStore, dateRange, onDateRangeChange }) => {
  const [loading, setLoading] = useState(false);
  const [salesData, setSalesData] = useState(null);
  const [paymentData, setPaymentData] = useState([]);
  const [categoryData, setCategoryData] = useState([]);
  const [dailyTrendData, setDailyTrendData] = useState([]);
  const [hourlyData, setHourlyData] = useState([]);
  
  // 获取销售概览数据
  const fetchSalesOverview = async () => {
    if (!selectedStore) return;
    
    setLoading(true);
    try {
      // 构建查询参数
      const params = {
        store_id: selectedStore,
        start_date: dateRange[0].format('YYYY-MM-DD'),
        end_date: dateRange[1].format('YYYY-MM-DD')
      };
      
      // 获取销售概览数据
      const response = await apiService.project.salesReport.getOverview(params);
      
      if (response) {
        setSalesData(response);
        
        // 处理支付方式数据
        if (response.payment_methods) {
          const paymentMethodsData = Object.entries(response.payment_methods).map(([name, value]) => ({
            type: getPaymentMethodName(name),
            value: parseFloat(value)
          }));
          setPaymentData(paymentMethodsData);
        }
        
        // 处理产品类别数据
        if (response.product_categories) {
          const categoriesData = Object.entries(response.product_categories).map(([name, value]) => ({
            type: name || '未分类',
            value: parseFloat(value)
          }));
          setCategoryData(categoriesData);
        }
        
        // 处理每日趋势数据
        if (response.daily_sales) {
          const dailyData = Object.entries(response.daily_sales).map(([date, value]) => ({
            date,
            sales: parseFloat(value)
          })).sort((a, b) => dayjs(a.date).diff(dayjs(b.date)));
          setDailyTrendData(dailyData);
        }
        
        // 处理分时段销售数据
        if (response.hourly_sales) {
          const hourlyData = Object.entries(response.hourly_sales).map(([hour, value]) => ({
            hour: `${hour}:00`,
            sales: parseFloat(value)
          })).sort((a, b) => parseInt(a.hour) - parseInt(b.hour));
          setHourlyData(hourlyData);
        }
      }
    } catch (error) {
      console.error('获取销售概览数据失败:', error);
    } finally {
      setLoading(false);
    }
  };
  
  // 获取支付方式名称
  const getPaymentMethodName = (method) => {
    const methodMap = {
      cash: '现金',
      wechat: '微信',
      alipay: '支付宝',
      card: '刷卡',
      other: '其他'
    };
    return methodMap[method] || method;
  };
  
  // 初始化
  useEffect(() => {
    if (selectedStore && dateRange && dateRange.length === 2) {
      fetchSalesOverview();
    }
  }, [selectedStore, dateRange]);
  
  // 支付方式饼图配置
  const paymentPieConfig = {
    data: paymentData,
    angleField: 'value',
    colorField: 'type',
    radius: 0.8,
    label: {
      type: 'outer',
      content: '{name}: {percentage}',
    },
    interactions: [{ type: 'element-active' }],
  };
  
  // 产品类别饼图配置
  const categoryPieConfig = {
    data: categoryData,
    angleField: 'value',
    colorField: 'type',
    radius: 0.8,
    label: {
      type: 'outer',
      content: '{name}: {percentage}',
    },
    interactions: [{ type: 'element-active' }],
  };
  
  // 每日销售趋势图配置
  const dailyTrendConfig = {
    data: dailyTrendData,
    xField: 'date',
    yField: 'sales',
    meta: {
      sales: {
        alias: '销售额',
        formatter: (v) => `¥${v.toFixed(2)}`
      },
      date: {
        alias: '日期'
      }
    },
    xAxis: {
      label: {
        formatter: (v) => dayjs(v).format('MM-DD')
      }
    },
    tooltip: {
      formatter: (datum) => {
        return { name: '销售额', value: `¥${datum.sales.toFixed(2)}` };
      }
    }
  };
  
  // 分时段销售图配置
  const hourlyConfig = {
    data: hourlyData,
    xField: 'hour',
    yField: 'sales',
    meta: {
      sales: {
        alias: '销售额',
        formatter: (v) => `¥${v.toFixed(2)}`
      },
      hour: {
        alias: '时段'
      }
    },
    tooltip: {
      formatter: (datum) => {
        return { name: '销售额', value: `¥${datum.sales.toFixed(2)}` };
      }
    }
  };
  
  if (loading) {
    return (
      <div style={{ textAlign: 'center', padding: '50px' }}>
        <Spin size="large" />
      </div>
    );
  }
  
  if (!salesData) {
    return (
      <Empty 
        description="暂无销售数据" 
        style={{ margin: '50px 0' }}
      />
    );
  }
  
  return (
    <div className="sales-overview">
      {/* 销售数据概览 */}
      <Row gutter={16} style={{ marginBottom: 16 }}>
        <Col span={6}>
          <Card>
            <Statistic
              title="销售总额"
              value={salesData.total_sales || 0}
              precision={2}
              prefix="¥"
              valueStyle={{ color: '#3f8600' }}
              suffix={
                salesData.sales_growth > 0 ? (
                  <ArrowUpOutlined style={{ color: '#3f8600' }} />
                ) : (
                  <ArrowDownOutlined style={{ color: '#cf1322' }} />
                )
              }
            />
            <div style={{ marginTop: 8 }}>
              <Text type={salesData.sales_growth > 0 ? 'success' : 'danger'}>
                {salesData.sales_growth > 0 ? '+' : ''}{salesData.sales_growth || 0}% 较上期
              </Text>
            </div>
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="订单总数"
              value={salesData.total_orders || 0}
              prefix={<ShoppingOutlined />}
              valueStyle={{ color: '#1890ff' }}
            />
            <div style={{ marginTop: 8 }}>
              <Text>平均客单价: ¥{salesData.average_order_value?.toFixed(2) || '0.00'}</Text>
            </div>
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="顾客总数"
              value={salesData.total_customers || 0}
              prefix={<UserOutlined />}
              valueStyle={{ color: '#722ed1' }}
            />
            <div style={{ marginTop: 8 }}>
              <Text>客均消费: ¥{salesData.average_customer_value?.toFixed(2) || '0.00'}</Text>
            </div>
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="线上销售占比"
              value={salesData.online_percentage || 0}
              precision={2}
              suffix="%"
              valueStyle={{ color: '#13c2c2' }}
            />
            <Progress 
              percent={salesData.online_percentage || 0} 
              showInfo={false} 
              strokeColor="#13c2c2" 
              style={{ marginTop: 8 }}
            />
          </Card>
        </Col>
      </Row>
      
      {/* 销售数据详情 */}
      <Row gutter={16}>
        <Col span={12}>
          <Card title="每日销售趋势" style={{ marginBottom: 16 }}>
            {dailyTrendData.length > 0 ? (
              <Column {...dailyTrendConfig} />
            ) : (
              <Empty description="暂无每日销售数据" />
            )}
          </Card>
        </Col>
        <Col span={12}>
          <Card title="分时段销售分布" style={{ marginBottom: 16 }}>
            {hourlyData.length > 0 ? (
              <Column {...hourlyConfig} />
            ) : (
              <Empty description="暂无分时段销售数据" />
            )}
          </Card>
        </Col>
      </Row>
      
      <Row gutter={16}>
        <Col span={12}>
          <Card title="支付方式分布" style={{ marginBottom: 16 }}>
            {paymentData.length > 0 ? (
              <Pie {...paymentPieConfig} />
            ) : (
              <Empty description="暂无支付方式数据" />
            )}
          </Card>
        </Col>
        <Col span={12}>
          <Card title="产品类别分布" style={{ marginBottom: 16 }}>
            {categoryData.length > 0 ? (
              <Pie {...categoryPieConfig} />
            ) : (
              <Empty description="暂无产品类别数据" />
            )}
          </Card>
        </Col>
      </Row>
      
      {/* 热销产品 */}
      <Card title="热销产品" style={{ marginBottom: 16 }}>
        {salesData.hot_products && salesData.hot_products.length > 0 ? (
          <Table
            dataSource={salesData.hot_products}
            rowKey="product_id"
            pagination={false}
            columns={[
              {
                title: '产品名称',
                dataIndex: 'product_name',
                key: 'product_name',
              },
              {
                title: '产品类别',
                dataIndex: 'product_category',
                key: 'product_category',
              },
              {
                title: '销售数量',
                dataIndex: 'quantity',
                key: 'quantity',
                sorter: (a, b) => a.quantity - b.quantity,
              },
              {
                title: '销售金额',
                dataIndex: 'sales_amount',
                key: 'sales_amount',
                render: (text) => `¥${parseFloat(text || 0).toFixed(2)}`,
                sorter: (a, b) => a.sales_amount - b.sales_amount,
              },
              {
                title: '占比',
                dataIndex: 'percentage',
                key: 'percentage',
                render: (text) => `${parseFloat(text || 0).toFixed(2)}%`,
                sorter: (a, b) => a.percentage - b.percentage,
              }
            ]}
          />
        ) : (
          <Empty description="暂无热销产品数据" />
        )}
      </Card>
    </div>
  );
};

export default SalesReportOverview;
