import React, { useState, useEffect } from 'react';
import {
  Card,
  Table,
  Button,
  Space,
  Select,
  Row,
  Col,
  Statistic,
  message,
  Spin,
  Typography,
  DatePicker,
  Empty,
  Form,
  Divider
} from 'antd';
import {
  BarChartOutlined,
  LineChartOutlined,
  DownloadOutlined,
  SearchOutlined,
  UserOutlined,
  DollarOutlined,
  CreditCardOutlined
} from '@ant-design/icons';
import { Column } from '@ant-design/charts';
import dayjs from 'dayjs';
import 'dayjs/locale/zh-cn';
import apiService from '../../../services/api';

const { Text, Title } = Typography;
const { Option } = Select;

/**
 * 销售月报组件
 * 按月汇总销售数据，展示销售趋势和详细数据
 *
 * @param {Object} props 组件属性
 * @param {string} props.selectedStore 选中的门店ID
 * @param {Array} props.dateRange 日期范围
 */
const SalesMonthlyReport = ({ selectedStore, dateRange }) => {
  const [loading, setLoading] = useState(false);
  const [monthlyReport, setMonthlyReport] = useState(null);
  const [dailyData, setDailyData] = useState([]);
  const [selectedMonth, setSelectedMonth] = useState(dayjs());
  const [monthlyReportLoading, setMonthlyReportLoading] = useState(false);

  // 初始化
  useEffect(() => {
    // 设置 dayjs 语言为中文
    dayjs.locale('zh-cn');

    // 初始化时获取数据
    fetchMonthlyReport();
  }, []);

  // 当父组件传递的门店ID变化时更新
  useEffect(() => {
    if (selectedStore !== undefined) {
      // 当门店变化时重新获取数据
      fetchMonthlyReport();
    }
  }, [selectedStore]);

  // 当父组件传递的日期范围变化时更新
  useEffect(() => {
    if (dateRange && dateRange.length === 2) {
      // 使用日期范围中的第一个月作为选中月份
      setSelectedMonth(dayjs(dateRange[0]));

      // 当日期范围变化时重新获取数据
      fetchMonthlyReport();
    }
  }, [dateRange]);

  // 获取月度销售报表数据
  const fetchMonthlyReport = async () => {
    setMonthlyReportLoading(true);
    try {
      // 准备月份字符串，确保格式为YYYY-MM
      const monthStr = selectedMonth.format('YYYY-MM');

      // 构建查询参数
      const params = {
        month: monthStr
      };

      // 只有当门店ID有效时才添加到参数中
      if (selectedStore) {
        params.store_id = selectedStore;
      }

      // 调用API获取月度销售报表数据
      // 如果 getMonthlyReport 方法不存在，使用 getStatistics 方法代替
      const response = apiService.project.salesReport.getMonthlyReport
        ? await apiService.project.salesReport.getMonthlyReport(params)
        : await apiService.project.salesReport.getStatistics({
            ...params,
            group_by: 'day',
            start_date: dayjs(monthStr).startOf('month').format('YYYY-MM-DD'),
            end_date: dayjs(monthStr).endOf('month').format('YYYY-MM-DD')
          });

      if (response) {
        // 处理不同格式的响应数据
        if (response.data) {
          // 标准 getMonthlyReport 响应格式
          setMonthlyReport(response.data);

          // 如果有每日数据，设置每日数据
          if (response.data.daily_data && Array.isArray(response.data.daily_data)) {
            // 处理每日数据，确保所有数值字段都是数字类型
            const formattedDailyData = response.data.daily_data.map(item => ({
              ...item,
              date: item.date,
              total_sales: parseFloat(item.total_sales || 0),
              total_orders: parseInt(item.total_orders || 0, 10),
              total_customers: parseInt(item.total_customers || 0, 10),
              average_order: parseFloat(item.average_order || 0),
              recharge_amount: parseFloat(item.recharge_amount || 0)
            }));

            // 按日期排序
            formattedDailyData.sort((a, b) => dayjs(a.date).diff(dayjs(b.date)));

            setDailyData(formattedDailyData);
          } else {
            setDailyData([]);
          }
        } else if (response.items && Array.isArray(response.items)) {
          // getStatistics 响应格式
          // 计算月度汇总数据
          const totalSales = response.items.reduce((sum, item) => sum + parseFloat(item.total_sales || 0), 0);
          const totalOrders = response.items.reduce((sum, item) => sum + parseInt(item.total_orders || 0, 10), 0);
          const totalCustomers = response.items.reduce((sum, item) => sum + parseInt(item.total_customers || 0, 10), 0);
          const totalRecharge = response.items.reduce((sum, item) => {
            const rechargeAmount = parseFloat(item.recharge_amount || 0);
            const cardSalesAmount = parseFloat(item.card_sales_amount || 0);
            return sum + rechargeAmount + cardSalesAmount;
          }, 0);

          // 创建月度报表数据
          const monthlyData = {
            month: selectedMonth.format('YYYY-MM'),
            total_sales: totalSales,
            total_orders: totalOrders,
            customer_count: totalCustomers,
            average_order: totalOrders > 0 ? totalSales / totalOrders : 0,
            total_recharge: totalRecharge
          };

          setMonthlyReport(monthlyData);

          // 处理每日数据
          const formattedDailyData = response.items.map(item => ({
            date: item.date || item.day || item.month,
            total_sales: parseFloat(item.total_sales || 0),
            total_orders: parseInt(item.total_orders || 0, 10),
            total_customers: parseInt(item.total_customers || 0, 10),
            average_order: item.total_orders > 0 ? item.total_sales / item.total_orders : 0,
            recharge_amount: parseFloat(item.recharge_amount || 0) + parseFloat(item.card_sales_amount || 0)
          }));

          // 按日期排序
          formattedDailyData.sort((a, b) => dayjs(a.date).diff(dayjs(b.date)));

          setDailyData(formattedDailyData);
        } else {
          // 如果没有数据或数据格式不正确，重置状态
          setMonthlyReport(null);
          setDailyData([]);
          message.info('暂无月度销售报表数据');
        }
      } else {
        // 如果没有响应，重置状态
        setMonthlyReport(null);
        setDailyData([]);
        message.info('暂无月度销售报表数据');
      }
    } catch (error) {
      console.error('获取月度销售报表失败:', error);
      message.error('获取月度销售报表失败: ' + (error.response?.data?.detail || error.message));
      // 发生错误时重置数据
      setMonthlyReport(null);
      setDailyData([]);
    } finally {
      setMonthlyReportLoading(false);
    }
  };

  // 导出月度报表数据
  const handleExportMonthlyReport = async () => {
    if (!monthlyReport) {
      message.warning('没有数据可导出');
      return;
    }

    try {
      // 准备月份字符串，确保格式为YYYY-MM
      const monthStr = selectedMonth.format('YYYY-MM');

      // 构建查询参数
      const params = {
        month: monthStr,
        export: true
      };

      // 只有当门店ID有效时才添加到参数中
      if (selectedStore) {
        params.store_id = selectedStore;
      }

      // 尝试使用 exportMonthlyReport 方法，如果不存在则使用 export 方法
      let response;
      if (apiService.project.salesReport.exportMonthlyReport) {
        response = await apiService.project.salesReport.exportMonthlyReport(params);
      } else {
        // 使用通用导出方法
        const exportParams = {
          start_date: dayjs(monthStr).startOf('month').format('YYYY-MM-DD'),
          end_date: dayjs(monthStr).endOf('month').format('YYYY-MM-DD'),
          store_id: selectedStore,
          group_by: 'day'
        };
        response = await apiService.project.salesReport.export(exportParams);
      }

      if (response) {
        // 创建下载链接
        const url = window.URL.createObjectURL(new Blob([response]));
        const link = document.createElement('a');
        link.href = url;
        link.setAttribute('download', `销售月报_${monthStr}.xlsx`);
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);

        message.success('导出成功');
      }
    } catch (error) {
      console.error('导出月度销售报表失败:', error);
      message.error('导出月度销售报表失败: ' + (error.response?.data?.detail || error.message));
    }
  };

  // 渲染月度销售报表
  const renderMonthlyReport = () => {
    return (
      <div className="monthly-report-container">
        <Card title="月度销售报表" style={{ marginBottom: 16, boxShadow: '0 1px 3px rgba(0,0,0,0.1)' }} className="monthly-report-card">
          <div style={{ marginBottom: 16, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            <DatePicker
              picker="month"
              value={selectedMonth}
              onChange={async (date) => {
                if (date) {
                  // 先更新状态
                  setSelectedMonth(date);
                  // 使用新的date值直接调用API
                  await fetchMonthlyReport();
                }
              }}
              style={{ width: 200 }}
              allowClear={false}
            />
            <Button
              icon={<DownloadOutlined />}
              onClick={handleExportMonthlyReport}
              disabled={!monthlyReport}
            >
              导出报表
            </Button>
          </div>

          {monthlyReportLoading ? (
            <div style={{ textAlign: 'center', padding: '20px 0' }}>
              <Spin />
            </div>
          ) : monthlyReport ? (
            <>
              {/* 顶部统计卡片 */}
              <Row gutter={[16, 16]} style={{ marginBottom: 16 }}>
                <Col span={6} xs={12} sm={8} md={6}>
                  <Card className="stat-card">
                    <Statistic
                      title="月度总销售额"
                      value={monthlyReport.total_sales || 0}
                      precision={2}
                      prefix={<DollarOutlined style={{ color: '#1890ff' }} />}
                      suffix="元"
                      valueStyle={{ color: '#1890ff', fontWeight: 'bold' }}
                    />
                  </Card>
                </Col>
                <Col span={6} xs={12} sm={8} md={6}>
                  <Card className="stat-card">
                    <Statistic
                      title="客流量"
                      value={monthlyReport.customer_count || 0}
                      prefix={<UserOutlined style={{ color: '#52c41a' }} />}
                      suffix="人"
                      valueStyle={{ color: '#52c41a', fontWeight: 'bold' }}
                    />
                  </Card>
                </Col>
                <Col span={6} xs={12} sm={8} md={6}>
                  <Card className="stat-card">
                    <Statistic
                      title="客单价"
                      value={monthlyReport.average_order || (monthlyReport.total_orders > 0 ? (monthlyReport.total_sales / monthlyReport.total_orders) : 0)}
                      precision={2}
                      prefix={<BarChartOutlined style={{ color: '#722ed1' }} />}
                      suffix="元"
                      valueStyle={{ color: '#722ed1', fontWeight: 'bold' }}
                    />
                  </Card>
                </Col>
                <Col span={6} xs={12} sm={8} md={6}>
                  <Card className="stat-card">
                    <Statistic
                      title="总充值/售卡额"
                      value={monthlyReport.total_recharge || 0}
                      precision={2}
                      prefix={<CreditCardOutlined style={{ color: '#fa8c16' }} />}
                      suffix="元"
                      valueStyle={{ color: '#fa8c16', fontWeight: 'bold' }}
                    />
                  </Card>
                </Col>
              </Row>

              {/* 销售趋势图 */}
              <Card title="日销售趋势" style={{ marginBottom: 16 }}>
                {dailyData.length > 0 ? (
                  <Column
                    data={dailyData.filter(item => item && item.date)}
                    xField="date"
                    yField="total_sales"
                    meta={{
                      date: {
                        formatter: (value) => value ? dayjs(value).format('MM-DD') : ''
                      },
                      total_sales: {
                        formatter: (value) => value ? `¥${parseFloat(value).toFixed(2)}` : '¥0.00'
                      }
                    }}
                    label={{
                      position: 'top',
                      formatter: (datum) => {
                        if (!datum || typeof datum.total_sales === 'undefined') return '¥0';
                        return `¥${parseFloat(datum.total_sales).toFixed(0)}`;
                      }
                    }}
                    tooltip={{
                      formatter: (datum) => {
                        if (!datum || typeof datum.total_sales === 'undefined') {
                          return { name: '销售额', value: '¥0.00' };
                        }
                        return {
                          name: '销售额',
                          value: `¥${parseFloat(datum.total_sales).toFixed(2)}`
                        };
                      }
                    }}
                    height={300}
                  />
                ) : (
                  <Empty description="暂无日销售数据" />
                )}
              </Card>

              {/* 每日销售数据表格 */}
              <Card title="每日销售数据">
                <Table
                  dataSource={dailyData}
                  columns={[
                    {
                      title: '日期',
                      dataIndex: 'date',
                      key: 'date',
                      render: (text) => dayjs(text).format('YYYY-MM-DD')
                    },
                    {
                      title: '销售额',
                      dataIndex: 'total_sales',
                      key: 'total_sales',
                      render: (text) => `¥${parseFloat(text || 0).toFixed(2)}`,
                      sorter: (a, b) => a.total_sales - b.total_sales
                    },
                    {
                      title: '订单数',
                      dataIndex: 'total_orders',
                      key: 'total_orders',
                      sorter: (a, b) => a.total_orders - b.total_orders
                    },
                    {
                      title: '客单价',
                      dataIndex: 'average_order',
                      key: 'average_order',
                      render: (_, record) => {
                        const avgOrder = record.total_orders > 0 ? record.total_sales / record.total_orders : 0;
                        return `¥${avgOrder.toFixed(2)}`;
                      },
                      sorter: (a, b) => {
                        const avgA = a.total_orders > 0 ? a.total_sales / a.total_orders : 0;
                        const avgB = b.total_orders > 0 ? b.total_sales / b.total_orders : 0;
                        return avgA - avgB;
                      }
                    },
                    {
                      title: '充值/售卡额',
                      dataIndex: 'recharge_amount',
                      key: 'recharge_amount',
                      render: (text) => `¥${parseFloat(text || 0).toFixed(2)}`,
                    }
                  ]}
                  pagination={{ pageSize: 10 }}
                  bordered
                  size="middle"
                  rowKey="date"
                />
              </Card>
            </>
          ) : (
            <Empty description="暂无月度报表数据" />
          )}
        </Card>
      </div>
    );
  };

  return (
    <div className="sales-monthly-report">
      {renderMonthlyReport()}
    </div>
  );
};

export default SalesMonthlyReport;
