import React, { useState } from 'react';
import { Tabs, Card } from 'antd';
import {
  DashboardOutlined,
  ShoppingOutlined,
  AccountBookOutlined,
  ShopOutlined,
  ShoppingCartOutlined
} from '@ant-design/icons';
import ProjectDashboard from './ProjectDashboard';
import OperationsDashboard from './OperationsDashboard';
import FinanceDashboard from './FinanceDashboard';
import WarehouseDashboard from './WarehouseDashboard';
import PurchaseDashboard from './PurchaseDashboard';
import { useNavigate, useLocation } from 'react-router-dom';

const { TabPane } = Tabs;

/**
 * 仪表盘页面
 * 包含项目管理、运营、财务、仓储、采购等仪表盘
 * @param {Object} props - 组件属性
 * @param {string} props.activeTab - 当前活动的标签
 */
const Dashboard = ({ activeTab: initialActiveTab }) => {
  const navigate = useNavigate();
  const location = useLocation();

  // 从URL中获取当前活动的标签
  const getActiveTabFromPath = () => {
    const path = location.pathname;
    if (path.includes('/operation/dashboard')) return '2';
    if (path.includes('/finance/dashboard')) return '3';
    if (path.includes('/inventory/dashboard')) return '4';
    if (path.includes('/purchase/dashboard')) return '5';
    return '1'; // 默认为项目仪表盘
  };

  const [activeTab, setActiveTab] = useState(initialActiveTab || getActiveTabFromPath());

  // 处理标签切换
  const handleTabChange = (key) => {
    setActiveTab(key);

    // 更新URL
    switch (key) {
      case '1':
        navigate('/project/dashboard');
        break;
      case '2':
        navigate('/project/operation/dashboard');
        break;
      case '3':
        navigate('/project/finance/dashboard');
        break;
      case '4':
        navigate('/project/inventory/dashboard');
        break;
      case '5':
        navigate('/project/purchase/dashboard');
        break;
      default:
        navigate('/project/dashboard');
    }
  };

  // 渲染标签内容
  const renderTabContent = () => {
    switch (activeTab) {
      case '1':
        return <ProjectDashboard />;
      case '2':
        return <OperationsDashboard />;
      case '3':
        return <FinanceDashboard />;
      case '4':
        return <WarehouseDashboard />;
      case '5':
        return <PurchaseDashboard />;
      default:
        return <ProjectDashboard />;
    }
  };

  return (
    <div className="dashboard-container">
      <Tabs
        activeKey={activeTab}
        onChange={handleTabChange}
        tabPosition="top"
        type="card"
        className="dashboard-tabs"
      >
        <TabPane
          tab={
            <span>
              <DashboardOutlined />
              项目管理
            </span>
          }
          key="1"
        />
        <TabPane
          tab={
            <span>
              <ShoppingOutlined />
              运营
            </span>
          }
          key="2"
        />
        <TabPane
          tab={
            <span>
              <AccountBookOutlined />
              财务
            </span>
          }
          key="3"
        />
        <TabPane
          tab={
            <span>
              <ShopOutlined />
              仓储
            </span>
          }
          key="4"
        />
        <TabPane
          tab={
            <span>
              <ShoppingCartOutlined />
              采购
            </span>
          }
          key="5"
        />
      </Tabs>

      <div className="dashboard-content">
        {renderTabContent()}
      </div>
    </div>
  );
};

export default Dashboard;
