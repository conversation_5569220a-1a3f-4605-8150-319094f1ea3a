import React, { useState, useEffect, useRef } from 'react';
import {
  Card,
  Input,
  Button,
  List,
  Avatar,
  Typography,
  Space,
  Spin,
  message,
  Select,
  Tag,
  Divider,
  Empty,
  Tooltip,
  Modal,
  Form,
  Switch,
  Upload,
  Checkbox,
  Alert,
  Progress,
  Row,
  Col,
  Badge,
  Popover,
  Tabs,
  Statistic
} from 'antd';
import {
  SendOutlined,
  RobotOutlined,
  UserOutlined,
  MessageOutlined,
  HistoryOutlined,
  SettingOutlined,
  DingtalkOutlined,
  ReloadOutlined,
  SyncOutlined,
  PlusOutlined,
  FileOutlined,
  PictureOutlined,
  BookOutlined,
  CloudUploadOutlined,
  CheckCircleOutlined,
  ExclamationCircleOutlined,
  InfoCircleOutlined
} from '@ant-design/icons';
import * as dingtalkService from '../service/dingtalkService';

const { TextArea } = Input;
const { Text, Paragraph } = Typography;
const { Option } = Select;
const { Dragger } = Upload;
const { TabPane } = Tabs;

/**
 * AI聊天组件 - 支持文件上传和知识库集成
 */
const AIChat = () => {
  const [loading, setLoading] = useState(true);
  const [sending, setSending] = useState(false);
  const [syncing, setSyncing] = useState(false);
  const [messages, setMessages] = useState([]);
  const [inputMessage, setInputMessage] = useState('');
  const [assistants, setAssistants] = useState([]);
  const [threads, setThreads] = useState([]);
  const [selectedAssistant, setSelectedAssistant] = useState(null);
  const [selectedThread, setSelectedThread] = useState(null);
  const [userInfo, setUserInfo] = useState(null);
  const [settingsVisible, setSettingsVisible] = useState(false);
  const [autoSendToDingtalk, setAutoSendToDingtalk] = useState(false);
  const [sendTarget, setSendTarget] = useState({ type: 'user', id: '' });
  const [messageType, setMessageType] = useState('text'); // text 或 card
  const [lastSyncTime, setLastSyncTime] = useState(null);
  
  // 新增状态 - 文件和知识库支持
  const [uploadedFiles, setUploadedFiles] = useState([]);
  const [useKnowledgeBase, setUseKnowledgeBase] = useState(true);
  const [knowledgeBases, setKnowledgeBases] = useState([]);
  const [selectedKnowledgeBases, setSelectedKnowledgeBases] = useState([]);
  const [fileUploadVisible, setFileUploadVisible] = useState(false);

  const messagesEndRef = useRef(null);
  const [settingsForm] = Form.useForm();

  // 滚动到底部
  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  // 初始化
  useEffect(() => {
    const initializeChat = async () => {
      setLoading(true);
      try {
        await Promise.all([
          fetchUserInfo(),
          fetchAssistants(),
          fetchKnowledgeBases()
        ]);
      } catch (error) {
        console.error('初始化AI聊天失败:', error);
      } finally {
        setLoading(false);
      }
    };

    initializeChat();
    
    // 监听窗口焦点事件，当用户从其他页面返回时自动刷新用户状态
    const handleFocus = () => {
      // 延迟刷新，避免频繁调用
      setTimeout(() => {
        fetchUserInfo();
      }, 500);
    };
    
    window.addEventListener('focus', handleFocus);
    
    return () => {
      window.removeEventListener('focus', handleFocus);
    };
  }, []);

  // 获取用户信息
  const fetchUserInfo = async () => {
    try {
      const response = await dingtalkService.getUserInfo();
      if (response.success) {
        setUserInfo(response.data);
      }
    } catch (error) {
      console.error('获取用户信息失败:', error);
    }
  };

  // 获取AI助手列表
  const fetchAssistants = async () => {
    try {
      const response = await dingtalkService.getAIAssistants();
      if (response.success) {
        setAssistants(response.data);
        // 默认选择钉钉智能助手
        const dingtalkAssistant = response.data.find(a => a.name === '钉钉智能助手');
        if (dingtalkAssistant) {
          setSelectedAssistant(dingtalkAssistant.id);
          await fetchThreads(dingtalkAssistant.id);
        }
      }
    } catch (error) {
      console.error('获取AI助手列表失败:', error);
      message.error('获取AI助手列表失败');
    }
  };

  // 获取知识库列表
  const fetchKnowledgeBases = async () => {
    try {
      const projectId = localStorage.getItem('project_id');
      const response = await dingtalkService.getKnowledgeBases();
      if (response.success) {
        setKnowledgeBases(response.data || []);
        // 默认选择所有知识库
        setSelectedKnowledgeBases((response.data || []).map(kb => kb.id));
      }
    } catch (error) {
      console.error('获取知识库列表失败:', error);
      // 设置空数组避免错误
      setKnowledgeBases([]);
      setSelectedKnowledgeBases([]);
    }
  };

  // 获取对话线程
  const fetchThreads = async (assistantId) => {
    try {
      const response = await dingtalkService.getAIThreads(assistantId);
      if (response.success) {
        setThreads(response.data);
      }
    } catch (error) {
      console.error('获取对话线程失败:', error);
    }
  };

  // 文件上传配置
  const uploadProps = {
    name: 'files',
    multiple: true,
    accept: '.txt,.pdf,.doc,.docx,.jpg,.jpeg,.png,.gif',
    beforeUpload: (file) => {
      // 检查文件大小（限制10MB）
      const isLt10M = file.size / 1024 / 1024 < 10;
      if (!isLt10M) {
        message.error('文件大小不能超过10MB');
        return false;
      }
      
      // 检查文件类型
      const allowedTypes = [
        'text/plain',
        'application/pdf',
        'application/msword',
        'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
        'image/jpeg',
        'image/jpg',
        'image/png',
        'image/gif'
      ];
      
      if (!allowedTypes.includes(file.type)) {
        message.error('不支持的文件类型');
        return false;
      }
      
      // 添加到上传文件列表
      setUploadedFiles(prev => [...prev, file]);
      return false; // 阻止自动上传
    },
    onRemove: (file) => {
      setUploadedFiles(prev => prev.filter(f => f.uid !== file.uid));
    },
    fileList: uploadedFiles
  };

  // 发送消息
  const handleSendMessage = async () => {
    if (!inputMessage.trim() && uploadedFiles.length === 0) {
      message.warning('请输入消息内容或上传文件');
      return;
    }

    if (!userInfo?.third_party_account) {
      message.error('请先绑定钉钉账号');
      return;
    }

    setSending(true);
    
    // 添加用户消息到界面
    const userMessage = {
      id: Date.now(),
      type: 'user',
      content: inputMessage,
      files: uploadedFiles.map(f => ({ name: f.name, type: f.type })),
      timestamp: new Date().toISOString()
    };
    
    setMessages(prev => [...prev, userMessage]);
    const currentMessage = inputMessage;
    const currentFiles = [...uploadedFiles];
    setInputMessage('');
    setUploadedFiles([]);

    try {
      let response;
      
      if (currentFiles.length > 0) {
        // 有文件时使用FormData
        const formData = new FormData();
        formData.append('message', currentMessage);
        
        if (selectedAssistant) {
          formData.append('assistant_id', selectedAssistant);
        }
        
        if (selectedThread) {
          formData.append('thread_id', selectedThread);
        }
        
        formData.append('use_knowledge_base', useKnowledgeBase);
        
        if (useKnowledgeBase && selectedKnowledgeBases.length > 0) {
          selectedKnowledgeBases.forEach(kbId => {
            formData.append('knowledge_base_ids', kbId);
          });
        }
        
        // 添加文件
        currentFiles.forEach(file => {
          formData.append('files', file);
        });

        // 调用文件上传接口
        response = await dingtalkService.aiChatWithFiles(formData);
      } else {
        // 没有文件时使用JSON格式
        const chatData = {
          message: currentMessage,
          assistant_id: selectedAssistant,
          thread_id: selectedThread,
          use_knowledge_base: useKnowledgeBase,
          knowledge_base_ids: useKnowledgeBase ? selectedKnowledgeBases : []
        };
        
        // 调用JSON接口
        response = await dingtalkService.aiChat(chatData);
      }

      if (response.success) {
        // 添加AI回复到界面
        const aiMessage = {
          id: Date.now() + 1,
          type: 'assistant',
          content: response.data.message,
          timestamp: response.data.created_at,
          assistant_id: response.data.assistant_id,
          thread_id: response.data.thread_id,
          files_processed: response.data.files_processed || [],
          knowledge_base_used: response.data.knowledge_base_used || false,
          usage: response.data.usage
        };
        
        setMessages(prev => [...prev, aiMessage]);
        
        // 更新选中的线程
        if (!selectedThread && response.data.thread_id) {
          setSelectedThread(response.data.thread_id);
          await fetchThreads(selectedAssistant);
        }

        // 如果开启自动发送到钉钉
        if (autoSendToDingtalk && sendTarget.type && sendTarget.id) {
          await handleSendToDingtalk(response.data.message);
        }
      } else {
        message.error('AI聊天失败: ' + (response.message || '未知错误'));
      }
    } catch (error) {
      console.error('发送消息失败:', error);
      message.error('发送消息失败');
    } finally {
      setSending(false);
    }
  };

  // 发送到钉钉
  const handleSendToDingtalk = async (messageContent) => {
    try {
      const response = await dingtalkService.sendAIResponseToDingtalk({
        message: messageContent,
        target_type: sendTarget.type,
        target_id: sendTarget.id,
        message_type: messageType
      });

      if (response.success) {
        message.success('AI回复已发送到钉钉');
      } else {
        message.error('发送到钉钉失败: ' + (response.message || '未知错误'));
      }
    } catch (error) {
      console.error('发送到钉钉失败:', error);
      message.error('发送到钉钉失败');
    }
  };

  // 同步钉钉消息
  const handleSyncMessages = async () => {
    try {
      setSyncing(true);
      const response = await dingtalkService.syncDingtalkMessages({
        since_timestamp: lastSyncTime
      });

      if (response.success) {
        message.success(`已同步 ${response.data.synced_count} 条消息`);
        setLastSyncTime(Date.now());
      } else {
        message.error('同步消息失败');
      }
    } catch (error) {
      console.error('同步消息失败:', error);
      message.error('同步消息失败');
    } finally {
      setSyncing(false);
    }
  };

  // 处理助手切换
  const handleAssistantChange = (assistantId) => {
    setSelectedAssistant(assistantId);
    setSelectedThread(null);
    setMessages([]);
    fetchThreads(assistantId);
  };

  // 处理线程切换
  const handleThreadChange = (threadId) => {
    setSelectedThread(threadId);
    setMessages([]);
    // 这里可以加载线程的历史消息
  };

  // 新建对话
  const handleNewChat = () => {
    setSelectedThread(null);
    setMessages([]);
  };

  // 同步用户信息
  const handleSyncUserInfo = async () => {
    try {
      const response = await dingtalkService.syncUserInfo();
      if (response.success) {
        message.success('同步用户信息成功');
        await fetchUserInfo();
      } else {
        message.error('同步用户信息失败');
      }
    } catch (error) {
      console.error('同步用户信息失败:', error);
      message.error('同步用户信息失败');
    }
  };

  // 渲染消息
  const renderMessage = (msg) => {
    const isUser = msg.type === 'user';
    
    return (
      <div
        key={msg.id}
        style={{
          display: 'flex',
          justifyContent: isUser ? 'flex-end' : 'flex-start',
          marginBottom: 16
        }}
      >
        <div style={{ maxWidth: '70%' }}>
          <div
            style={{
              display: 'flex',
              alignItems: 'flex-start',
              flexDirection: isUser ? 'row-reverse' : 'row'
            }}
          >
            <Avatar
              icon={isUser ? <UserOutlined /> : <RobotOutlined />}
              style={{
                backgroundColor: isUser ? '#1890ff' : '#52c41a',
                margin: isUser ? '0 0 0 8px' : '0 8px 0 0'
              }}
            />
            <div>
              <div
                style={{
                  padding: '12px 16px',
                  borderRadius: 12,
                  backgroundColor: isUser ? '#1890ff' : '#f0f0f0',
                  color: isUser ? 'white' : 'black',
                  wordBreak: 'break-word'
                }}
              >
                {msg.content}
                
                {/* 显示处理的文件 */}
                {msg.files && msg.files.length > 0 && (
                  <div style={{ marginTop: 8 }}>
                    {msg.files.map((file, index) => (
                      <Tag 
                        key={index} 
                        icon={file.type.startsWith('image/') ? <PictureOutlined /> : <FileOutlined />}
                        style={{ margin: '2px' }}
                      >
                        {file.name}
                      </Tag>
                    ))}
                  </div>
                )}
                
                {/* 显示AI处理信息 */}
                {!isUser && msg.files_processed && msg.files_processed.length > 0 && (
                  <div style={{ marginTop: 8, fontSize: 12, opacity: 0.8 }}>
                    <CheckCircleOutlined style={{ marginRight: 4 }} />
                    已处理 {msg.files_processed.length} 个文件
                  </div>
                )}
                
                {/* 显示知识库使用信息 */}
                {!isUser && msg.knowledge_base_used && (
                  <div style={{ marginTop: 4, fontSize: 12, opacity: 0.8 }}>
                    <BookOutlined style={{ marginRight: 4 }} />
                    已使用知识库
                  </div>
                )}
              </div>
              
              <div
                style={{
                  fontSize: 12,
                  color: '#999',
                  marginTop: 4,
                  textAlign: isUser ? 'right' : 'left'
                }}
              >
                {new Date(msg.timestamp).toLocaleTimeString()}
                {!isUser && msg.usage && (
                  <Tooltip title={`Token使用: ${msg.usage.total_tokens || 0}`}>
                    <InfoCircleOutlined style={{ marginLeft: 4 }} />
                  </Tooltip>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  };

  // 渲染用户状态
  const renderUserStatus = () => {
    if (!userInfo) return null;

    return (
      <Card size="small" style={{ marginBottom: 16 }}>
        <Space>
          <DingtalkOutlined style={{ color: '#1890ff' }} />
          <Text strong>钉钉状态:</Text>
          {userInfo.third_party_account ? (
            <Tag color="green">已绑定 - {userInfo.third_party_account.platform_username}</Tag>
          ) : (
            <Tag color="red">未绑定</Tag>
          )}
          {userInfo.dingtalk_mapping && (
            <Tag color="blue">已同步详细信息</Tag>
          )}
          <Button
            size="small"
            icon={<ReloadOutlined />}
            onClick={fetchUserInfo}
            loading={loading}
          >
            刷新状态
          </Button>
          {!userInfo.third_party_account && (
            <Button
              size="small"
              type="primary"
              onClick={() => window.open('/project/user/profile', '_blank')}
            >
              前往绑定
            </Button>
          )}
          {userInfo.third_party_account && !userInfo.dingtalk_mapping && (
            <Button
              size="small"
              icon={<SyncOutlined />}
              onClick={handleSyncUserInfo}
            >
              同步信息
            </Button>
          )}
        </Space>
      </Card>
    );
  };

  // 渲染设置面板
  const renderSettingsPanel = () => (
    <div style={{ padding: 16 }}>
      <div style={{ marginBottom: 16 }}>
        <Text strong>知识库设置</Text>
        <div style={{ marginTop: 8 }}>
          <Switch
            checked={useKnowledgeBase}
            onChange={setUseKnowledgeBase}
            checkedChildren="启用"
            unCheckedChildren="禁用"
          />
          <Text style={{ marginLeft: 8 }}>使用项目知识库</Text>
        </div>
        
        {useKnowledgeBase && (
          <div style={{ marginTop: 12 }}>
            <Text>选择知识库：</Text>
            <Checkbox.Group
              style={{ marginTop: 8, display: 'block' }}
              value={selectedKnowledgeBases}
              onChange={setSelectedKnowledgeBases}
            >
              {knowledgeBases.map(kb => (
                <div key={kb.id} style={{ marginBottom: 4 }}>
                  <Checkbox value={kb.id}>{kb.name}</Checkbox>
                </div>
              ))}
            </Checkbox.Group>
          </div>
        )}
      </div>
      
      <Divider />
      
      <div style={{ marginBottom: 16 }}>
        <Text strong>钉钉发送设置</Text>
        <div style={{ marginTop: 8 }}>
          <Switch
            checked={autoSendToDingtalk}
            onChange={setAutoSendToDingtalk}
            checkedChildren="自动发送"
            unCheckedChildren="手动发送"
          />
          <Text style={{ marginLeft: 8 }}>自动发送AI回复到钉钉</Text>
        </div>
        
        {autoSendToDingtalk && (
          <div style={{ marginTop: 12 }}>
            <Row gutter={8}>
              <Col span={8}>
                <Select
                  value={sendTarget.type}
                  onChange={(value) => setSendTarget({ ...sendTarget, type: value })}
                  style={{ width: '100%' }}
                >
                  <Option value="user">用户</Option>
                  <Option value="group">群组</Option>
                </Select>
              </Col>
              <Col span={10}>
                <Input
                  placeholder="目标ID"
                  value={sendTarget.id}
                  onChange={(e) => setSendTarget({ ...sendTarget, id: e.target.value })}
                />
              </Col>
              <Col span={6}>
                <Select
                  value={messageType}
                  onChange={setMessageType}
                  style={{ width: '100%' }}
                >
                  <Option value="text">文本</Option>
                  <Option value="card">卡片</Option>
                </Select>
              </Col>
            </Row>
          </div>
        )}
      </div>
    </div>
  );

  if (loading) {
    return (
      <Card>
        <div style={{ textAlign: 'center', padding: '50px 0' }}>
          <Spin size="large" />
          <div style={{ marginTop: 16 }}>正在初始化AI聊天...</div>
        </div>
      </Card>
    );
  }

  if (!userInfo?.third_party_account) {
    return (
      <div style={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
        {/* 用户状态 */}
        {renderUserStatus()}
        
        <Card style={{ flex: 1 }}>
          <Empty
            description="请先绑定钉钉账号才能使用AI智能对话功能"
            image={Empty.PRESENTED_IMAGE_SIMPLE}
          >
            <Button type="primary" onClick={() => window.open('/project/user/profile', '_blank')}>
              前往绑定钉钉账号
            </Button>
          </Empty>
        </Card>
      </div>
    );
  }

  return (
    <div style={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
      {/* 用户状态 */}
      {renderUserStatus()}

      {/* 控制面板 */}
      <Card size="small" style={{ marginBottom: 16 }}>
        <Space wrap>
          <span>AI助手:</span>
          <Select
            style={{ width: 200 }}
            value={selectedAssistant}
            onChange={handleAssistantChange}
            placeholder="选择AI助手"
          >
            {assistants.map(assistant => (
              <Option key={assistant.id} value={assistant.id}>
                {assistant.name}
              </Option>
            ))}
          </Select>

          <span>对话线程:</span>
          <Select
            style={{ width: 200 }}
            value={selectedThread}
            onChange={handleThreadChange}
            placeholder="选择对话线程"
            allowClear
          >
            {threads.map(thread => (
              <Option key={thread.id} value={thread.id}>
                {thread.title}
              </Option>
            ))}
          </Select>

          <Button onClick={handleNewChat}>新建对话</Button>
          
          <Button
            icon={<SettingOutlined />}
            onClick={() => setSettingsVisible(true)}
          >
            设置
          </Button>
        </Space>
      </Card>

      {/* 聊天区域 */}
      <Card
        title={
          <Space>
            <MessageOutlined />
            AI智能对话
            {selectedThread && (
              <Tag color="blue">
                {threads.find(t => t.id === selectedThread)?.title || '当前对话'}
              </Tag>
            )}
          </Space>
        }
        style={{ flex: 1, display: 'flex', flexDirection: 'column' }}
        bodyStyle={{ flex: 1, display: 'flex', flexDirection: 'column', padding: 0 }}
      >
        {/* 消息列表 */}
        <div
          style={{
            flex: 1,
            padding: '16px',
            overflowY: 'auto',
            maxHeight: '400px'
          }}
        >
          {messages.length === 0 ? (
            <Empty
              description="开始与AI助手对话吧！支持上传文件和使用知识库"
              image={Empty.PRESENTED_IMAGE_SIMPLE}
            />
          ) : (
            messages.map(renderMessage)
          )}
          <div ref={messagesEndRef} />
        </div>

        <Divider style={{ margin: 0 }} />

        {/* 文件预览区域 */}
        {uploadedFiles.length > 0 && (
          <div style={{ padding: '8px 16px', backgroundColor: '#fafafa' }}>
            <Text type="secondary">待发送文件：</Text>
            <div style={{ marginTop: 4 }}>
              {uploadedFiles.map((file, index) => (
                <Tag
                  key={index}
                  closable
                  onClose={() => {
                    setUploadedFiles(prev => prev.filter((_, i) => i !== index));
                  }}
                  icon={file.type.startsWith('image/') ? <PictureOutlined /> : <FileOutlined />}
                >
                  {file.name}
                </Tag>
              ))}
            </div>
          </div>
        )}

        {/* 输入区域 */}
        <div style={{ padding: '16px' }}>
          <div style={{ marginBottom: 8 }}>
            <Space>
              <Upload {...uploadProps} showUploadList={false}>
                <Button icon={<CloudUploadOutlined />} size="small">
                  上传文件
                </Button>
              </Upload>
              <Button 
                icon={<BookOutlined />} 
                size="small"
                onClick={() => setSettingsVisible(true)}
              >
                知识库设置
              </Button>
              <Button 
                icon={<SyncOutlined />} 
                size="small"
                onClick={handleSyncMessages}
                loading={syncing}
              >
                同步消息
              </Button>
            </Space>
          </div>
          <Input.Group compact>
            <TextArea
              style={{ width: 'calc(100% - 80px)' }}
              placeholder="输入消息...支持@知识库查询和文件上传"
              value={inputMessage}
              onChange={(e) => setInputMessage(e.target.value)}
              onPressEnter={(e) => {
                if (!e.shiftKey) {
                  e.preventDefault();
                  handleSendMessage();
                }
              }}
              autoSize={{ minRows: 1, maxRows: 3 }}
            />
            <Button
              type="primary"
              icon={<SendOutlined />}
              onClick={handleSendMessage}
              loading={sending}
              style={{ height: 'auto' }}
            >
              发送
            </Button>
          </Input.Group>
        </div>
      </Card>

      {/* 设置弹窗 */}
      <Modal
        title="AI聊天设置"
        open={settingsVisible}
        onCancel={() => setSettingsVisible(false)}
        width={600}
        footer={[
          <Button key="cancel" onClick={() => setSettingsVisible(false)}>
            取消
          </Button>,
          <Button
            key="save"
            type="primary"
            onClick={() => {
              setSettingsVisible(false);
              message.success('设置已保存');
            }}
          >
            保存
          </Button>
        ]}
      >
        <Tabs defaultActiveKey="knowledge">
          <TabPane tab="知识库设置" key="knowledge">
            <div style={{ marginBottom: 16 }}>
              <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: 12 }}>
                <Text strong>使用项目知识库</Text>
                <Switch
                  checked={useKnowledgeBase}
                  onChange={setUseKnowledgeBase}
                  checkedChildren="启用"
                  unCheckedChildren="禁用"
                />
              </div>
              
              {useKnowledgeBase && (
                <div>
                  <Text>选择知识库分类：</Text>
                  <div style={{ marginTop: 8, maxHeight: 200, overflowY: 'auto' }}>
                    <Checkbox.Group
                      style={{ display: 'block' }}
                      value={selectedKnowledgeBases}
                      onChange={setSelectedKnowledgeBases}
                    >
                      {knowledgeBases.map(kb => (
                        <div key={kb.id} style={{ marginBottom: 8, padding: 8, border: '1px solid #f0f0f0', borderRadius: 4 }}>
                          <Checkbox value={kb.id}>
                            <div>
                              <div style={{ fontWeight: 500 }}>{kb.name}</div>
                              {kb.description && (
                                <div style={{ fontSize: 12, color: '#666' }}>{kb.description}</div>
                              )}
                            </div>
                          </Checkbox>
                        </div>
                      ))}
                    </Checkbox.Group>
                  </div>
                  {knowledgeBases.length === 0 && (
                    <Empty 
                      description="暂无知识库分类" 
                      image={Empty.PRESENTED_IMAGE_SIMPLE}
                      style={{ margin: '20px 0' }}
                    />
                  )}
                </div>
              )}
            </div>
          </TabPane>
          
          <TabPane tab="钉钉发送" key="dingtalk">
            <Form form={settingsForm} layout="vertical">
              <Form.Item
                label="自动发送AI回复到钉钉"
                name="autoSendToDingtalk"
                valuePropName="checked"
              >
                <Switch
                  checked={autoSendToDingtalk}
                  onChange={setAutoSendToDingtalk}
                  checkedChildren="自动发送"
                  unCheckedChildren="手动发送"
                />
              </Form.Item>

              {autoSendToDingtalk && (
                <>
                  <Form.Item label="发送目标类型" name="targetType">
                    <Select
                      value={sendTarget.type}
                      onChange={(value) => setSendTarget(prev => ({ ...prev, type: value }))}
                    >
                      <Option value="user">用户</Option>
                      <Option value="group">群组</Option>
                    </Select>
                  </Form.Item>

                  <Form.Item label="目标ID" name="targetId">
                    <Input
                      value={sendTarget.id}
                      onChange={(e) => setSendTarget(prev => ({ ...prev, id: e.target.value }))}
                      placeholder="输入用户ID或群组ID"
                    />
                  </Form.Item>
                  
                  <Form.Item label="消息类型" name="messageType">
                    <Select
                      value={messageType}
                      onChange={setMessageType}
                    >
                      <Option value="text">文本消息</Option>
                      <Option value="card">卡片消息</Option>
                    </Select>
                  </Form.Item>
                </>
              )}
            </Form>
          </TabPane>
          
          <TabPane tab="使用统计" key="stats">
            <div>
              <Row gutter={[16, 16]}>
                <Col span={12}>
                  <Card size="small">
                    <Statistic
                      title="今日对话"
                      value={messages.length}
                      prefix={<MessageOutlined />}
                    />
                  </Card>
                </Col>
                <Col span={12}>
                  <Card size="small">
                    <Statistic
                      title="文件处理"
                      value={messages.filter(m => m.files_processed?.length > 0).length}
                      prefix={<FileOutlined />}
                    />
                  </Card>
                </Col>
                <Col span={12}>
                  <Card size="small">
                    <Statistic
                      title="知识库查询"
                      value={messages.filter(m => m.knowledge_base_used).length}
                      prefix={<BookOutlined />}
                    />
                  </Card>
                </Col>
                <Col span={12}>
                  <Card size="small">
                    <Statistic
                      title="钉钉发送"
                      value={0}
                      prefix={<DingtalkOutlined />}
                    />
                  </Card>
                </Col>
              </Row>
              
              <Alert
                style={{ marginTop: 16 }}
                message="功能提示"
                description="AI助手支持文件上传、知识库查询、钉钉消息发送等功能，让您的工作更加高效。"
                type="info"
                showIcon
              />
            </div>
          </TabPane>
        </Tabs>
      </Modal>
    </div>
  );
};

export default AIChat; 