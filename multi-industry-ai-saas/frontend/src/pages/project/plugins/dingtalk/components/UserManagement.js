import React, { useState, useEffect } from 'react';
import {
  Card,
  Button,
  Space,
  Typography,
  Row,
  Col,
  message,
  Spin,
  Tag,
  Descriptions,
  Alert,
  Modal,
  Steps
} from 'antd';
import {
  DingtalkOutlined,
  UserOutlined,
  SyncOutlined,
  CheckCircleOutlined,
  ExclamationCircleOutlined,
  DeleteOutlined,
  InfoCircleOutlined
} from '@ant-design/icons';
import * as dingtalkService from '../service/dingtalkService';

const { Title, Text, Paragraph } = Typography;
const { Step } = Steps;

/**
 * 用户管理组件
 */
const UserManagement = () => {
  const [loading, setLoading] = useState(true);
  const [syncing, setSyncing] = useState(false);
  const [userInfo, setUserInfo] = useState(null);
  const [helpVisible, setHelpVisible] = useState(false);

  useEffect(() => {
    fetchUserInfo();
    
    // 监听窗口焦点事件，当用户从其他页面返回时自动刷新
    const handleFocus = () => {
      // 延迟刷新，避免频繁调用
      setTimeout(() => {
        fetchUserInfo();
      }, 500);
    };
    
    window.addEventListener('focus', handleFocus);
    
    return () => {
      window.removeEventListener('focus', handleFocus);
    };
  }, []);

  // 获取用户信息
  const fetchUserInfo = async () => {
    setLoading(true);
    try {
      const response = await dingtalkService.getUserInfo();
      if (response.success) {
        setUserInfo(response.data);
      } else {
        message.error('获取用户信息失败');
      }
    } catch (error) {
      console.error('获取用户信息失败:', error);
      message.error('获取用户信息失败');
    } finally {
      setLoading(false);
    }
  };

  // 同步用户信息
  const handleSyncUserInfo = async () => {
    if (!userInfo?.third_party_account) {
      message.error('请先绑定钉钉账号');
      return;
    }

    setSyncing(true);
    try {
      const response = await dingtalkService.syncUserInfo();
      if (response.success) {
        message.success('同步用户信息成功');
        await fetchUserInfo();
      } else {
        message.error('同步用户信息失败: ' + (response.message || '未知错误'));
      }
    } catch (error) {
      console.error('同步用户信息失败:', error);
      message.error('同步用户信息失败');
    } finally {
      setSyncing(false);
    }
  };

  // 清除用户映射
  const handleClearMapping = async () => {
    Modal.confirm({
      title: '确认清除用户映射',
      content: '这将清除钉钉插件中的用户扩展信息，但不会影响账号绑定。确定要继续吗？',
      icon: <ExclamationCircleOutlined />,
      onOk: async () => {
        try {
          const response = await dingtalkService.clearUserMapping();
          if (response.success) {
            message.success('清除用户映射成功');
            await fetchUserInfo();
          } else {
            message.error('清除用户映射失败');
          }
        } catch (error) {
          console.error('清除用户映射失败:', error);
          message.error('清除用户映射失败');
        }
      }
    });
  };

  // 渲染绑定状态
  const renderBindingStatus = () => {
    if (!userInfo) return null;

    const hasBinding = userInfo.third_party_account;
    const hasMapping = userInfo.dingtalk_mapping;

    return (
      <Card 
        title="钉钉账号绑定状态" 
        style={{ marginBottom: 24 }}
        extra={
          <Button
            icon={<SyncOutlined />}
            onClick={fetchUserInfo}
            loading={loading}
            size="small"
          >
            刷新状态
          </Button>
        }
      >
        <Steps current={hasBinding ? (hasMapping ? 2 : 1) : 0} status={hasBinding ? 'finish' : 'error'}>
          <Step
            title="绑定钉钉账号"
            description="在用户资料页面绑定钉钉账号"
            icon={hasBinding ? <CheckCircleOutlined /> : <ExclamationCircleOutlined />}
          />
          <Step
            title="同步用户信息"
            description="同步钉钉用户详细信息到插件"
            icon={hasMapping ? <CheckCircleOutlined /> : <SyncOutlined />}
          />
          <Step
            title="使用AI功能"
            description="开始使用钉钉AI智能功能"
            icon={hasBinding && hasMapping ? <CheckCircleOutlined /> : <InfoCircleOutlined />}
          />
        </Steps>

        <div style={{ marginTop: 24 }}>
          {!hasBinding && (
            <Alert
              message="未绑定钉钉账号"
              description={
                <div>
                  <p>请前往用户资料页面绑定钉钉账号后再使用插件功能。</p>
                  <p style={{ marginTop: 8, fontSize: '12px', color: '#666' }}>
                    提示：绑定完成后请点击右上角的"刷新状态"按钮更新状态
                  </p>
                </div>
              }
              type="warning"
              showIcon
              action={
                <Space>
                  <Button size="small" onClick={() => setHelpVisible(true)}>
                    查看帮助
                  </Button>
                  <Button 
                    size="small" 
                    type="primary"
                    onClick={() => window.open('/project/user/profile', '_blank')}
                  >
                    前往绑定
                  </Button>
                </Space>
              }
            />
          )}

          {hasBinding && !hasMapping && (
            <Alert
              message="建议同步用户信息"
              description="同步钉钉用户详细信息可以获得更好的AI体验。"
              type="info"
              showIcon
              action={
                <Button
                  size="small"
                  type="primary"
                  loading={syncing}
                  onClick={handleSyncUserInfo}
                >
                  立即同步
                </Button>
              }
            />
          )}

          {hasBinding && hasMapping && (
            <Alert
              message="配置完成"
              description="您已成功配置钉钉插件，可以开始使用AI功能了！"
              type="success"
              showIcon
            />
          )}
        </div>
      </Card>
    );
  };

  // 渲染用户详细信息
  const renderUserDetails = () => {
    if (!userInfo) return null;

    return (
      <Row gutter={[16, 16]}>
        {/* 基础绑定信息 */}
        <Col xs={24} lg={12}>
          <Card
            title={
              <Space>
                <DingtalkOutlined style={{ color: '#1890ff' }} />
                基础绑定信息
              </Space>
            }
            size="small"
          >
            {userInfo.third_party_account ? (
              <Descriptions column={1} size="small">
                <Descriptions.Item label="平台">
                  <Tag color="blue">钉钉</Tag>
                </Descriptions.Item>
                <Descriptions.Item label="用户名">
                  {userInfo.third_party_account.platform_username}
                </Descriptions.Item>
                <Descriptions.Item label="用户ID">
                  <Text code>{userInfo.third_party_account.platform_user_id}</Text>
                </Descriptions.Item>
                <Descriptions.Item label="绑定时间">
                  {new Date(userInfo.third_party_account.created_at).toLocaleString()}
                </Descriptions.Item>
                <Descriptions.Item label="更新时间">
                  {new Date(userInfo.third_party_account.updated_at).toLocaleString()}
                </Descriptions.Item>
              </Descriptions>
            ) : (
              <div style={{ textAlign: 'center', padding: '20px 0' }}>
                <ExclamationCircleOutlined style={{ fontSize: 48, color: '#faad14' }} />
                <div style={{ marginTop: 16 }}>
                  <Text type="secondary">未绑定钉钉账号</Text>
                </div>
              </div>
            )}
          </Card>
        </Col>

        {/* 插件扩展信息 */}
        <Col xs={24} lg={12}>
          <Card
            title={
              <Space>
                <UserOutlined style={{ color: '#52c41a' }} />
                插件扩展信息
              </Space>
            }
            size="small"
            extra={
              userInfo.dingtalk_mapping && (
                <Button
                  size="small"
                  danger
                  icon={<DeleteOutlined />}
                  onClick={handleClearMapping}
                >
                  清除
                </Button>
              )
            }
          >
            {userInfo.dingtalk_mapping ? (
              <Descriptions column={1} size="small">
                <Descriptions.Item label="钉钉姓名">
                  {userInfo.dingtalk_mapping.dingtalk_name}
                </Descriptions.Item>
                <Descriptions.Item label="钉钉用户ID">
                  <Text code>{userInfo.dingtalk_mapping.dingtalk_user_id}</Text>
                </Descriptions.Item>
                {userInfo.dingtalk_mapping.dingtalk_mobile && (
                  <Descriptions.Item label="手机号">
                    {userInfo.dingtalk_mapping.dingtalk_mobile}
                  </Descriptions.Item>
                )}
                {userInfo.dingtalk_mapping.dingtalk_email && (
                  <Descriptions.Item label="邮箱">
                    {userInfo.dingtalk_mapping.dingtalk_email}
                  </Descriptions.Item>
                )}
                {userInfo.dingtalk_mapping.dingtalk_department && (
                  <Descriptions.Item label="部门">
                    {userInfo.dingtalk_mapping.dingtalk_department}
                  </Descriptions.Item>
                )}
                {userInfo.dingtalk_mapping.dingtalk_position && (
                  <Descriptions.Item label="职位">
                    {userInfo.dingtalk_mapping.dingtalk_position}
                  </Descriptions.Item>
                )}
                <Descriptions.Item label="同步时间">
                  {new Date(userInfo.dingtalk_mapping.created_at).toLocaleString()}
                </Descriptions.Item>
              </Descriptions>
            ) : (
              <div style={{ textAlign: 'center', padding: '20px 0' }}>
                <SyncOutlined style={{ fontSize: 48, color: '#d9d9d9' }} />
                <div style={{ marginTop: 16 }}>
                  <Text type="secondary">未同步详细信息</Text>
                  <div style={{ marginTop: 8 }}>
                    <Button
                      type="primary"
                      size="small"
                      loading={syncing}
                      onClick={handleSyncUserInfo}
                      disabled={!userInfo.third_party_account}
                    >
                      立即同步
                    </Button>
                  </div>
                </div>
              </div>
            )}
          </Card>
        </Col>
      </Row>
    );
  };

  // 渲染操作面板
  const renderActionPanel = () => {
    if (!userInfo) return null;

    return (
      <Card title="操作面板" style={{ marginTop: 24 }}>
        <Space wrap>
          <Button
            type="primary"
            icon={<SyncOutlined />}
            loading={syncing}
            onClick={handleSyncUserInfo}
            disabled={!userInfo.third_party_account}
          >
            同步用户信息
          </Button>

          <Button
            icon={<InfoCircleOutlined />}
            onClick={() => setHelpVisible(true)}
          >
            查看帮助
          </Button>

          {userInfo.dingtalk_mapping && (
            <Button
              danger
              icon={<DeleteOutlined />}
              onClick={handleClearMapping}
            >
              清除映射信息
            </Button>
          )}
        </Space>

        <div style={{ marginTop: 16 }}>
          <Text type="secondary">
            • 同步用户信息：从钉钉获取用户详细信息，提升AI体验
          </Text>
          <br />
          <Text type="secondary">
            • 清除映射信息：清除插件中的扩展信息，不影响账号绑定
          </Text>
        </div>
      </Card>
    );
  };

  if (loading) {
    return (
      <Card>
        <div style={{ textAlign: 'center', padding: '50px 0' }}>
          <Spin size="large" />
          <div style={{ marginTop: 16 }}>正在加载用户信息...</div>
        </div>
      </Card>
    );
  }

  return (
    <div>
      <Title level={4}>
        <Space>
          <UserOutlined />
          用户管理
        </Space>
      </Title>

      <Paragraph type="secondary">
        管理您的钉钉账号绑定和用户信息同步。绑定钉钉账号后，您可以使用AI聊天等高级功能。
      </Paragraph>

      {renderBindingStatus()}
      {renderUserDetails()}
      {renderActionPanel()}

      {/* 帮助弹窗 */}
      <Modal
        title="钉钉插件使用帮助"
        open={helpVisible}
        onCancel={() => setHelpVisible(false)}
        footer={[
          <Button key="close" type="primary" onClick={() => setHelpVisible(false)}>
            我知道了
          </Button>
        ]}
        width={600}
      >
        <div>
          <Title level={5}>如何绑定钉钉账号？</Title>
          <Paragraph>
            <strong>方法一：直接跳转</strong>
            <br />
            1. 点击上方的"前往绑定"按钮
            <br />
            2. 在个人资料页面找到"第三方账号"标签页
            <br />
            3. 点击钉钉旁边的"绑定"按钮
            <br />
            4. 在弹出的确认框中点击"确定绑定"
            <br />
            5. 在新窗口中完成钉钉授权
            <br />
            6. 授权完成后关闭窗口，返回插件页面点击"刷新状态"
          </Paragraph>
          
          <Paragraph>
            <strong>方法二：手动导航</strong>
            <br />
            1. 点击页面右上角的用户头像，选择"个人资料"
            <br />
            2. 在个人资料页面找到"第三方账号"标签页
            <br />
            3. 点击钉钉旁边的"绑定"按钮，按照提示完成授权
            <br />
            4. 绑定成功后返回插件页面即可使用
          </Paragraph>

          <Title level={5}>为什么要同步用户信息？</Title>
          <Paragraph>
            同步用户信息可以：
            <br />
            • 获取您在钉钉中的详细信息（姓名、部门、职位等）
            <br />
            • 提供更个性化的AI服务
            <br />
            • 支持更精准的消息推送
            <br />
            • 启用高级功能（如部门查询、同事查找等）
          </Paragraph>

          <Title level={5}>常见问题</Title>
          <Paragraph>
            <strong>Q: 绑定后状态没有更新怎么办？</strong>
            <br />
            A: 请点击右上角的"刷新状态"按钮，或者刷新整个页面。
          </Paragraph>
          
          <Paragraph>
            <strong>Q: 绑定时出现404错误怎么办？</strong>
            <br />
            A: 这个问题已经修复，请重新尝试绑定。如果仍有问题，请联系管理员。
          </Paragraph>

          <Title level={5}>数据安全说明</Title>
          <Paragraph>
            • 我们只会获取您的基本信息（用户名、头像等）
            <br />
            • 不会获取您的聊天记录或其他敏感信息
            <br />
            • 您可以随时在个人资料页面解绑账号
            <br />
            • 解绑后相关数据将被安全删除
          </Paragraph>
        </div>
      </Modal>
    </div>
  );
};

export default UserManagement; 