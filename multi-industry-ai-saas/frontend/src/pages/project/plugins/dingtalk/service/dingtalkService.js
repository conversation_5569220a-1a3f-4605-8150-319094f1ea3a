import api from '../../../../../services/api/httpClient';
import { getProjectId, getTenantId } from '../../../../../services/api/httpClient';

/**
 * 获取钉钉机器人插件设置
 * @returns {Promise<Object>} 插件设置
 */
const getSettings = async () => {
  try {
    const projectId = getProjectId();
    const response = await api.get(`/project/${projectId}/plugin/dingtalk/settings`);
    return response;
  } catch (error) {
    console.error('获取钉钉机器人插件设置失败:', error);
    throw error;
  }
};

/**
 * 更新钉钉机器人插件设置
 * @param {Object} settings 插件设置
 * @returns {Promise<Object>} 更新结果
 */
const updateSettings = async (settings) => {
  try {
    const projectId = getProjectId();
    const response = await api.post(`/project/${projectId}/plugin/dingtalk/settings`, settings);
    return response;
  } catch (error) {
    console.error('更新钉钉机器人插件设置失败:', error);
    throw error;
  }
};

/**
 * 获取钉钉机器人Webhook列表
 * @returns {Promise<Object>} Webhook列表
 */
const getWebhooks = async () => {
  try {
    const projectId = getProjectId();
    const response = await api.get(`/project/${projectId}/plugin/dingtalk/webhooks`);
    return response;
  } catch (error) {
    console.error('获取钉钉机器人Webhook列表失败:', error);
    throw error;
  }
};

/**
 * 创建钉钉机器人Webhook
 * @param {Object} webhookData Webhook数据
 * @returns {Promise<Object>} 创建结果
 */
const createWebhook = async (webhookData) => {
  try {
    const projectId = getProjectId();
    const response = await api.post(`/project/${projectId}/plugin/dingtalk/webhooks`, webhookData);
    return response;
  } catch (error) {
    console.error('创建钉钉机器人Webhook失败:', error);
    throw error;
  }
};

/**
 * 更新钉钉机器人Webhook
 * @param {string} webhookId Webhook ID
 * @param {Object} webhookData Webhook数据
 * @returns {Promise<Object>} 更新结果
 */
const updateWebhook = async (webhookId, webhookData) => {
  try {
    const projectId = getProjectId();
    const response = await api.put(`/project/${projectId}/plugin/dingtalk/webhooks/${webhookId}`, webhookData);
    return response;
  } catch (error) {
    console.error('更新钉钉机器人Webhook失败:', error);
    throw error;
  }
};

/**
 * 删除钉钉机器人Webhook
 * @param {string} webhookId Webhook ID
 * @returns {Promise<Object>} 删除结果
 */
const deleteWebhook = async (webhookId) => {
  try {
    const projectId = getProjectId();
    const response = await api.delete(`/project/${projectId}/plugin/dingtalk/webhooks/${webhookId}`);
    return response;
  } catch (error) {
    console.error('删除钉钉机器人Webhook失败:', error);
    throw error;
  }
};

/**
 * 测试钉钉机器人Webhook
 * @param {Object} testData 测试数据
 * @returns {Promise<Object>} 测试结果
 */
const testWebhook = async (testData) => {
  try {
    const projectId = getProjectId();
    const response = await api.post(`/project/${projectId}/plugin/dingtalk/test`, testData);
    return response;
  } catch (error) {
    console.error('测试钉钉机器人Webhook失败:', error);
    throw error;
  }
};

/**
 * 获取钉钉通知日志
 * @param {Object} params 查询参数
 * @returns {Promise<Object>} 通知日志
 */
const getLogs = async (params = {}) => {
  try {
    const projectId = getProjectId();
    const response = await api.get(`/project/${projectId}/plugin/dingtalk/logs`, { params });
    return response;
  } catch (error) {
    console.error('获取钉钉通知日志失败:', error);
    throw error;
  }
};

/**
 * 同步钉钉数据
 * @param {Object} syncData 同步数据
 * @returns {Promise<Object>} 同步结果
 */
const syncDingtalk = async (syncData) => {
  try {
    const projectId = getProjectId();
    const response = await api.post(`/project/${projectId}/plugin/dingtalk/sync`, syncData);
    return response;
  } catch (error) {
    console.error('同步钉钉数据失败:', error);
    throw error;
  }
};

// ==================== AI 功能相关 API ====================

/**
 * 获取用户钉钉绑定信息
 * @returns {Promise<Object>} 用户绑定信息
 */
const getUserInfo = async () => {
  try {
    const projectId = getProjectId();
    const response = await api.get(`/project/${projectId}/plugin/dingtalk/auth/user-info`);
    return response;
  } catch (error) {
    console.error('获取用户钉钉绑定信息失败:', error);
    throw error;
  }
};

/**
 * 同步钉钉用户信息到插件
 * @returns {Promise<Object>} 同步结果
 */
const syncUserInfo = async () => {
  try {
    const projectId = getProjectId();
    const response = await api.post(`/project/${projectId}/plugin/dingtalk/auth/sync-info`);
    return response;
  } catch (error) {
    console.error('同步钉钉用户信息失败:', error);
    throw error;
  }
};

/**
 * 清除钉钉插件用户映射
 * @returns {Promise<Object>} 清除结果
 */
const clearUserMapping = async () => {
  try {
    const projectId = getProjectId();
    const response = await api.delete(`/project/${projectId}/plugin/dingtalk/auth/clear-mapping`);
    return response;
  } catch (error) {
    console.error('清除钉钉用户映射失败:', error);
    throw error;
  }
};

/**
 * AI聊天对话
 * @param {Object} chatData 聊天数据
 * @returns {Promise<Object>} AI回复
 */
const aiChat = async (chatData) => {
  try {
    const projectId = getProjectId();
    const response = await api.post(`/project/${projectId}/plugin/dingtalk/ai/chat-json`, chatData);
    return response;
  } catch (error) {
    console.error('AI聊天失败:', error);
    throw error;
  }
};

/**
 * 发送AI回复到钉钉
 * @param {Object} sendData 发送数据
 * @returns {Promise<Object>} 发送结果
 */
const sendAIResponseToDingtalk = async (sendData) => {
  try {
    const projectId = getProjectId();
    const response = await api.post(`/project/${projectId}/plugin/dingtalk/ai/send-to-dingtalk`, sendData);
    return response;
  } catch (error) {
    console.error('发送AI回复到钉钉失败:', error);
    throw error;
  }
};

/**
 * 获取可用的AI助手列表
 * @returns {Promise<Object>} AI助手列表
 */
const getAIAssistants = async () => {
  try {
    const projectId = getProjectId();
    const response = await api.get(`/project/${projectId}/plugin/dingtalk/ai/assistants`);
    return response;
  } catch (error) {
    console.error('获取AI助手列表失败:', error);
    throw error;
  }
};

/**
 * 获取对话线程列表
 * @param {string} assistantId 助手ID（可选）
 * @returns {Promise<Object>} 对话线程列表
 */
const getAIThreads = async (assistantId = null) => {
  try {
    const projectId = getProjectId();
    const params = assistantId ? { assistant_id: assistantId } : {};
    const response = await api.get(`/project/${projectId}/plugin/dingtalk/ai/threads`, { params });
    return response;
  } catch (error) {
    console.error('获取对话线程列表失败:', error);
    throw error;
  }
};

/**
 * 发送钉钉消息（通用接口）
 * @param {Object} messageData 消息数据
 * @returns {Promise<Object>} 发送结果
 */
const sendMessage = async (messageData) => {
  try {
    const projectId = getProjectId();
    const response = await api.post(`/project/${projectId}/plugin/dingtalk/send`, messageData);
    return response;
  } catch (error) {
    console.error('发送钉钉消息失败:', error);
    throw error;
  }
};

/**
 * AI聊天对话 - 支持文件上传
 * @param {FormData} formData 包含消息和文件的FormData
 * @returns {Promise<Object>} AI回复
 */
const aiChatWithFiles = async (formData) => {
  try {
    const projectId = getProjectId();
    const response = await api.post(`/project/${projectId}/plugin/dingtalk/ai/chat`, formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    });
    return response;
  } catch (error) {
    console.error('AI聊天失败:', error);
    throw error;
  }
};

/**
 * 同步钉钉消息
 * @param {Object} syncData 同步参数
 * @returns {Promise<Object>} 同步结果
 */
const syncDingtalkMessages = async (syncData) => {
  try {
    const projectId = getProjectId();
    const response = await api.post(`/project/${projectId}/plugin/dingtalk/ai/sync-messages`, syncData);
    return response;
  } catch (error) {
    console.error('同步钉钉消息失败:', error);
    throw error;
  }
};

/**
 * 获取知识库列表
 * @returns {Promise<Object>} 知识库列表
 */
const getKnowledgeBases = async () => {
  try {
    const projectId = getProjectId();
    const response = await api.get(`/project/${projectId}/knowledge-base/categories`);
    return response;
  } catch (error) {
    console.error('获取知识库列表失败:', error);
    return { success: false, data: [] };
  }
};

export {
  // 原有功能
  getSettings,
  updateSettings,
  getWebhooks,
  createWebhook,
  updateWebhook,
  deleteWebhook,
  testWebhook,
  getLogs,
  syncDingtalk,
  sendMessage,
  
  // AI功能
  getUserInfo,
  syncUserInfo,
  clearUserMapping,
  aiChat,
  sendAIResponseToDingtalk,
  getAIAssistants,
  getAIThreads,
  aiChatWithFiles,
  syncDingtalkMessages,
  getKnowledgeBases
};
