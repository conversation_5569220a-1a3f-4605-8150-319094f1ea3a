import React, { useState, useEffect } from 'react';
import {
  Card,
  Button,
  Space,
  Typography,
  Row,
  Col,
  Tabs,
  message,
  Spin,
  Form,
  Input,
  Switch,
  Select,
  Table,
  Modal,
  Tag,
  Badge
} from 'antd';
import {
  PlusOutlined,
  SaveOutlined,
  SyncOutlined,
  DeleteOutlined,
  EditOutlined,
  SendOutlined,
  RobotOutlined,
  InfoCircleOutlined,
  MessageOutlined,
  UserOutlined
} from '@ant-design/icons';
import * as dingtalkService from './service/dingtalkService';
import AIChat from './components/AIChat';
import UserManagement from './components/UserManagement';

const { Paragraph } = Typography;
const { TabPane } = Tabs;
const { Option } = Select;
const { TextArea } = Input;

/**
 * 钉钉机器人插件组件
 */
const DingTalkRobot = () => {
  const [loading, setLoading] = useState(true);
  const [settings, setSettings] = useState(null);
  const [webhooks, setWebhooks] = useState([]);
  const [logs, setLogs] = useState([]);
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 10,
    total: 0
  });
  const [activeTab, setActiveTab] = useState('ai-chat');
  const [webhookModalVisible, setWebhookModalVisible] = useState(false);
  const [editingWebhook, setEditingWebhook] = useState(null);
  const [testModalVisible, setTestModalVisible] = useState(false);
  const [syncModalVisible, setSyncModalVisible] = useState(false);
  const [accessToken, setAccessToken] = useState('');
  const [testContent, setTestContent] = useState('');
  const [testingWebhookId, setTestingWebhookId] = useState(null);

  const [webhookForm] = Form.useForm();
  const [settingsForm] = Form.useForm();

  // 获取插件设置
  const fetchSettings = async () => {
    setLoading(true);
    try {
      const response = await dingtalkService.getSettings();

      if (response.success) {
        setSettings(response.data);

        // 设置表单值
        settingsForm.setFieldsValue({
          enable_dingtalk: response.data.enable_dingtalk,
          notification_level: response.data.notification_level,
          retry_count: response.data.retry_count,
          retry_interval: response.data.retry_interval,
          default_template: response.data.default_template
        });
      } else {
        message.error('获取钉钉机器人插件设置失败');
      }
    } catch (error) {
      console.error('获取钉钉机器人插件设置失败:', error);
      // 避免连续报错，只显示一次错误消息
      if (error.response && error.response.status === 403) {
        message.error('钉钉机器人插件未激活，请联系管理员激活插件');
        // 设置一个默认的设置对象，避免页面报错
        setSettings({
          enable_dingtalk: false,
          notification_level: 'all',
          retry_count: 3,
          retry_interval: 60,
          default_template: '### {{title}}\n\n{{content}}\n\n> 来自系统通知 - {{time}}'
        });
      } else {
        message.error('获取钉钉机器人插件设置失败');
      }
    } finally {
      setLoading(false);
    }
  };

  // 获取Webhook列表
  const fetchWebhooks = async () => {
    try {
      const response = await dingtalkService.getWebhooks();

      if (response.success) {
        setWebhooks(response.data || []);
      } else {
        message.error('获取钉钉机器人Webhook列表失败');
      }
    } catch (error) {
      console.error('获取钉钉机器人Webhook列表失败:', error);
      message.error('获取钉钉机器人Webhook列表失败');
    }
  };

  // 获取通知日志
  const fetchLogs = async (page = 1, pageSize = 10) => {
    try {
      const response = await dingtalkService.getLogs({
        skip: (page - 1) * pageSize,
        limit: pageSize
      });

      if (response && response.success && response.data) {
        // 检查数据格式
        const logsData = response.data.logs || [];
        const paginationData = response.data.pagination || {};
        
        setLogs(logsData);
        setPagination({
          current: paginationData.current || page,
          pageSize: paginationData.pageSize || pageSize,
          total: paginationData.total || 0
        });
        
        // 如果没有数据，不显示错误消息
        if (logsData.length === 0) {
          console.log('暂无钉钉通知日志');
        }
      } else {
        // 只有在真正出错时才显示错误消息
        console.warn('获取钉钉通知日志返回格式异常:', response);
        setLogs([]);
        setPagination({
          current: page,
          pageSize: pageSize,
          total: 0
        });
      }
    } catch (error) {
      console.error('获取钉钉通知日志失败:', error);
      // 只有在网络错误或服务器错误时才显示错误消息
      if (error.response && error.response.status >= 500) {
        message.error('获取钉钉通知日志失败');
      } else {
        // 其他情况（如404、403等）只记录日志，不显示错误消息
        console.log('钉钉通知日志接口调用失败，可能是权限问题或接口不存在');
        setLogs([]);
        setPagination({
          current: page,
          pageSize: pageSize,
          total: 0
        });
      }
    }
  };

  // 处理标签页切换
  const handleTabChange = (key) => {
    setActiveTab(key);

    if (key === 'logs') {
      fetchLogs();
    } else if (key === 'webhooks') {
      fetchWebhooks();
    }
  };

  // 处理保存设置
  const handleSaveSettings = async () => {
    try {
      const values = await settingsForm.validateFields();

      const response = await dingtalkService.updateSettings(values);

      if (response.success) {
        message.success('钉钉机器人插件设置保存成功');
        fetchSettings();
      } else {
        message.error('钉钉机器人插件设置保存失败');
      }
    } catch (error) {
      console.error('保存钉钉机器人插件设置失败:', error);
      message.error('保存钉钉机器人插件设置失败');
    }
  };

  // 处理添加Webhook
  const handleAddWebhook = () => {
    setEditingWebhook(null);
    webhookForm.resetFields();

    // 设置默认值
    webhookForm.setFieldsValue({
      name: '',
      webhook_url: '',
      secret: '',
      enabled: true,
      message_template: settings?.default_template || '',
      notification_types: ['all'],
      target_users: [],
      target_groups: []
    });

    setWebhookModalVisible(true);
  };

  // 处理编辑Webhook
  const handleEditWebhook = (webhook) => {
    setEditingWebhook(webhook);

    // 设置表单值
    webhookForm.setFieldsValue({
      name: webhook.name,
      webhook_url: webhook.webhook_url,
      secret: webhook.secret,
      enabled: webhook.enabled,
      message_template: webhook.message_template,
      notification_types: webhook.notification_types,
      target_users: webhook.target_users,
      target_groups: webhook.target_groups
    });

    setWebhookModalVisible(true);
  };

  // 处理删除Webhook
  const handleDeleteWebhook = (webhookId) => {
    Modal.confirm({
      title: '确认删除',
      content: '确定要删除这个机器人配置吗？',
      onOk: async () => {
        try {
          const response = await dingtalkService.deleteWebhook(webhookId);

          if (response.success) {
            message.success('删除机器人配置成功');
            fetchWebhooks();
          } else {
            message.error('删除机器人配置失败');
          }
        } catch (error) {
          console.error('删除机器人配置失败:', error);
          message.error('删除机器人配置失败');
        }
      }
    });
  };

  // 处理保存Webhook
  const handleSaveWebhook = async () => {
    try {
      const values = await webhookForm.validateFields();

      let response;
      if (editingWebhook) {
        response = await dingtalkService.updateWebhook(editingWebhook.id, values);
      } else {
        response = await dingtalkService.createWebhook(values);
      }

      if (response.success) {
        message.success(`${editingWebhook ? '更新' : '创建'}机器人配置成功`);
        setWebhookModalVisible(false);
        fetchWebhooks();
      } else {
        message.error(`${editingWebhook ? '更新' : '创建'}机器人配置失败`);
      }
    } catch (error) {
      console.error(`${editingWebhook ? '更新' : '创建'}机器人配置失败:`, error);
      message.error(`${editingWebhook ? '更新' : '创建'}机器人配置失败`);
    }
  };

  // 处理测试Webhook
  const handleTestWebhook = (webhookId) => {
    setTestingWebhookId(webhookId);
    setTestContent('这是一条测试消息');
    setTestModalVisible(true);
  };

  // 处理发送测试消息
  const handleSendTestMessage = async () => {
    try {
      const response = await dingtalkService.testWebhook({
        webhook_id: testingWebhookId,
        content: testContent
      });

      if (response.success) {
        message.success('测试消息发送成功');
        setTestModalVisible(false);
      } else {
        message.error('测试消息发送失败');
      }
    } catch (error) {
      console.error('发送测试消息失败:', error);
      message.error('发送测试消息失败');
    }
  };

  // 处理同步钉钉数据
  const handleSyncDingtalk = async () => {
    try {
      if (!accessToken) {
        message.warning('请输入钉钉API访问令牌');
        return;
      }

      const response = await dingtalkService.syncDingtalk({
        access_token: accessToken
      });

      if (response.success) {
        message.success(`同步成功: ${response.data.users_synced} 个用户, ${response.data.groups_synced} 个群组`);
        setSyncModalVisible(false);
      } else {
        message.error('同步钉钉数据失败');
      }
    } catch (error) {
      console.error('同步钉钉数据失败:', error);
      message.error('同步钉钉数据失败');
    }
  };

  // 初始化
  useEffect(() => {
    // 使用一个标志来跟踪是否已经显示过错误消息
    let errorShown = false;

    const initializePlugin = async () => {
      try {
        await fetchSettings();
        // 只有在设置获取成功后才获取 Webhooks
        if (!errorShown) {
          await fetchWebhooks();
        }
      } catch (error) {
        errorShown = true;
        console.error('初始化钉钉机器人插件失败:', error);
      }
    };

    initializePlugin();
  }, []);

  // Webhook表格列定义
  const webhookColumns = [
    {
      title: '名称',
      dataIndex: 'name',
      key: 'name'
    },
    {
      title: 'Webhook地址',
      dataIndex: 'webhook_url',
      key: 'webhook_url',
      ellipsis: true
    },
    {
      title: '状态',
      dataIndex: 'enabled',
      key: 'enabled',
      render: (enabled) => (
        enabled ? (
          <Badge status="success" text="启用" />
        ) : (
          <Badge status="default" text="禁用" />
        )
      )
    },
    {
      title: '通知类型',
      dataIndex: 'notification_types',
      key: 'notification_types',
      render: (types) => (
        <Space>
          {types.includes('all') ? (
            <Tag color="blue">所有</Tag>
          ) : (
            types.map(type => (
              <Tag key={type} color="blue">{type}</Tag>
            ))
          )}
        </Space>
      )
    },
    {
      title: '操作',
      key: 'action',
      width: 200,
      render: (_, record) => (
        <Space>
          <Button
            type="link"
            icon={<EditOutlined />}
            onClick={() => handleEditWebhook(record)}
          >
            编辑
          </Button>
          <Button
            type="link"
            icon={<SendOutlined />}
            onClick={() => handleTestWebhook(record.id)}
          >
            测试
          </Button>
          <Button
            type="link"
            danger
            icon={<DeleteOutlined />}
            onClick={() => handleDeleteWebhook(record.id)}
          >
            删除
          </Button>
        </Space>
      )
    }
  ];

  // 日志表格列定义
  const logColumns = [
    {
      title: '时间',
      dataIndex: 'created_at',
      key: 'created_at',
      render: (text) => text ? new Date(text).toLocaleString() : '-'
    },
    {
      title: '通知类型',
      dataIndex: 'notification_type',
      key: 'notification_type'
    },
    {
      title: '标题',
      dataIndex: 'title',
      key: 'title'
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status) => (
        status === 'success' ? (
          <Badge status="success" text="成功" />
        ) : (
          <Badge status="error" text="失败" />
        )
      )
    },
    {
      title: '重试次数',
      dataIndex: 'retry_count',
      key: 'retry_count'
    },
    {
      title: '错误信息',
      dataIndex: 'error_message',
      key: 'error_message',
      ellipsis: true,
      render: (text) => text || '-'
    }
  ];

  if (loading && !settings) {
    return (
      <Card>
        <div style={{ textAlign: 'center', padding: '50px 0' }}>
          <Spin size="large" />
          <p style={{ marginTop: 16 }}>加载中...</p>
        </div>
      </Card>
    );
  }

  return (
    <Card
      title={
        <Space>
          <RobotOutlined />
          <span>钉钉智能机器人</span>
        </Space>
      }
      extra={
        <Space>
          <Button
            icon={<SyncOutlined />}
            onClick={() => setSyncModalVisible(true)}
          >
            同步钉钉数据
          </Button>
        </Space>
      }
    >
      <Tabs activeKey={activeTab} onChange={handleTabChange}>
        <TabPane 
          tab={
            <Space>
              <MessageOutlined />
              AI智能对话
            </Space>
          } 
          key="ai-chat"
        >
          <AIChat />
        </TabPane>

        <TabPane 
          tab={
            <Space>
              <UserOutlined />
              用户管理
            </Space>
          } 
          key="user-management"
        >
          <UserManagement />
        </TabPane>

        <TabPane tab="基本设置" key="settings">
          <Form
            form={settingsForm}
            layout="vertical"
            initialValues={{
              enable_dingtalk: true,
              notification_level: 'all',
              retry_count: 3,
              retry_interval: 60,
              default_template: '### {{title}}\n\n{{content}}\n\n> 来自系统通知 - {{time}}'
            }}
          >
            <Row gutter={16}>
              <Col span={12}>
                <Form.Item
                  name="enable_dingtalk"
                  label="启用钉钉通知"
                  valuePropName="checked"
                >
                  <Switch />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item
                  name="notification_level"
                  label="通知级别"
                >
                  <Select>
                    <Option value="all">所有通知</Option>
                    <Option value="high_only">仅高优先级</Option>
                    <Option value="custom">自定义</Option>
                  </Select>
                </Form.Item>
              </Col>
            </Row>
            <Row gutter={16}>
              <Col span={12}>
                <Form.Item
                  name="retry_count"
                  label="重试次数"
                >
                  <Select>
                    <Option value={0}>不重试</Option>
                    <Option value={1}>1次</Option>
                    <Option value={2}>2次</Option>
                    <Option value={3}>3次</Option>
                    <Option value={5}>5次</Option>
                  </Select>
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item
                  name="retry_interval"
                  label="重试间隔(秒)"
                >
                  <Select>
                    <Option value={10}>10秒</Option>
                    <Option value={30}>30秒</Option>
                    <Option value={60}>1分钟</Option>
                    <Option value={300}>5分钟</Option>
                  </Select>
                </Form.Item>
              </Col>
            </Row>
            <Form.Item
              name="default_template"
              label="默认消息模板"
            >
              <TextArea
                rows={6}
                placeholder="请输入默认消息模板，可使用变量：{{title}}、{{content}}、{{time}}"
              />
            </Form.Item>
            <Form.Item>
              <Button
                type="primary"
                icon={<SaveOutlined />}
                onClick={handleSaveSettings}
              >
                保存设置
              </Button>
            </Form.Item>
          </Form>
        </TabPane>

        <TabPane tab="机器人配置" key="webhooks">
          <div style={{ marginBottom: 16 }}>
            <Button
              type="primary"
              icon={<PlusOutlined />}
              onClick={handleAddWebhook}
            >
              添加机器人
            </Button>
          </div>
          <Table
            columns={webhookColumns}
            dataSource={webhooks}
            rowKey="id"
            pagination={false}
          />
        </TabPane>

        <TabPane tab="通知日志" key="logs">
          <Table
            columns={logColumns}
            dataSource={logs}
            rowKey="id"
            pagination={{
              current: pagination.current,
              pageSize: pagination.pageSize,
              total: pagination.total,
              onChange: (page, pageSize) => {
                fetchLogs(page, pageSize);
              }
            }}
          />
        </TabPane>
      </Tabs>

      {/* Webhook编辑对话框 */}
      <Modal
        title={`${editingWebhook ? '编辑' : '添加'}机器人`}
        open={webhookModalVisible}
        onCancel={() => setWebhookModalVisible(false)}
        onOk={handleSaveWebhook}
        width={800}
      >
        <Form
          form={webhookForm}
          layout="vertical"
        >
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="name"
                label="机器人名称"
                rules={[{ required: true, message: '请输入机器人名称' }]}
              >
                <Input placeholder="请输入机器人名称" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="enabled"
                label="启用状态"
                valuePropName="checked"
              >
                <Switch />
              </Form.Item>
            </Col>
          </Row>
          <Form.Item
            name="webhook_url"
            label="Webhook地址"
            rules={[{ required: true, message: '请输入Webhook地址' }]}
          >
            <Input placeholder="请输入钉钉机器人的Webhook地址" />
          </Form.Item>
          <Form.Item
            name="secret"
            label="安全密钥"
          >
            <Input placeholder="请输入钉钉机器人的安全密钥（可选）" />
          </Form.Item>
          <Form.Item
            name="message_template"
            label="消息模板"
          >
            <TextArea
              rows={4}
              placeholder="请输入消息模板，可使用变量：{{title}}、{{content}}、{{time}}"
            />
          </Form.Item>
          <Form.Item
            name="notification_types"
            label="通知类型"
          >
            <Select mode="multiple" placeholder="请选择通知类型">
              <Option value="all">所有通知</Option>
              <Option value="high">高优先级</Option>
              <Option value="medium">中优先级</Option>
              <Option value="low">低优先级</Option>
            </Select>
          </Form.Item>
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="target_users"
                label="目标用户"
              >
                <Select mode="multiple" placeholder="请选择目标用户（可选）">
                  <Option value="all">所有用户</Option>
                </Select>
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="target_groups"
                label="目标群组"
              >
                <Select mode="multiple" placeholder="请选择目标群组（可选）">
                  <Option value="all">所有群组</Option>
                </Select>
              </Form.Item>
            </Col>
          </Row>
        </Form>
      </Modal>

      {/* 测试消息对话框 */}
      <Modal
        title="发送测试消息"
        open={testModalVisible}
        onCancel={() => setTestModalVisible(false)}
        onOk={handleSendTestMessage}
      >
        <Form layout="vertical">
          <Form.Item
            label="测试消息内容"
          >
            <TextArea
              rows={4}
              value={testContent}
              onChange={(e) => setTestContent(e.target.value)}
              placeholder="请输入测试消息内容"
            />
          </Form.Item>
        </Form>
      </Modal>

      {/* 同步钉钉数据对话框 */}
      <Modal
        title="同步钉钉数据"
        open={syncModalVisible}
        onCancel={() => setSyncModalVisible(false)}
        onOk={handleSyncDingtalk}
      >
        <Form layout="vertical">
          <Form.Item
            label="钉钉API访问令牌"
            required
            help="请输入钉钉开放平台获取的访问令牌"
          >
            <Input
              value={accessToken}
              onChange={(e) => setAccessToken(e.target.value)}
              placeholder="请输入钉钉API访问令牌"
            />
          </Form.Item>
          <Paragraph>
            <InfoCircleOutlined style={{ marginRight: 8 }} />
            同步将获取钉钉用户和群组数据，用于通知发送。
          </Paragraph>
        </Form>
      </Modal>
    </Card>
  );
};

export default DingTalkRobot;
