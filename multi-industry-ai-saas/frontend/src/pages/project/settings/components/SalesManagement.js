import React, { useState, useEffect } from 'react';
import {
  Card,
  Tabs,
  Table,
  Button,
  Space,
  Modal,
  Form,
  Input,
  Select,
  Switch,
  message,
  Tooltip,
  InputNumber,
  Popconfirm
} from 'antd';
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  ExclamationCircleOutlined,
  ShopOutlined,
  CreditCardOutlined
} from '@ant-design/icons';
import apiService from '../../../../services/api';

const { TabPane } = Tabs;
const { Option } = Select;
const { TextArea } = Input;

/**
 * 销售管理组件
 * 包含销售渠道和支付方式管理
 */
const SalesManagement = () => {
  const [activeTab, setActiveTab] = useState('channel');
  const [channelList, setChannelList] = useState([]);
  const [paymentMethodList, setPaymentMethodList] = useState([]);
  const [loading, setLoading] = useState(false);
  const [channelModalVisible, setChannelModalVisible] = useState(false);
  const [paymentModalVisible, setPaymentModalVisible] = useState(false);
  const [editingItem, setEditingItem] = useState(null);
  const [channelForm] = Form.useForm();
  const [paymentForm] = Form.useForm();

  // 初始化数据
  useEffect(() => {
    if (activeTab === 'channel') {
      fetchChannels();
    } else if (activeTab === 'payment') {
      fetchPaymentMethods();
    }
  }, [activeTab]);

  // 获取销售渠道列表
  const fetchChannels = async () => {
    try {
      setLoading(true);
      const response = await apiService.project.channel.getList();
      setChannelList(response.items || []);
    } catch (error) {
      console.error('获取销售渠道列表失败:', error);
      message.error('获取销售渠道列表失败');
    } finally {
      setLoading(false);
    }
  };

  // 获取支付方式列表
  const fetchPaymentMethods = async () => {
    try {
      setLoading(true);
      const response = await apiService.project.paymentMethod.getList();
      setPaymentMethodList(response.items || []);
    } catch (error) {
      console.error('获取支付方式列表失败:', error);
      message.error('获取支付方式列表失败');
    } finally {
      setLoading(false);
    }
  };

  // 打开销售渠道表单
  const showChannelModal = (record = null) => {
    setEditingItem(record);
    channelForm.resetFields();
    
    if (record) {
      channelForm.setFieldsValue({
        name: record.name,
        code: record.code,
        type: record.type,
        description: record.description,
        is_active: record.is_active
      });
    }
    
    setChannelModalVisible(true);
  };

  // 打开支付方式表单
  const showPaymentModal = (record = null) => {
    setEditingItem(record);
    paymentForm.resetFields();
    
    if (record) {
      paymentForm.setFieldsValue({
        name: record.name,
        code: record.code,
        icon: record.icon,
        description: record.description,
        is_active: record.is_active,
        is_default: record.is_default,
        sort_order: record.sort_order
      });
    }
    
    setPaymentModalVisible(true);
  };

  // 提交销售渠道表单
  const handleChannelSubmit = async () => {
    try {
      const values = await channelForm.validateFields();
      setLoading(true);
      
      if (editingItem) {
        // 更新销售渠道
        await apiService.project.channel.update(editingItem.id, values);
        message.success('销售渠道更新成功');
      } else {
        // 创建销售渠道
        await apiService.project.channel.create(values);
        message.success('销售渠道创建成功');
      }
      
      setChannelModalVisible(false);
      fetchChannels();
    } catch (error) {
      console.error('提交销售渠道失败:', error);
      message.error('提交销售渠道失败: ' + (error.response?.data?.detail || error.message));
    } finally {
      setLoading(false);
    }
  };

  // 提交支付方式表单
  const handlePaymentSubmit = async () => {
    try {
      const values = await paymentForm.validateFields();
      setLoading(true);
      
      if (editingItem) {
        // 更新支付方式
        await apiService.project.paymentMethod.update(editingItem.id, values);
        message.success('支付方式更新成功');
      } else {
        // 创建支付方式
        await apiService.project.paymentMethod.create(values);
        message.success('支付方式创建成功');
      }
      
      setPaymentModalVisible(false);
      fetchPaymentMethods();
    } catch (error) {
      console.error('提交支付方式失败:', error);
      message.error('提交支付方式失败: ' + (error.response?.data?.detail || error.message));
    } finally {
      setLoading(false);
    }
  };

  // 删除销售渠道
  const handleDeleteChannel = async (id) => {
    try {
      setLoading(true);
      await apiService.project.channel.delete(id);
      message.success('销售渠道删除成功');
      fetchChannels();
    } catch (error) {
      console.error('删除销售渠道失败:', error);
      message.error('删除销售渠道失败: ' + (error.response?.data?.detail || error.message));
    } finally {
      setLoading(false);
    }
  };

  // 删除支付方式
  const handleDeletePayment = async (id) => {
    try {
      setLoading(true);
      await apiService.project.paymentMethod.delete(id);
      message.success('支付方式删除成功');
      fetchPaymentMethods();
    } catch (error) {
      console.error('删除支付方式失败:', error);
      message.error('删除支付方式失败: ' + (error.response?.data?.detail || error.message));
    } finally {
      setLoading(false);
    }
  };

  // 销售渠道表格列定义
  const channelColumns = [
    {
      title: '渠道名称',
      dataIndex: 'name',
      key: 'name',
      width: 150
    },
    {
      title: '渠道编码',
      dataIndex: 'code',
      key: 'code',
      width: 120
    },
    {
      title: '渠道类型',
      dataIndex: 'type',
      key: 'type',
      width: 120,
      render: (text) => {
        const typeMap = {
          online: '线上',
          offline: '线下',
          other: '其他'
        };
        return typeMap[text] || text;
      }
    },
    {
      title: '描述',
      dataIndex: 'description',
      key: 'description',
      ellipsis: true
    },
    {
      title: '状态',
      dataIndex: 'is_active',
      key: 'is_active',
      width: 100,
      render: (active) => (
        <span style={{ color: active ? '#52c41a' : '#ff4d4f' }}>
          {active ? '启用' : '禁用'}
        </span>
      )
    },
    {
      title: '操作',
      key: 'action',
      width: 150,
      render: (_, record) => (
        <Space size="small">
          <Button
            type="text"
            icon={<EditOutlined />}
            onClick={() => showChannelModal(record)}
          />
          <Popconfirm
            title="确定要删除此销售渠道吗？"
            onConfirm={() => handleDeleteChannel(record.id)}
            okText="确定"
            cancelText="取消"
          >
            <Button
              type="text"
              danger
              icon={<DeleteOutlined />}
            />
          </Popconfirm>
        </Space>
      )
    }
  ];

  // 支付方式表格列定义
  const paymentColumns = [
    {
      title: '支付方式名称',
      dataIndex: 'name',
      key: 'name',
      width: 150
    },
    {
      title: '支付方式编码',
      dataIndex: 'code',
      key: 'code',
      width: 120
    },
    {
      title: '图标',
      dataIndex: 'icon',
      key: 'icon',
      width: 100,
      render: (icon) => icon ? <img src={icon} alt="icon" style={{ width: 24, height: 24 }} /> : '-'
    },
    {
      title: '描述',
      dataIndex: 'description',
      key: 'description',
      ellipsis: true
    },
    {
      title: '默认',
      dataIndex: 'is_default',
      key: 'is_default',
      width: 80,
      render: (isDefault) => isDefault ? '是' : '否'
    },
    {
      title: '排序',
      dataIndex: 'sort_order',
      key: 'sort_order',
      width: 80
    },
    {
      title: '状态',
      dataIndex: 'is_active',
      key: 'is_active',
      width: 100,
      render: (active) => (
        <span style={{ color: active ? '#52c41a' : '#ff4d4f' }}>
          {active ? '启用' : '禁用'}
        </span>
      )
    },
    {
      title: '操作',
      key: 'action',
      width: 150,
      render: (_, record) => (
        <Space size="small">
          <Button
            type="text"
            icon={<EditOutlined />}
            onClick={() => showPaymentModal(record)}
          />
          <Popconfirm
            title="确定要删除此支付方式吗？"
            onConfirm={() => handleDeletePayment(record.id)}
            okText="确定"
            cancelText="取消"
          >
            <Button
              type="text"
              danger
              icon={<DeleteOutlined />}
            />
          </Popconfirm>
        </Space>
      )
    }
  ];

  return (
    <div className="sales-management">
      <Tabs activeKey={activeTab} onChange={setActiveTab}>
        <TabPane
          tab={
            <span>
              <ShopOutlined />
              销售渠道
            </span>
          }
          key="channel"
        >
          <div style={{ marginBottom: 16 }}>
            <Button
              type="primary"
              icon={<PlusOutlined />}
              onClick={() => showChannelModal()}
            >
              新增销售渠道
            </Button>
          </div>
          <Table
            columns={channelColumns}
            dataSource={channelList}
            rowKey="id"
            loading={loading}
            pagination={{ pageSize: 10 }}
          />
        </TabPane>

        <TabPane
          tab={
            <span>
              <CreditCardOutlined />
              支付方式
            </span>
          }
          key="payment"
        >
          <div style={{ marginBottom: 16 }}>
            <Button
              type="primary"
              icon={<PlusOutlined />}
              onClick={() => showPaymentModal()}
            >
              新增支付方式
            </Button>
          </div>
          <Table
            columns={paymentColumns}
            dataSource={paymentMethodList}
            rowKey="id"
            loading={loading}
            pagination={{ pageSize: 10 }}
          />
        </TabPane>
      </Tabs>

      {/* 销售渠道表单 */}
      <Modal
        title={editingItem ? '编辑销售渠道' : '新增销售渠道'}
        open={channelModalVisible}
        onOk={handleChannelSubmit}
        onCancel={() => setChannelModalVisible(false)}
        confirmLoading={loading}
        maskClosable={false}
        destroyOnClose
      >
        <Form
          form={channelForm}
          layout="vertical"
          initialValues={{
            type: 'offline',
            is_active: true
          }}
        >
          <Form.Item
            name="name"
            label="渠道名称"
            rules={[{ required: true, message: '请输入渠道名称' }]}
          >
            <Input placeholder="请输入渠道名称" />
          </Form.Item>

          <Form.Item
            name="code"
            label="渠道编码"
          >
            <Input placeholder="请输入渠道编码" />
          </Form.Item>

          <Form.Item
            name="type"
            label="渠道类型"
            rules={[{ required: true, message: '请选择渠道类型' }]}
          >
            <Select placeholder="请选择渠道类型">
              <Option value="online">线上</Option>
              <Option value="offline">线下</Option>
              <Option value="other">其他</Option>
            </Select>
          </Form.Item>

          <Form.Item
            name="description"
            label="描述"
          >
            <TextArea rows={4} placeholder="请输入描述" />
          </Form.Item>

          <Form.Item
            name="is_active"
            label="状态"
            valuePropName="checked"
          >
            <Switch checkedChildren="启用" unCheckedChildren="禁用" />
          </Form.Item>
        </Form>
      </Modal>

      {/* 支付方式表单 */}
      <Modal
        title={editingItem ? '编辑支付方式' : '新增支付方式'}
        open={paymentModalVisible}
        onOk={handlePaymentSubmit}
        onCancel={() => setPaymentModalVisible(false)}
        confirmLoading={loading}
        maskClosable={false}
        destroyOnClose
      >
        <Form
          form={paymentForm}
          layout="vertical"
          initialValues={{
            is_active: true,
            is_default: false,
            sort_order: 0
          }}
        >
          <Form.Item
            name="name"
            label="支付方式名称"
            rules={[{ required: true, message: '请输入支付方式名称' }]}
          >
            <Input placeholder="请输入支付方式名称" />
          </Form.Item>

          <Form.Item
            name="code"
            label="支付方式编码"
          >
            <Input placeholder="请输入支付方式编码" />
          </Form.Item>

          <Form.Item
            name="icon"
            label="图标URL"
          >
            <Input placeholder="请输入图标URL" />
          </Form.Item>

          <Form.Item
            name="description"
            label="描述"
          >
            <TextArea rows={4} placeholder="请输入描述" />
          </Form.Item>

          <Form.Item
            name="sort_order"
            label="排序"
          >
            <InputNumber min={0} style={{ width: '100%' }} />
          </Form.Item>

          <Form.Item
            name="is_default"
            label="设为默认"
            valuePropName="checked"
          >
            <Switch checkedChildren="是" unCheckedChildren="否" />
          </Form.Item>

          <Form.Item
            name="is_active"
            label="状态"
            valuePropName="checked"
          >
            <Switch checkedChildren="启用" unCheckedChildren="禁用" />
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default SalesManagement;
