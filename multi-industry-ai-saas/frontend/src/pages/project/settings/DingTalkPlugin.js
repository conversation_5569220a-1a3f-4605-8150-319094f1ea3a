import React, { useState, useEffect } from 'react';
import {
  Card, Tabs, Button, Space, message, Spin, Alert, 
  Form, Input, Switch, Select, Table, Tag, Modal, 
  Tooltip, Progress, Descriptions, Divider, Row, Col, InputNumber, List, Avatar, Empty
} from 'antd';
import {
  SettingOutlined, SyncOutlined, RobotOutlined, 
  DatabaseOutlined, HistoryOutlined, InfoCircleOutlined,
  CheckCircleOutlined, ExclamationCircleOutlined, UserOutlined
} from '@ant-design/icons';
import { useAuth } from '../../../contexts/AuthContext';
import RoleBasedContent from '../../../components/dashboard/RoleBasedContent';
import { api } from '../../../services/api';
import request from '../../../services/request';
import './DingTalkPlugin.css';

const { TabPane } = Tabs;
const { Option } = Select;
const { TextArea } = Input;

/**
 * 钉钉插件管理页面
 * 基于用户角色显示不同的功能模块
 */
const DingTalkPlugin = () => {
  const { user, hasRole } = useAuth();
  const [loading, setLoading] = useState(true);
  const [capabilities, setCapabilities] = useState(null);
  const [settings, setSettings] = useState(null);
  const [webhooks, setWebhooks] = useState([]);
  const [syncHistory, setSyncHistory] = useState([]);
  const [aiIntegration, setAiIntegration] = useState(null);
  const [systemStatus, setSystemStatus] = useState(null);
  const [activeTab, setActiveTab] = useState('overview');
  const [syncModalVisible, setSyncModalVisible] = useState(false);
  const [syncLoading, setSyncLoading] = useState(false);
  const [form] = Form.useForm();
  const [webhookModalVisible, setWebhookModalVisible] = useState(false);
  const [editingWebhook, setEditingWebhook] = useState(null);
  const [webhookForm] = Form.useForm();
  const [dingtalkUsers, setDingtalkUsers] = useState([]);
  const [usersLoading, setUsersLoading] = useState(false);
  const [chatMessages, setChatMessages] = useState([]);
  const [chatInput, setChatInput] = useState('');
  const [chatLoading, setChatLoading] = useState(false);
  const [messageLoading, setMessageLoading] = useState(false);
  const [aiConfigModalVisible, setAiConfigModalVisible] = useState(false);
  const [keywordInputVisible, setKeywordInputVisible] = useState(false);
  const [templateModalVisible, setTemplateModalVisible] = useState(false);
  
  // 新增：AI配置相关状态
  const [aiConfigs, setAiConfigs] = useState([]);
  const [aiModels, setAiModels] = useState([]);
  const [selectedAiConfig, setSelectedAiConfig] = useState(null);

  useEffect(() => {
    fetchData();
  }, []);

  // 新增：当AI配置模态框打开时初始化选中的配置
  useEffect(() => {
    if (aiConfigModalVisible && aiIntegration && aiConfigs.length > 0) {
      const configId = aiIntegration.ai_model_config?.model;
      if (configId) {
        const config = aiConfigs.find(c => c.id === configId);
        if (config) {
          setSelectedAiConfig(config);
          console.log('钉钉插件 - 初始化选中AI配置:', config);
        }
      }
    }
  }, [aiConfigModalVisible, aiIntegration, aiConfigs]);

  // 获取数据
  const fetchData = async () => {
    try {
      setLoading(true);
      
      // 获取项目ID
      const projectId = localStorage.getItem('project_id');
      if (!projectId) {
        throw new Error('未找到项目ID');
      }
      
      // 获取用户能力 - 使用正确的项目级插件路径
      const capabilitiesRes = await api.get(`/project/${projectId}/plugin/dingtalk/admin/capabilities`);
      setCapabilities(capabilitiesRes.data);
      
      // 获取基础设置
      const settingsRes = await api.get(`/project/${projectId}/plugin/dingtalk/settings`);
      setSettings(settingsRes.settings);
      setWebhooks(settingsRes.webhooks || []);
      
      // 获取AI配置和模型数据
      try {
        const aiConfigsRes = await request.get(`/project/${projectId}/ai/configs`);
        setAiConfigs(aiConfigsRes.data || []);
        
        const aiModelsRes = await request.get(`/project/${projectId}/ai/models`);
        setAiModels(aiModelsRes.data || []);
        
        console.log('钉钉插件 - 获取AI配置:', aiConfigsRes.data);
        console.log('钉钉插件 - 获取AI模型:', aiModelsRes.data);
      } catch (error) {
        console.error('获取AI配置和模型数据失败:', error);
        // 不影响主要功能，只记录错误
      }
      
      // 如果有管理员权限，获取更多数据
      if (capabilitiesRes.data.features.knowledge_sync) {
        const historyRes = await api.get(`/project/${projectId}/plugin/dingtalk/admin/knowledge-sync-history`);
        setSyncHistory(historyRes.data.history || []);
      }
      
      if (capabilitiesRes.data.features.ai_integration) {
        const aiRes = await api.get(`/project/${projectId}/plugin/dingtalk/admin/ai-integration`);
        setAiIntegration(aiRes.data);
      }
      
      if (capabilitiesRes.data.features.system_integration) {
        const statusRes = await api.get(`/project/${projectId}/plugin/dingtalk/admin/system-status`);
        setSystemStatus(statusRes.data);
      }
      
      if (capabilitiesRes.data.features.user_management) {
        const usersRes = await api.get(`/project/${projectId}/plugin/dingtalk/users`);
        setDingtalkUsers(usersRes.data.users || []);
      }
      
    } catch (error) {
      console.error('获取钉钉插件数据失败:', error);
      message.error('获取插件数据失败');
    } finally {
      setLoading(false);
    }
  };

  // 同步知识库
  const handleKnowledgeSync = async (values) => {
    try {
      setSyncLoading(true);
      
      // 获取项目ID
      const projectId = localStorage.getItem('project_id');
      if (!projectId) {
        throw new Error('未找到项目ID');
      }
      
      const syncOptions = {
        sync_type: values.sync_type || 'full',
        target_knowledge_bases: values.target_knowledge_bases || ['project', 'ai'],
        include_documents: values.include_documents || false,
        ...values
      };
      
      const result = await api.post(`/project/${projectId}/plugin/dingtalk/admin/sync-knowledge`, syncOptions);
      
      if (result.success) {
        message.success('知识库同步完成');
        setSyncModalVisible(false);
        fetchData(); // 刷新数据
      } else {
        message.error('知识库同步失败');
      }
      
    } catch (error) {
      console.error('知识库同步失败:', error);
      message.error('知识库同步失败');
    } finally {
      setSyncLoading(false);
    }
  };

  // 渲染概览页面
  const renderOverview = () => (
    <div className="dingtalk-overview">
      <Card title="插件状态" className="status-card">
        <Descriptions column={2}>
          <Descriptions.Item label="用户角色">
            <Tag color="blue">{capabilities?.role_name}</Tag>
          </Descriptions.Item>
          <Descriptions.Item label="权限级别">
            <Tag color={capabilities?.role === 'super_admin' ? 'red' : capabilities?.role === 'admin' ? 'orange' : 'green'}>
              {capabilities?.role === 'super_admin' ? '超级管理员' : 
               capabilities?.role === 'admin' ? '管理员' : '普通用户'}
            </Tag>
          </Descriptions.Item>
          <Descriptions.Item label="可用功能">
            <Space wrap>
              {capabilities?.features.user_management && <Tag color="green">用户管理</Tag>}
              {capabilities?.features.ai_chat && <Tag color="blue">AI对话</Tag>}
              {capabilities?.features.message_sending && <Tag color="cyan">消息发送</Tag>}
              {capabilities?.features.knowledge_sync && <Tag color="purple">知识库同步</Tag>}
              {capabilities?.features.ai_integration && <Tag color="orange">AI集成</Tag>}
            </Space>
          </Descriptions.Item>
        </Descriptions>
        
        <RoleBasedContent roles={['project_admin', 'tenant_admin']}>
          <Divider />
          <Alert
            message="管理员功能"
            description="您拥有管理员权限，可以配置插件设置、管理Webhook、同步知识库等。"
            type="info"
            showIcon
          />
        </RoleBasedContent>
      </Card>

      <RoleBasedContent roles={['project_admin', 'tenant_admin']}>
        <Card title="系统状态" className="system-status-card">
          {systemStatus && (
            <div className="status-grid">
              <div className="status-item">
                <div className="status-label">访问令牌</div>
                <div className={`status-value ${systemStatus.token_status}`}>
                  {systemStatus.token_status === 'available' ? 
                    <><CheckCircleOutlined style={{color: '#52c41a'}} /> 可用</> :
                    <><ExclamationCircleOutlined style={{color: '#ff4d4f'}} /> 不可用</>
                  }
                </div>
              </div>
              <div className="status-item">
                <div className="status-label">Webhook状态</div>
                <div className={`status-value ${systemStatus.webhook_status}`}>
                  {systemStatus.webhook_status === 'active' ? 
                    <><CheckCircleOutlined style={{color: '#52c41a'}} /> 活跃</> :
                    <><ExclamationCircleOutlined style={{color: '#ff4d4f'}} /> 未配置</>
                  }
                </div>
              </div>
              <div className="status-item">
                <div className="status-label">AI集成</div>
                <div className={`status-value ${systemStatus.ai_integration_status}`}>
                  {systemStatus.ai_integration_status === 'configured' ? 
                    <><CheckCircleOutlined style={{color: '#52c41a'}} /> 已配置</> :
                    <><ExclamationCircleOutlined style={{color: '#ff4d4f'}} /> 未配置</>
                  }
                </div>
              </div>
              <div className="status-item">
                <div className="status-label">知识库同步</div>
                <div className={`status-value ${systemStatus.knowledge_sync_status}`}>
                  {systemStatus.knowledge_sync_status === 'completed' ? 
                    <><CheckCircleOutlined style={{color: '#52c41a'}} /> 已同步</> :
                    systemStatus.knowledge_sync_status === 'never_synced' ?
                    <><ExclamationCircleOutlined style={{color: '#faad14'}} /> 未同步</> :
                    <><ExclamationCircleOutlined style={{color: '#ff4d4f'}} /> 同步失败</>
                  }
                </div>
              </div>
            </div>
          )}
        </Card>
      </RoleBasedContent>
    </div>
  );

  // 渲染知识库同步页面
  const renderKnowledgeSync = () => (
    <div className="knowledge-sync-section">
      <Card 
        title="知识库同步" 
        extra={
          <Button 
            type="primary" 
            icon={<SyncOutlined />}
            onClick={() => setSyncModalVisible(true)}
          >
            开始同步
          </Button>
        }
      >
        <Alert
          message="知识库同步说明"
          description="钉钉知识库同步功能可以将钉钉平台的组织架构、用户信息、群组信息等数据同步到项目知识库和AI知识库中，便于AI助手回答相关问题。"
          type="info"
          showIcon
          style={{ marginBottom: 16 }}
        />
        
        <Table
          dataSource={syncHistory}
          columns={[
            {
              title: '同步时间',
              dataIndex: 'created_at',
              key: 'created_at',
              render: (text) => new Date(text).toLocaleString()
            },
            {
              title: '同步类型',
              dataIndex: 'sync_type',
              key: 'sync_type',
              render: (type) => {
                const typeMap = {
                  'full': { text: '全量同步', color: 'blue' },
                  'incremental': { text: '增量同步', color: 'green' },
                  'selective': { text: '选择性同步', color: 'orange' }
                };
                const config = typeMap[type] || { text: type, color: 'default' };
                return <Tag color={config.color}>{config.text}</Tag>;
              }
            },
            {
              title: '同步状态',
              dataIndex: 'sync_status',
              key: 'sync_status',
              render: (status) => {
                const statusMap = {
                  'completed': { text: '完成', color: 'success' },
                  'failed': { text: '失败', color: 'error' },
                  'running': { text: '进行中', color: 'processing' }
                };
                const config = statusMap[status] || { text: status, color: 'default' };
                return <Tag color={config.color}>{config.text}</Tag>;
              }
            },
            {
              title: '同步结果',
              dataIndex: 'sync_result',
              key: 'sync_result',
              render: (result) => {
                if (!result || !result.statistics) return '-';
                return (
                  <Tooltip title={`项目知识库: ${result.statistics.project_kb_synced || 0}条, AI知识库: ${result.statistics.ai_kb_synced || 0}条`}>
                    <span>共 {result.statistics.synced_count || 0} 条</span>
                  </Tooltip>
                );
              }
            }
          ]}
          pagination={{ pageSize: 10 }}
          size="small"
        />
      </Card>
    </div>
  );

  // 渲染AI集成页面
  const renderAIIntegration = () => (
    <div className="ai-integration-section">
      <Card title="AI集成配置" 
        extra={
          <Button type="primary" onClick={() => setAiConfigModalVisible(true)}>
            配置AI集成
          </Button>
        }
      >
        <Alert
          message="AI集成功能"
          description="配置钉钉机器人的AI集成功能，包括自动回复、触发关键词、回复模板等。"
          type="info"
          showIcon
          style={{ marginBottom: 16 }}
        />
        
        {aiIntegration ? (
          <div>
            <Descriptions column={2} bordered style={{ marginBottom: 16 }}>
              <Descriptions.Item label="集成类型">
                <Tag color="blue">{aiIntegration.integration_type}</Tag>
              </Descriptions.Item>
              <Descriptions.Item label="自动回复">
                <Switch 
                  checked={aiIntegration.auto_reply_enabled} 
                  onChange={handleToggleAutoReply}
                />
              </Descriptions.Item>
              <Descriptions.Item label="AI模型配置" span={2}>
                <div>
                  <div>配置: {(() => {
                    const configId = aiIntegration.ai_model_config?.model;
                    if (configId) {
                      const config = aiConfigs.find(c => c.id === configId);
                      if (config) {
                        return `${config.model?.display_name || config.model?.name || '未知模型'} (${config.name})`;
                      }
                    }
                    return aiIntegration.ai_model_config?.model || '未配置';
                  })()}</div>
                  <div>温度: {aiIntegration.ai_model_config?.temperature || 0.7}</div>
                  <div>最大令牌: {aiIntegration.ai_model_config?.max_tokens || 1000}</div>
                </div>
              </Descriptions.Item>
              <Descriptions.Item label="触发关键词" span={2}>
                <Space wrap>
                  {(aiIntegration.trigger_keywords || []).map(keyword => (
                    <Tag key={keyword} closable onClose={() => handleRemoveKeyword(keyword)}>
                      {keyword}
                    </Tag>
                  ))}
                  <Tag 
                    style={{ background: '#fff', borderStyle: 'dashed' }}
                    onClick={() => setKeywordInputVisible(true)}
                  >
                    + 添加关键词
                  </Tag>
                </Space>
              </Descriptions.Item>
            </Descriptions>
            
            <Card title="回复模板管理" size="small">
              <List
                dataSource={Object.entries(aiIntegration.reply_templates || {})}
                renderItem={([key, template]) => (
                  <List.Item
                    actions={[
                      <Button size="small" type="link" onClick={() => handleEditTemplate(key, template)}>
                        编辑
                      </Button>,
                      <Button size="small" type="link" danger onClick={() => handleDeleteTemplate(key)}>
                        删除
                      </Button>
                    ]}
                  >
                    <List.Item.Meta
                      title={key}
                      description={template}
                    />
                  </List.Item>
                )}
              />
              <Button 
                type="dashed" 
                style={{ width: '100%', marginTop: 8 }}
                onClick={() => setTemplateModalVisible(true)}
              >
                添加回复模板
              </Button>
            </Card>
          </div>
        ) : (
          <Empty 
            description="尚未配置AI集成"
            image={Empty.PRESENTED_IMAGE_SIMPLE}
          >
            <Button type="primary" onClick={() => setAiConfigModalVisible(true)}>
              立即配置
            </Button>
          </Empty>
        )}
      </Card>
    </div>
  );

  // 渲染用户管理页面
  const renderUserManagement = () => (
    <div className="user-management-section">
      <Card title="钉钉用户管理" 
        extra={
          <Button icon={<SyncOutlined />} onClick={handleRefreshUsers}>
            刷新用户
          </Button>
        }
      >
        <Alert
          message="用户管理功能"
          description="查看和管理钉钉组织架构中的用户信息，支持用户同步和信息查看。"
          type="info"
          showIcon
          style={{ marginBottom: 16 }}
        />
        
        <Table
          dataSource={dingtalkUsers}
          columns={[
            {
              title: '用户头像',
              dataIndex: 'avatar',
              key: 'avatar',
              width: 80,
              render: (avatar, record) => (
                <Avatar src={avatar} icon={<UserOutlined />} />
              )
            },
            {
              title: '姓名',
              dataIndex: 'name',
              key: 'name',
              width: 120
            },
            {
              title: '手机号',
              dataIndex: 'mobile',
              key: 'mobile',
              width: 120
            },
            {
              title: '邮箱',
              dataIndex: 'email',
              key: 'email',
              width: 180
            },
            {
              title: '部门',
              dataIndex: 'department',
              key: 'department',
              width: 120
            },
            {
              title: '职位',
              dataIndex: 'position',
              key: 'position',
              width: 120
            },
            {
              title: '状态',
              dataIndex: 'status',
              key: 'status',
              width: 80,
              render: (status) => (
                <Tag color={status === 'active' ? 'green' : 'red'}>
                  {status === 'active' ? '活跃' : '离线'}
                </Tag>
              )
            }
          ]}
          pagination={{ pageSize: 10 }}
          loading={usersLoading}
        />
      </Card>
    </div>
  );

  // 渲染AI对话页面
  const renderAIChat = () => (
    <div className="ai-chat-section">
      <Card title="AI智能对话">
        <Alert
          message="AI对话功能"
          description="与钉钉AI助手进行智能对话，支持文本、图片等多种消息类型。"
          type="info"
          showIcon
          style={{ marginBottom: 16 }}
        />
        
        <div className="chat-container" style={{ height: 400, border: '1px solid #d9d9d9', borderRadius: 6, padding: 16, marginBottom: 16 }}>
          <div className="chat-messages" style={{ height: 320, overflowY: 'auto', marginBottom: 16 }}>
            {chatMessages.map((msg, index) => (
              <div key={index} className={`chat-message ${msg.type}`} style={{ 
                marginBottom: 12,
                textAlign: msg.type === 'user' ? 'right' : 'left'
              }}>
                <div style={{
                  display: 'inline-block',
                  padding: '8px 12px',
                  borderRadius: 8,
                  backgroundColor: msg.type === 'user' ? '#1890ff' : '#f0f0f0',
                  color: msg.type === 'user' ? 'white' : 'black',
                  maxWidth: '70%'
                }}>
                  {msg.content}
                </div>
                <div style={{ fontSize: 12, color: '#999', marginTop: 4 }}>
                  {new Date(msg.timestamp).toLocaleTimeString()}
                </div>
              </div>
            ))}
          </div>
        </div>
        
        <div className="chat-input">
          <Input.Group compact>
            <Input
              style={{ width: 'calc(100% - 80px)' }}
              placeholder="输入消息..."
              value={chatInput}
              onChange={(e) => setChatInput(e.target.value)}
              onPressEnter={handleSendChat}
            />
            <Button type="primary" onClick={handleSendChat} loading={chatLoading}>
              发送
            </Button>
          </Input.Group>
        </div>
      </Card>
    </div>
  );

  // 渲染消息发送页面
  const renderMessageSending = () => (
    <div className="message-sending-section">
      <Card title="消息发送">
        <Alert
          message="消息发送功能"
          description="向钉钉用户或群组发送通知消息，支持多种消息类型和模板。"
          type="info"
          showIcon
          style={{ marginBottom: 16 }}
        />
        
        <Form
          layout="vertical"
          onFinish={handleSendMessage}
        >
          <Form.Item
            name="title"
            label="消息标题"
            rules={[{ required: true, message: '请输入消息标题' }]}
          >
            <Input placeholder="请输入消息标题" />
          </Form.Item>
          
          <Form.Item
            name="content"
            label="消息内容"
            rules={[{ required: true, message: '请输入消息内容' }]}
          >
            <TextArea rows={4} placeholder="请输入消息内容" />
          </Form.Item>
          
          <Form.Item
            name="type"
            label="消息类型"
            initialValue="message"
          >
            <Select>
              <Option value="message">普通消息</Option>
              <Option value="notification">系统通知</Option>
              <Option value="alert">警告消息</Option>
              <Option value="info">信息提示</Option>
            </Select>
          </Form.Item>
          
          <Form.Item
            name="target_users"
            label="目标用户"
          >
            <Select mode="multiple" placeholder="选择目标用户（可选）">
              {dingtalkUsers.map(user => (
                <Option key={user.id} value={user.id}>{user.name}</Option>
              ))}
            </Select>
          </Form.Item>
          
          <Form.Item>
            <Button type="primary" htmlType="submit" loading={messageLoading}>
              发送消息
            </Button>
          </Form.Item>
        </Form>
      </Card>
    </div>
  );

  // 渲染基础设置页面
  const renderBasicSettings = () => (
    <div className="basic-settings-section">
      <Row gutter={[16, 16]}>
        <Col xs={24} lg={12}>
          <Card title="钉钉应用配置" style={{ marginBottom: 16 }}>
            <Form
              layout="vertical"
              initialValues={settings}
              onFinish={handleSettingsUpdate}
            >
              <Form.Item
                name="enable_dingtalk"
                label="启用钉钉通知"
                valuePropName="checked"
              >
                <Switch />
              </Form.Item>
              
              <Form.Item
                name="notification_level"
                label="通知级别"
                rules={[{ required: true, message: '请选择通知级别' }]}
              >
                <Select>
                  <Option value="all">所有通知</Option>
                  <Option value="high_only">仅高优先级</Option>
                  <Option value="custom">自定义</Option>
                </Select>
              </Form.Item>
              
              <Form.Item
                name="retry_count"
                label="重试次数"
                rules={[{ required: true, message: '请输入重试次数' }]}
              >
                <InputNumber min={0} max={10} style={{ width: '100%' }} />
              </Form.Item>
              
              <Form.Item
                name="retry_interval"
                label="重试间隔(秒)"
                rules={[{ required: true, message: '请输入重试间隔' }]}
              >
                <InputNumber min={10} max={300} style={{ width: '100%' }} />
              </Form.Item>
              
              <Form.Item
                name="default_template"
                label="默认消息模板"
              >
                <TextArea rows={4} placeholder="### {{title}}\n\n{{content}}\n\n> 来自系统通知 - {{time}}" />
              </Form.Item>
              
              <Form.Item>
                <Button type="primary" htmlType="submit">
                  保存设置
                </Button>
              </Form.Item>
            </Form>
          </Card>
        </Col>
        
        <Col xs={24} lg={12}>
          <Card title="Webhook管理" 
            extra={
              <Button type="primary" onClick={() => setWebhookModalVisible(true)}>
                添加Webhook
              </Button>
            }
          >
            <List
              dataSource={webhooks}
              renderItem={(webhook) => (
                <List.Item
                  actions={[
                    <Button size="small" type="link" onClick={() => handleEditWebhook(webhook)}>
                      编辑
                    </Button>,
                    <Button size="small" type="link" danger onClick={() => handleDeleteWebhook(webhook.id)}>
                      删除
                    </Button>
                  ]}
                >
                  <List.Item.Meta
                    title={
                      <Space>
                        <span>{webhook.name}</span>
                        <Tag color={webhook.enabled ? 'green' : 'red'}>
                          {webhook.enabled ? '已启用' : '已禁用'}
                        </Tag>
                      </Space>
                    }
                    description={
                      <div>
                        <div>URL: {webhook.webhook_url || '未配置'}</div>
                        <div>通知类型: {webhook.notification_types?.join(', ') || '全部'}</div>
                      </div>
                    }
                  />
                </List.Item>
              )}
            />
          </Card>
        </Col>
      </Row>
    </div>
  );

  // 更新设置
  const handleSettingsUpdate = async (values) => {
    try {
      const projectId = localStorage.getItem('project_id');
      if (!projectId) {
        throw new Error('未找到项目ID');
      }
      
      const result = await api.post(`/project/${projectId}/plugin/dingtalk/settings`, values);
      
      if (result.success !== false) {
        message.success('设置更新成功');
        setSettings(result);
      } else {
        message.error('设置更新失败');
      }
    } catch (error) {
      console.error('更新设置失败:', error);
      message.error('更新设置失败');
    }
  };

  // 编辑Webhook
  const handleEditWebhook = (webhook) => {
    setEditingWebhook(webhook);
    setWebhookModalVisible(true);
    webhookForm.setFieldsValue(webhook);
  };

  // 删除Webhook
  const handleDeleteWebhook = async (webhookId) => {
    try {
      const projectId = localStorage.getItem('project_id');
      if (!projectId) {
        throw new Error('未找到项目ID');
      }
      
      await api.delete(`/project/${projectId}/plugin/dingtalk/webhooks/${webhookId}`);
      message.success('Webhook删除成功');
      fetchData(); // 刷新数据
    } catch (error) {
      console.error('删除Webhook失败:', error);
      message.error('删除Webhook失败');
    }
  };

  // 保存Webhook
  const handleWebhookSave = async (values) => {
    try {
      const projectId = localStorage.getItem('project_id');
      if (!projectId) {
        throw new Error('未找到项目ID');
      }
      
      if (editingWebhook) {
        // 更新
        await api.put(`/project/${projectId}/plugin/dingtalk/webhooks/${editingWebhook.id}`, values);
        message.success('Webhook更新成功');
      } else {
        // 创建
        await api.post(`/project/${projectId}/plugin/dingtalk/webhooks`, values);
        message.success('Webhook创建成功');
      }
      
      setWebhookModalVisible(false);
      setEditingWebhook(null);
      webhookForm.resetFields();
      fetchData(); // 刷新数据
    } catch (error) {
      console.error('保存Webhook失败:', error);
      message.error('保存Webhook失败');
    }
  };

  // 刷新用户
  const handleRefreshUsers = async () => {
    try {
      setUsersLoading(true);
      const projectId = localStorage.getItem('project_id');
      if (!projectId) {
        throw new Error('未找到项目ID');
      }
      
      const usersRes = await api.get(`/project/${projectId}/plugin/dingtalk/users`);
      setDingtalkUsers(usersRes.data.users || []);
    } catch (error) {
      console.error('刷新用户失败:', error);
      message.error('刷新用户失败');
    } finally {
      setUsersLoading(false);
    }
  };

  // 发送消息
  const handleSendMessage = async (values) => {
    try {
      setMessageLoading(true);
      const projectId = localStorage.getItem('project_id');
      if (!projectId) {
        throw new Error('未找到项目ID');
      }
      
      const result = await api.post(`/project/${projectId}/plugin/dingtalk/send-message`, values);
      
      if (result.success) {
        message.success('消息发送成功');
      } else {
        message.error('消息发送失败');
      }
    } catch (error) {
      console.error('发送消息失败:', error);
      message.error('发送消息失败');
    } finally {
      setMessageLoading(false);
    }
  };

  // 发送聊天消息
  const handleSendChat = async () => {
    try {
      setChatLoading(true);
      const projectId = localStorage.getItem('project_id');
      if (!projectId) {
        throw new Error('未找到项目ID');
      }
      
      const result = await api.post(`/project/${projectId}/plugin/dingtalk/ai/chat`, {
        message: chatInput
      });
      
      if (result.success) {
        // 添加用户消息
        const userMessage = {
          content: chatInput,
          type: 'user',
          timestamp: new Date().toISOString()
        };
        
        // 添加AI回复
        const aiMessage = {
          content: result.data.message,
          type: 'ai',
          timestamp: result.data.created_at
        };
        
        setChatMessages([...chatMessages, userMessage, aiMessage]);
        setChatInput('');
        message.success('消息发送成功');
      } else {
        message.error('消息发送失败');
      }
    } catch (error) {
      console.error('发送聊天消息失败:', error);
      message.error('发送聊天消息失败');
    } finally {
      setChatLoading(false);
    }
  };

  // AI集成相关处理函数
  const handleToggleAutoReply = async (checked) => {
    try {
      const projectId = localStorage.getItem('project_id');
      if (!projectId) {
        throw new Error('未找到项目ID');
      }
      
      const updatedConfig = {
        ...aiIntegration,
        auto_reply_enabled: checked
      };
      
      const result = await api.post(`/project/${projectId}/plugin/dingtalk/admin/ai-integration`, updatedConfig);
      
      if (result.success) {
        setAiIntegration(result.data);
        message.success('自动回复设置已更新');
      } else {
        message.error('更新失败');
      }
    } catch (error) {
      console.error('更新自动回复设置失败:', error);
      message.error('更新失败');
    }
  };

  const handleRemoveKeyword = async (keyword) => {
    try {
      const projectId = localStorage.getItem('project_id');
      if (!projectId) {
        throw new Error('未找到项目ID');
      }
      
      const updatedKeywords = (aiIntegration.trigger_keywords || []).filter(k => k !== keyword);
      const updatedConfig = {
        ...aiIntegration,
        trigger_keywords: updatedKeywords
      };
      
      const result = await api.post(`/project/${projectId}/plugin/dingtalk/admin/ai-integration`, updatedConfig);
      
      if (result.success) {
        setAiIntegration(result.data);
        message.success('关键词已删除');
      } else {
        message.error('删除失败');
      }
    } catch (error) {
      console.error('删除关键词失败:', error);
      message.error('删除失败');
    }
  };

  const handleEditTemplate = (key, template) => {
    // 实现模板编辑逻辑
    setTemplateModalVisible(true);
  };

  const handleDeleteTemplate = async (key) => {
    try {
      const projectId = localStorage.getItem('project_id');
      if (!projectId) {
        throw new Error('未找到项目ID');
      }
      
      const updatedTemplates = { ...aiIntegration.reply_templates };
      delete updatedTemplates[key];
      
      const updatedConfig = {
        ...aiIntegration,
        reply_templates: updatedTemplates
      };
      
      const result = await api.post(`/project/${projectId}/plugin/dingtalk/admin/ai-integration`, updatedConfig);
      
      if (result.success) {
        setAiIntegration(result.data);
        message.success('模板已删除');
      } else {
        message.error('删除失败');
      }
    } catch (error) {
      console.error('删除模板失败:', error);
      message.error('删除失败');
    }
  };

  // AI配置保存
  const handleAiConfigSave = async (values) => {
    try {
      const projectId = localStorage.getItem('project_id');
      if (!projectId) {
        throw new Error('未找到项目ID');
      }
      
      const result = await api.post(`/project/${projectId}/plugin/dingtalk/admin/ai-integration`, values);
      
      if (result.success) {
        setAiIntegration(result.data);
        setAiConfigModalVisible(false);
        message.success('AI配置保存成功');
      } else {
        message.error('保存失败');
      }
    } catch (error) {
      console.error('保存AI配置失败:', error);
      message.error('保存失败');
    }
  };

  // 模板保存
  const handleTemplateSave = async (values) => {
    try {
      const projectId = localStorage.getItem('project_id');
      if (!projectId) {
        throw new Error('未找到项目ID');
      }
      
      const updatedTemplates = {
        ...aiIntegration?.reply_templates,
        [values.template_name]: values.template_content
      };
      
      const updatedConfig = {
        ...aiIntegration,
        reply_templates: updatedTemplates
      };
      
      const result = await api.post(`/project/${projectId}/plugin/dingtalk/admin/ai-integration`, updatedConfig);
      
      if (result.success) {
        setAiIntegration(result.data);
        setTemplateModalVisible(false);
        message.success('模板保存成功');
      } else {
        message.error('保存失败');
      }
    } catch (error) {
      console.error('保存模板失败:', error);
      message.error('保存失败');
    }
  };

  // 新增：处理AI模型选择变化
  const handleAiModelChange = (configId) => {
    const config = aiConfigs.find(c => c.id === configId);
    setSelectedAiConfig(config);
    console.log('钉钉插件 - 选择AI配置:', config);
  };

  if (loading) {
    return (
      <div style={{ textAlign: 'center', padding: '50px' }}>
        <Spin size="large" />
        <div style={{ marginTop: 16 }}>加载钉钉插件数据...</div>
      </div>
    );
  }

  if (!capabilities) {
    return (
      <Alert
        message="权限不足"
        description="您没有访问钉钉插件的权限。"
        type="error"
        showIcon
      />
    );
  }

  return (
    <div className="dingtalk-plugin">
      <Card title="钉钉智能机器人插件" className="plugin-header">
        <Tabs activeKey={activeTab} onChange={setActiveTab}>
          <TabPane tab="概览" key="overview" icon={<InfoCircleOutlined />}>
            {renderOverview()}
          </TabPane>
          
          <TabPane tab="基础设置" key="settings" icon={<SettingOutlined />}>
            <RoleBasedContent 
              roles={['project_admin', 'tenant_admin']}
              fallback={<Alert message="权限不足" description="您没有权限访问此功能。" type="warning" />}
            >
              {renderBasicSettings()}
            </RoleBasedContent>
          </TabPane>
          
          <TabPane 
            tab="知识库同步" 
            key="knowledge" 
            icon={<DatabaseOutlined />}
            disabled={!capabilities?.features.knowledge_sync}
          >
            <RoleBasedContent 
              roles={['project_admin', 'tenant_admin', 'operation_admin']}
              fallback={<Alert message="权限不足" description="您没有权限访问知识库同步功能。" type="warning" />}
            >
              {renderKnowledgeSync()}
            </RoleBasedContent>
          </TabPane>
          
          <TabPane 
            tab="AI集成" 
            key="ai" 
            icon={<RobotOutlined />}
            disabled={!capabilities?.features.ai_integration}
          >
            <RoleBasedContent 
              roles={['project_admin', 'tenant_admin']}
              fallback={<Alert message="权限不足" description="您没有权限访问AI集成功能。" type="warning" />}
            >
              {renderAIIntegration()}
            </RoleBasedContent>
          </TabPane>
          
          <TabPane 
            tab="用户管理" 
            key="user_management" 
            icon={<UserOutlined />}
            disabled={!capabilities?.features.user_management}
          >
            <RoleBasedContent 
              roles={['project_admin', 'tenant_admin']}
              fallback={<Alert message="权限不足" description="您没有权限访问用户管理功能。" type="warning" />}
            >
              {renderUserManagement()}
            </RoleBasedContent>
          </TabPane>
          
          <TabPane 
            tab="AI对话" 
            key="ai_chat" 
            icon={<RobotOutlined />}
            disabled={!capabilities?.features.ai_chat}
          >
            <RoleBasedContent 
              roles={['project_admin', 'tenant_admin']}
              fallback={<Alert message="权限不足" description="您没有权限访问AI对话功能。" type="warning" />}
            >
              {renderAIChat()}
            </RoleBasedContent>
          </TabPane>
          
          <TabPane 
            tab="消息发送" 
            key="message_sending" 
            icon={<RobotOutlined />}
            disabled={!capabilities?.features.message_sending}
          >
            <RoleBasedContent 
              roles={['project_admin', 'tenant_admin']}
              fallback={<Alert message="权限不足" description="您没有权限访问消息发送功能。" type="warning" />}
            >
              {renderMessageSending()}
            </RoleBasedContent>
          </TabPane>
        </Tabs>
      </Card>

      {/* 知识库同步模态框 */}
      <Modal
        title="知识库同步"
        visible={syncModalVisible}
        onCancel={() => setSyncModalVisible(false)}
        footer={null}
        width={600}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleKnowledgeSync}
          initialValues={{
            sync_type: 'full',
            target_knowledge_bases: ['project', 'ai'],
            include_documents: false
          }}
        >
          <Form.Item
            name="sync_type"
            label="同步类型"
            rules={[{ required: true, message: '请选择同步类型' }]}
          >
            <Select>
              <Option value="full">全量同步</Option>
              <Option value="incremental">增量同步</Option>
              <Option value="selective">选择性同步</Option>
            </Select>
          </Form.Item>
          
          <Form.Item
            name="target_knowledge_bases"
            label="目标知识库"
            rules={[{ required: true, message: '请选择目标知识库' }]}
          >
            <Select mode="multiple">
              <Option value="project">项目知识库</Option>
              <Option value="ai">AI知识库</Option>
            </Select>
          </Form.Item>
          
          <Form.Item
            name="include_documents"
            label="包含文档"
            valuePropName="checked"
          >
            <Switch />
          </Form.Item>
          
          <Form.Item>
            <Space>
              <Button type="primary" htmlType="submit" loading={syncLoading}>
                开始同步
              </Button>
              <Button onClick={() => setSyncModalVisible(false)}>
                取消
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>

      {/* Webhook模态框 */}
      <Modal
        title="Webhook管理"
        visible={webhookModalVisible}
        onCancel={() => setWebhookModalVisible(false)}
        footer={null}
        width={600}
      >
        <Form
          form={webhookForm}
          layout="vertical"
          onFinish={handleWebhookSave}
        >
          <Form.Item
            name="name"
            label="Webhook名称"
            rules={[{ required: true, message: '请输入Webhook名称' }]}
          >
            <Input />
          </Form.Item>
          
          <Form.Item
            name="webhook_url"
            label="Webhook URL"
            rules={[{ required: true, message: '请输入Webhook URL' }]}
          >
            <Input />
          </Form.Item>
          
          <Form.Item
            name="notification_types"
            label="通知类型"
            rules={[{ required: true, message: '请选择通知类型' }]}
          >
            <Select mode="multiple">
              <Option value="user_management">用户管理</Option>
              <Option value="ai_chat">AI对话</Option>
              <Option value="message_sending">消息发送</Option>
              <Option value="knowledge_sync">知识库同步</Option>
              <Option value="ai_integration">AI集成</Option>
            </Select>
          </Form.Item>
          
          <Form.Item>
            <Space>
              <Button type="primary" htmlType="submit">
                保存
              </Button>
              <Button onClick={() => setWebhookModalVisible(false)}>
                取消
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>

      {/* AI配置模态框 */}
      <Modal
        title="AI集成配置"
        visible={aiConfigModalVisible}
        onCancel={() => setAiConfigModalVisible(false)}
        footer={null}
        width={800}
      >
        <Form
          layout="vertical"
          onFinish={handleAiConfigSave}
          initialValues={aiIntegration || {
            integration_type: 'basic',
            auto_reply_enabled: false,
            ai_model_config: {
              model: 'gpt-3.5-turbo',
              temperature: 0.7,
              max_tokens: 1000
            },
            trigger_keywords: [],
            reply_templates: {}
          }}
        >
          <Form.Item
            name="integration_type"
            label="集成类型"
            rules={[{ required: true, message: '请选择集成类型' }]}
          >
            <Select>
              <Option value="basic">基础集成</Option>
              <Option value="advanced">高级集成</Option>
              <Option value="custom">自定义集成</Option>
            </Select>
          </Form.Item>
          
          <Form.Item
            name="auto_reply_enabled"
            label="启用自动回复"
            valuePropName="checked"
          >
            <Switch />
          </Form.Item>
          
          <Divider>AI模型配置</Divider>
          
          <Form.Item
            name={['ai_model_config', 'model']}
            label="AI模型"
            rules={[{ required: true, message: '请选择AI模型' }]}
          >
            <Select placeholder="请选择AI模型" allowClear onChange={handleAiModelChange}>
              {aiConfigs.filter(config => config.status === 'active').map(config => {
                const model = config.model;
                const provider = config.provider;
                return (
                  <Option key={config.id} value={config.id}>
                    <div style={{ display: 'flex', flexDirection: 'column' }}>
                      <div style={{ fontWeight: 'bold' }}>
                        {model?.display_name || model?.name || '未知模型'}
                      </div>
                      <div style={{ fontSize: '12px', color: '#666' }}>
                        配置: {config.name} | 提供商: {provider?.display_name || '未知'} | 
                        {model?.model_type && ` 类型: ${model.model_type}`}
                      </div>
                    </div>
                  </Option>
                );
              })}
              {aiConfigs.filter(config => config.status === 'active').length === 0 && (
                <Option disabled value="">
                  <div style={{ color: '#999', fontSize: '12px' }}>
                    暂无可用的AI配置，请先在AI模型与配置页面创建配置
                  </div>
                </Option>
              )}
            </Select>
          </Form.Item>
          
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name={['ai_model_config', 'temperature']}
                label="温度"
                rules={[{ required: true, message: '请输入温度值' }]}
              >
                <InputNumber min={0} max={2} step={0.1} style={{ width: '100%' }} />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name={['ai_model_config', 'max_tokens']}
                label="最大令牌数"
                rules={[{ required: true, message: '请输入最大令牌数' }]}
                extra={selectedAiConfig?.model ? 
                  `模型支持最大: ${selectedAiConfig.model.token_limit || selectedAiConfig.model.context_window || '未知'} tokens` : 
                  "请先选择AI模型"
                }
              >
                <InputNumber 
                  min={100} 
                  max={selectedAiConfig?.model?.token_limit || selectedAiConfig?.model?.context_window || 8192} 
                  style={{ width: '100%' }} 
                  placeholder={selectedAiConfig?.model?.token_limit ? `建议: ${Math.min(selectedAiConfig.model.token_limit, 4000)}` : "1000"}
                />
              </Form.Item>
            </Col>
          </Row>
          
          <Form.Item
            name="trigger_keywords"
            label="触发关键词"
          >
            <Select mode="tags" placeholder="输入关键词后按回车添加">
            </Select>
          </Form.Item>
          
          <Form.Item>
            <Space>
              <Button type="primary" htmlType="submit">
                保存配置
              </Button>
              <Button onClick={() => setAiConfigModalVisible(false)}>
                取消
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>

      {/* 模板管理模态框 */}
      <Modal
        title="回复模板管理"
        visible={templateModalVisible}
        onCancel={() => setTemplateModalVisible(false)}
        footer={null}
        width={600}
      >
        <Form
          layout="vertical"
          onFinish={handleTemplateSave}
        >
          <Form.Item
            name="template_name"
            label="模板名称"
            rules={[{ required: true, message: '请输入模板名称' }]}
          >
            <Input placeholder="例如：欢迎消息" />
          </Form.Item>
          
          <Form.Item
            name="template_content"
            label="模板内容"
            rules={[{ required: true, message: '请输入模板内容' }]}
          >
            <TextArea 
              rows={4} 
              placeholder="例如：欢迎使用我们的服务！如有任何问题，请随时联系我们。"
            />
          </Form.Item>
          
          <Form.Item>
            <Space>
              <Button type="primary" htmlType="submit">
                保存模板
              </Button>
              <Button onClick={() => setTemplateModalVisible(false)}>
                取消
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default DingTalkPlugin; 