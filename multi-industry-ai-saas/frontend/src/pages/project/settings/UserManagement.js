import React, { useState, useEffect } from 'react';
import {
  Card, Table, Button, Space, Tag, Modal, Form,
  Input, Select, Switch, message, Avatar, Tooltip,
  Popconfirm, Tabs, Row, Col, Divider
} from 'antd';
import {
  PlusOutlined, EditOutlined, DeleteOutlined,
  UserOutlined, LockOutlined, MailOutlined,
  PhoneOutlined, TeamOutlined, ShopOutlined
} from '@ant-design/icons';
import apiService from '../../../services/api';
import './UserManagement.css';

const { Option } = Select;

/**
 * 用户管理页面
 * 用于管理项目内的用户，包括创建、编辑、删除用户，以及分配角色
 */
const UserManagement = () => {
  const [users, setUsers] = useState([]);
  const [roles, setRoles] = useState([]);
  const [stores, setStores] = useState([]);
  const [loading, setLoading] = useState(true);
  const [modalVisible, setModalVisible] = useState(false);
  const [editingUser, setEditingUser] = useState(null);
  const [activeTab, setActiveTab] = useState('all');
  const [selectedRole, setSelectedRole] = useState('');
  const [form] = Form.useForm();

  // 获取用户和角色列表
  useEffect(() => {
    fetchUsers();
    fetchRoles();
    fetchStores();
  }, []);

  // 获取用户数据
  const fetchUsers = async () => {
    try {
      setLoading(true);
      // 调用后端API获取用户数据
      const res = await apiService.project.user.getList();
      console.log('API返回的用户数据:', JSON.stringify(res, null, 2));

      // 根据API返回的数据结构进行处理
      // API返回格式可能是各种嵌套结构
      let userData = [];

      // 检查是否是数组，且数组中的元素具有success和data字段
      if (Array.isArray(res) && res.length > 0 && res[0].success && res[0].data) {
        // 格式1：[{ success: true, data: {...} }, ...]
        userData = res.map(item => item.data);
      } else if (res && res.success === true) {
        if (Array.isArray(res.data)) {
          // 格式2：{ success: true, data: [...] }
          userData = res.data;
        } else if (res.data && typeof res.data === 'object') {
          // 格式3：{ success: true, data: {...} } - 单个用户对象
          userData = [res.data];
        }
      } else if (res && Array.isArray(res.data)) {
        // 格式4：{ data: [...] }
        userData = res.data;
      } else if (res && res.data && typeof res.data === 'object') {
        // 格式5：{ data: {...} } - 单个用户对象
        userData = [res.data];
      } else if (res && Array.isArray(res.items)) {
        // 格式6：{ items: [...] }
        userData = res.items;
      } else if (Array.isArray(res)) {
        // 格式7：直接是数组
        userData = res;
      } else if (res && typeof res === 'object' && !res.success) {
        // 格式8：直接是单个对象（非API响应格式）
        userData = [res];
      }

      // 确保userData中的每个元素都是用户对象，而不是嵌套的API响应
      userData = userData.map(item => {
        if (item && item.success && item.data) {
          return item.data;
        }
        return item;
      });

      console.log('处理后的用户数据:', JSON.stringify(userData, null, 2));

      // 检查数据结构是否与表格列匹配
      if (userData.length > 0) {
        console.log('第一个用户数据示例:', JSON.stringify(userData[0], null, 2));
        console.log('数据字段:', Object.keys(userData[0]));
      } else {
        console.warn('处理后的用户数据为空数组');
      }

      // 检查是否有嵌套的success和data结构
      const hasNestedStructure = Array.isArray(userData) && userData.some(item => item && item.success && item.data);
      if (hasNestedStructure) {
        console.warn('警告：用户数据中仍然存在嵌套的success和data结构');
      }

      // 转换数据结构以匹配表格列
      const formattedUsers = Array.isArray(userData) ? userData.map((user, index) => {
        const userObj = user && user.data ? user.data : user;

        return {
          id: userObj.id || `temp-user-${index}`,
          username: userObj.username || userObj.user_name || '',
          name: userObj.name || userObj.display_name || '',
          email: userObj.email || '',
          phone: userObj.phone || userObj.mobile || '',
          role: userObj.role || userObj.role_code || '',
          role_name: userObj.role_name || userObj.role_display ||
            (userObj.role === 'project_admin' ? '项目管理员' :
            userObj.role === 'store_admin' ? '门店管理员' :
            userObj.role === 'operation_staff' ? '运营人员' :
            userObj.role === 'purchase_staff' ? '采购人员' :
            userObj.role === 'finance_staff' ? '财务人员' :
            userObj.role === 'inventory_staff' ? '仓管人员' :
            (userObj.role ? userObj.role : '')),
          status: userObj.status || 'active',
          last_login: userObj.last_login || null,
          store_names: Array.isArray(userObj.store_names) ? userObj.store_names :
            (userObj.store_ids && Array.isArray(userObj.store_ids) && userObj.store_ids.length > 0 ?
              userObj.store_ids.map(id => {
                const store = stores.find(s => s.id === id);
                return store ? store.name : `门店${id}`;
              }) : [])
        };
      }) : [];

      console.log('格式化后的用户数据:', JSON.stringify(formattedUsers, null, 2));
      setUsers(formattedUsers);
      setLoading(false);
    } catch (error) {
      console.error('获取用户列表失败:', error);
      message.error('获取用户列表失败');
      setLoading(false);
    }
  };

  // 获取角色数据
  const fetchRoles = async () => {
    try {
      const response = await apiService.project.role.getList();
      // 检查响应格式，兼容不同的返回格式
      if (response && typeof response === 'object') {
        if (response.success !== false) {
          const roleList = response.data || response.items || response || [];
          setRoles(Array.isArray(roleList) ? roleList : []);
        } else {
          console.error('获取角色列表失败:', response.message);
          message.error(response.message || '获取角色列表失败');
          setRoles([]);
        }
      } else {
        setRoles([]);
      }
    } catch (error) {
      console.error('获取角色列表失败:', error);
      message.error('获取角色列表失败');
      setRoles([]);
    }
  };

  // 获取门店列表
  const fetchStores = async () => {
    try {
      const res = await apiService.project.store.getList();
      setStores(Array.isArray(res.data) ? res.data : (res.items || []));
    } catch (error) {
      message.error('获取门店列表失败');
    }
  };

  // 处理角色变化
  const handleRoleChange = (value) => {
    console.log('角色已更改:', value);
    // 更新选中的角色状态，触发组件重新渲染
    setSelectedRole(value);
    // 如果不是门店角色，清除门店选择
    if (!isStoreRole(value)) {
      form.setFieldsValue({ store_ids: undefined });
    }
  };

  // 判断是否为门店相关角色
  const isStoreRole = (role) => {
    if (!role) return false;

    // 支持常见门店角色关键词
    const keywords = [
      'store', 'shop', 'retail',
      '门店', '店长', '店员', '营业', '销售'
    ];

    // 转换为小写进行比较，提高匹配准确性
    const roleLower = role.toLowerCase();
    return keywords.some(k => roleLower.includes(k.toLowerCase()));
  };

  // 打开创建用户模态框
  const showCreateModal = () => {
    setEditingUser(null);
    form.resetFields();
    form.setFieldsValue({
      status: 'active',
      is_project_admin: false
    });
    setModalVisible(true);
  };

  // 打开编辑用户模态框
  const showEditModal = (user) => {
    setEditingUser(user);
    form.setFieldsValue({
      username: user.username,
      name: user.name,
      email: user.email,
      phone: user.phone,
      role: user.role,
      status: user.status,
      is_project_admin: user.role === 'project_admin'
    });
    setModalVisible(true);
  };

  // 关闭模态框
  const handleCancel = () => {
    setModalVisible(false);
  };

  // 判断是否门店相关角色已在上方定义

  // 提交表单
  const handleSubmit = async () => {
    try {
      const values = await form.validateFields();
      if (values.is_project_admin) {
        values.role = 'project_admin';
      }
      // 只在门店相关角色时传 store_ids，否则删除
      if (!isStoreRole(values.role)) {
        delete values.store_ids;
      }
      if (editingUser) {
        // 更新用户
        await apiService.project.user.update(editingUser.id, values);
        message.success('用户更新成功');
      } else {
        // 创建用户
        await apiService.project.user.create(values);
        message.success('用户创建成功');
      }
      setModalVisible(false);
      fetchUsers();
    } catch (error) {
      console.error('提交表单失败:', error);
      message.error('操作失败');
    }
  };

  // 删除用户
  const handleDelete = async (user) => {
    try {
      await apiService.project.user.delete(user.id);
      message.success('用户删除成功');
      fetchUsers();
    } catch (error) {
      console.error('删除用户失败:', error);
      message.error('删除用户失败');
    }
  };

  // 根据角色过滤用户
  const filterUsersByRole = (role) => {
    if (role === 'all') return users;

    // 根据角色类别过滤用户
    return users.filter(user => {
      const userRole = user.role || '';

      switch(role) {
        case 'admin':
          return userRole.includes('admin') || userRole.includes('管理员');
        case 'store':
          return userRole.includes('store') || userRole.includes('门店') || userRole.includes('店长') || userRole.includes('店员');
        case 'operation':
          return userRole.includes('operation') || userRole.includes('运营');
        case 'purchase':
          return userRole.includes('purchase') || userRole.includes('采购');
        case 'finance':
          return userRole.includes('finance') || userRole.includes('财务');
        case 'inventory':
          return userRole.includes('inventory') || userRole.includes('仓管') || userRole.includes('库存');
        case 'hr':
          return userRole.includes('hr') || userRole.includes('人事') || userRole.includes('人力');
        default:
          return false;
      }
    });
  };

  // 表格列配置
  const columns = [
    {
      title: '用户',
      dataIndex: 'name',
      key: 'name',
      render: (text, record) => (
        <div className="user-info-cell">
          <Avatar icon={<UserOutlined />} />
          <div className="user-info-details">
            <div className="user-name">{text}</div>
            <div className="user-username">@{record.username}</div>
          </div>
        </div>
      )
    },
    {
      title: '联系方式',
      key: 'contact',
      render: (_, record) => (
        <div>
          <div><MailOutlined /> {record.email}</div>
          <div><PhoneOutlined /> {record.phone}</div>
        </div>
      )
    },
    {
      title: '角色',
      dataIndex: 'role_name',
      key: 'role_name',
      render: (text, record) => (
        <Tag color={
          record.role === 'project_admin' ? 'gold' :
          record.role === 'store_admin' ? 'green' :
          record.role === 'operation_staff' ? 'blue' :
          record.role === 'purchase_staff' ? 'purple' :
          record.role === 'finance_staff' ? 'orange' :
          record.role === 'inventory_staff' ? 'cyan' :
          'default'
        }>
          {text}
        </Tag>
      )
    },
    {
      title: '所属门店',
      dataIndex: 'store_names',
      key: 'store_names',
      render: (names) => (Array.isArray(names) && names.length > 0 ? names.join(', ') : '-')
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status) => (
        <Tag color={status === 'active' ? 'green' : 'red'}>
          {status === 'active' ? '启用' : '禁用'}
        </Tag>
      )
    },
    {
      title: '最后登录',
      dataIndex: 'last_login',
      key: 'last_login',
      render: (text) => text ? new Date(text).toLocaleString() : '-'
    },
    {
      title: '操作',
      key: 'action',
      render: (_, record) => (
        <Space size="middle">
          <Button
            type="text"
            icon={<EditOutlined />}
            onClick={() => showEditModal(record)}
          />
          <Popconfirm
            title="确定要删除这个用户吗？"
            onConfirm={() => handleDelete(record)}
            okText="确定"
            cancelText="取消"
          >
            <Button
              type="text"
              danger
              icon={<DeleteOutlined />}
            />
          </Popconfirm>
        </Space>
      )
    }
  ];

  // 角色选项卡
  const roleTabItems = [
    { key: 'all', label: '全部用户' },
    { key: 'admin', label: '管理员' },
    { key: 'store', label: '门店' },
    { key: 'operation', label: '运营管理' },
    { key: 'purchase', label: '采购管理' },
    { key: 'finance', label: '财务管理' },
    { key: 'inventory', label: '仓管管理' },
    { key: 'hr', label: '人事管理' }
  ];

  console.log('UserManagement 组件渲染', { users, loading, activeTab });

  return (
    <div className="user-management">
      <Card
        title="用户管理"
        extra={
          <Button
            type="primary"
            icon={<PlusOutlined />}
            onClick={showCreateModal}
          >
            创建用户
          </Button>
        }
      >
        <Tabs
          activeKey={activeTab}
          onChange={setActiveTab}
          items={roleTabItems}
        />

        <Table
          columns={columns}
          dataSource={filterUsersByRole(activeTab)}
          rowKey={(record) => record.id || `user-${Math.random().toString(36).substring(2, 11)}`}
          loading={loading}
          pagination={{ pageSize: 10 }}
        />
      </Card>

      <Modal
        title={editingUser ? '编辑用户' : '创建用户'}
        open={modalVisible}
        onOk={handleSubmit}
        onCancel={handleCancel}
        width={600}
      >
        <Form
          form={form}
          layout="vertical"
        >
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="username"
                label="用户名"
                rules={[{ required: true, message: '请输入用户名' }]}
              >
                <Input
                  prefix={<UserOutlined />}
                  placeholder="请输入用户名"
                />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="name"
                label="姓名"
                rules={[{ required: true, message: '请输入姓名' }]}
              >
                <Input placeholder="请输入姓名" />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="email"
                label="邮箱"
                rules={[
                  { required: true, message: '请输入邮箱' },
                  { type: 'email', message: '请输入有效的邮箱地址' }
                ]}
              >
                <Input
                  prefix={<MailOutlined />}
                  placeholder="请输入邮箱"
                />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="phone"
                label="手机号"
                rules={[{ required: true, message: '请输入手机号' }]}
              >
                <Input
                  prefix={<PhoneOutlined />}
                  placeholder="请输入手机号"
                />
              </Form.Item>
            </Col>
          </Row>

          {!editingUser && (
            <Row gutter={16}>
              <Col span={12}>
                <Form.Item
                  name="password"
                  label="密码"
                  rules={[{ required: true, message: '请输入密码' }]}
                >
                  <Input.Password
                    prefix={<LockOutlined />}
                    placeholder="请输入密码"
                  />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item
                  name="confirm_password"
                  label="确认密码"
                  dependencies={['password']}
                  rules={[
                    { required: true, message: '请确认密码' },
                    ({ getFieldValue }) => ({
                      validator(_, value) {
                        if (!value || getFieldValue('password') === value) {
                          return Promise.resolve();
                        }
                        return Promise.reject(new Error('两次输入的密码不一致'));
                      },
                    }),
                  ]}
                >
                  <Input.Password
                    prefix={<LockOutlined />}
                    placeholder="请确认密码"
                  />
                </Form.Item>
              </Col>
            </Row>
          )}

          <Divider style={{ margin: '12px 0' }} />

          {/* 角色和状态 */}
          <Row gutter={24} align="middle" style={{ marginTop: 8 }}>
            <Col span={12}>
              <Form.Item
                name="role"
                label={<span style={{ fontWeight: 500 }}><TeamOutlined /> 角色</span>}
                rules={[{ required: true, message: '请选择角色' }]}
                style={{ marginBottom: 24 }}
              >
                <Select
                  placeholder="请选择角色"
                  onChange={handleRoleChange}
                  allowClear
                  showSearch
                  optionFilterProp="children"
                  style={{ width: '100%' }}
                  dropdownMatchSelectWidth={false}
                  dropdownStyle={{ minWidth: '180px' }}
                >
                  {roles.map(role => (
                    <Option key={role.code} value={role.code}>
                      {role.name}
                    </Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="status"
                label={<span style={{ fontWeight: 500 }}><span style={{ marginRight: 4 }}>•</span> 状态</span>}
                initialValue="active"
                style={{ marginBottom: 24 }}
              >
                <Select style={{ width: '100%' }}>
                  <Option value="active">启用</Option>
                  <Option value="inactive">禁用</Option>
                </Select>
              </Form.Item>
            </Col>
          </Row>

          {/* 所属门店和项目管理员 */}
          <Row gutter={24} align="middle">
            <Col span={12}>
              <Form.Item
                name="store_ids"
                label={<span style={{ fontWeight: 500 }}><ShopOutlined /> 所属门店</span>}
                rules={[{ required: isStoreRole(selectedRole || form.getFieldValue('role')), message: '请选择门店' }]}
                style={{ marginBottom: 24 }}
                tooltip={isStoreRole(selectedRole || form.getFieldValue('role')) ? "可选择多个门店" : "仅门店相关角色可选择"}
              >
                <Select
                  mode="multiple"
                  placeholder={isStoreRole(selectedRole || form.getFieldValue('role')) ? "请选择门店" : "仅门店相关角色可选择"}
                  disabled={!isStoreRole(selectedRole || form.getFieldValue('role'))}
                  showSearch
                  optionFilterProp="children"
                  maxTagCount={3}
                  maxTagTextLength={5}
                  style={{ width: '100%' }}
                  listHeight={200}
                  dropdownStyle={{ minWidth: '200px' }}
                >
                  {stores.map(store => (
                    <Option key={store.id} value={store.id}>{store.name}</Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="is_project_admin"
                label={
                  <span style={{ fontWeight: 500, display: 'inline-flex', alignItems: 'center' }}>
                    <span style={{ marginRight: 4 }}>•</span> 设为项目管理员
                    <Tooltip title="项目管理员拥有项目内的所有权限">
                      <span style={{ marginLeft: 4, color: '#1890ff', cursor: 'help' }}>ⓘ</span>
                    </Tooltip>
                  </span>
                }
                valuePropName="checked"
                style={{ marginBottom: 24 }}
              >
                <Switch />
              </Form.Item>
            </Col>
          </Row>
        </Form>
      </Modal>
    </div>
  );
};

export default UserManagement;
