import React, { useState, useEffect, useMemo } from 'react';
import {
  Card, Tabs, Form, Switch, Select, Button,
  Radio, Slider, message, Row, Col, Input,
  Divider, Space, Alert, InputNumber
} from 'antd';
import {
  SaveOutlined, ReloadOutlined, BgColorsOutlined,
  SettingOutlined, BellOutlined,
  SecurityScanOutlined, GlobalOutlined, LoginOutlined, RobotOutlined
} from '@ant-design/icons';
import ThirdPartyLogin from './components/ThirdPartyLogin';
import { project } from '../../../services';
import { applyTheme } from '../../../utils/theme';
import '../../../styles/theme.css';
import './SystemSettings.css';

const { Option } = Select;

/**
 * 系统设置页面
 * 用于管理项目的系统设置，包括主题、通知、安全等
 */
const SystemSettings = () => {
  const [activeTab, setActiveTab] = useState('theme');
  const [loading, setLoading] = useState(false);
  const [themeForm] = Form.useForm();
  const [notificationForm] = Form.useForm();
  const [securityForm] = Form.useForm();
  const [generalForm] = Form.useForm();
  const [aiForm] = Form.useForm();

  // 用于跟踪每个标签页是否已加载数据
  const [loadedTabs, setLoadedTabs] = useState({});

  // 初始化表单数据和标签页切换时获取设置
  useEffect(() => {
    if (!loadedTabs[activeTab]) {
      fetchTabSettings(activeTab);
    }
    // eslint-disable-next-line
  }, [activeTab]);

  // 根据当前标签页获取对应的设置
  const fetchTabSettings = async (tabKey) => {
    if (loading) return; // 如果正在加载，不重复请求

    try {
      setLoading(true);

      // 根据当前标签页获取对应的设置
      switch (tabKey) {
        case 'theme':
          const themeSettings = await project.settings.getTheme();
          themeForm.setFieldsValue(themeSettings);
          break;
        case 'notification':
          const notificationSettings = await project.settings.getNotification();
          notificationForm.setFieldsValue(notificationSettings);
          break;
        case 'security':
          const securitySettings = await project.settings.getSecurity();
          securityForm.setFieldsValue(securitySettings);
          break;
        case 'general':
          const generalSettings = await project.settings.getGeneral();
          generalForm.setFieldsValue(generalSettings);
          break;
        case 'ai':
          const aiSettings = await project.settings.getAI();
          aiForm.setFieldsValue(aiSettings);
          break;
        // 第三方登录设置由ThirdPartyLogin组件自己处理
        default:
          break;
      }

      // 标记该标签页已加载数据
      setLoadedTabs(prev => ({
        ...prev,
        [tabKey]: true
      }));

      setLoading(false);
    } catch (error) {
      console.error('获取设置失败:', error);
      message.error('获取设置失败');
      setLoading(false);
    }
  };

  // 保存主题设置
  const saveThemeSettings = async () => {
    try {
      const values = await themeForm.validateFields();
      setLoading(true);

      // 调用API保存设置
      await project.settings.updateTheme(values);

      // 应用主题设置
      applyTheme(values);

      message.success('主题设置保存成功');
      setLoading(false);
    } catch (error) {
      console.error('保存主题设置失败:', error);
      message.error('保存主题设置失败');
      setLoading(false);
    }
  };

  // 保存通知设置
  const saveNotificationSettings = async () => {
    try {
      const values = await notificationForm.validateFields();
      setLoading(true);

      // 调用API保存设置
      await project.settings.updateNotification(values);
      message.success('通知设置保存成功');
      setLoading(false);
    } catch (error) {
      console.error('保存通知设置失败:', error);
      message.error('保存通知设置失败');
      setLoading(false);
    }
  };

  // 保存安全设置
  const saveSecuritySettings = async () => {
    try {
      const values = await securityForm.validateFields();
      setLoading(true);

      // 调用API保存设置
      await project.settings.updateSecurity(values);
      message.success('安全设置保存成功');
      setLoading(false);
    } catch (error) {
      console.error('保存安全设置失败:', error);
      message.error('保存安全设置失败');
      setLoading(false);
    }
  };

  // 保存常规设置
  const saveGeneralSettings = async () => {
    try {
      const values = await generalForm.validateFields();
      setLoading(true);

      // 调用API保存设置
      await project.settings.updateGeneral(values);
      message.success('常规设置保存成功');
      setLoading(false);
    } catch (error) {
      console.error('保存常规设置失败:', error);
      message.error('保存常规设置失败');
      setLoading(false);
    }
  };

  // 保存AI设置
  const saveAISettings = async () => {
    try {
      const values = await aiForm.validateFields();
      setLoading(true);

      // 调用API保存设置
      await project.settings.updateAI(values);
      message.success('AI设置保存成功');
      setLoading(false);
    } catch (error) {
      console.error('保存AI设置失败:', error);
      message.error('保存AI设置失败');
      setLoading(false);
    }
  };

  // 重置设置
  const resetSettings = () => {
    switch (activeTab) {
      case 'theme':
        themeForm.resetFields();
        message.info('主题设置已重置');
        break;
      case 'notification':
        notificationForm.resetFields();
        message.info('通知设置已重置');
        break;
      case 'security':
        securityForm.resetFields();
        message.info('安全设置已重置');
        break;
      case 'general':
        generalForm.resetFields();
        message.info('常规设置已重置');
        break;
      case 'ai':
        aiForm.resetFields();
        message.info('AI设置已重置');
        break;
      default:
        break;
    }
  };

  // 主题设置表单
  const renderThemeSettings = () => {
    return (
      <Form
        form={themeForm}
        layout="vertical"
        className="settings-form"
        initialValues={{
          mode: 'light',
          primaryColor: '#1890ff',
          borderRadius: 4,
          compactMode: false,
          customFont: false,
          fontFamily: 'default'
        }}
        style={{ backgroundColor: 'var(--component-bg)', padding: '20px', borderRadius: 'var(--border-radius)' }}
      >
        <Alert
          message="主题设置将应用于整个项目"
          description="更改主题设置后，所有用户都将看到相同的界面风格。"
          type="info"
          showIcon
          style={{ marginBottom: 24 }}
        />

        <Row gutter={24}>
          <Col span={12}>
            <Form.Item
              name="mode"
              label="主题模式"
            >
              <Radio.Group>
                <Radio.Button value="light">浅色</Radio.Button>
                <Radio.Button value="dark">深色</Radio.Button>
                <Radio.Button value="auto">跟随系统</Radio.Button>
              </Radio.Group>
            </Form.Item>
          </Col>

          <Col span={12}>
            <Form.Item
              name="primaryColor"
              label="主题色"
              getValueFromEvent={(e) => {
                // 确保颜色值是有效的十六进制格式
                const color = e.target.value;
                return color.match(/^#[0-9A-F]{6}$/i) ? color : '#1890ff';
              }}
            >
              <Input type="color" defaultValue="#1890ff" />
            </Form.Item>
          </Col>
        </Row>

        <Row gutter={24}>
          <Col span={12}>
            <Form.Item
              name="borderRadius"
              label="圆角大小"
            >
              <Slider
                min={0}
                max={16}
                marks={{
                  0: '0',
                  4: '4',
                  8: '8',
                  16: '16'
                }}
              />
            </Form.Item>
          </Col>

          <Col span={12}>
            <Form.Item
              name="compactMode"
              label="紧凑模式"
              valuePropName="checked"
            >
              <Switch />
            </Form.Item>

            <Form.Item
              name="customFont"
              label="自定义字体"
              valuePropName="checked"
            >
              <Switch onChange={(checked) => {
                // 当开关状态改变时，更新字体选择器的禁用状态
                if (checked) {
                  // 如果启用自定义字体，应用当前选择的字体
                  const fontFamily = themeForm.getFieldValue('fontFamily');
                  const fontMap = {
                    'default': '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif',
                    'arial': 'Arial, sans-serif',
                    'roboto': 'Roboto, sans-serif',
                    'pingfang': 'PingFang SC, sans-serif',
                    'microsoft': 'Microsoft YaHei, sans-serif'
                  };
                  document.documentElement.style.setProperty('--font-family', fontMap[fontFamily] || fontMap.default);
                } else {
                  // 如果禁用自定义字体，恢复默认字体
                  document.documentElement.style.setProperty('--font-family', '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif');
                }
              }} />
            </Form.Item>
          </Col>
        </Row>

        <Form.Item
          name="fontFamily"
          label="字体"
          dependencies={['customFont']}
        >
          <Select
            disabled={!themeForm.getFieldValue('customFont')}
            onChange={(value) => {
              if (themeForm.getFieldValue('customFont')) {
                // 立即应用字体预览
                const fontMap = {
                  'default': '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif',
                  'arial': 'Arial, sans-serif',
                  'roboto': 'Roboto, sans-serif',
                  'pingfang': 'PingFang SC, sans-serif',
                  'microsoft': 'Microsoft YaHei, sans-serif'
                };
                document.documentElement.style.setProperty('--font-family', fontMap[value] || fontMap.default);
              }
            }}
          >
            <Option value="default">系统默认</Option>
            <Option value="arial">Arial</Option>
            <Option value="roboto">Roboto</Option>
            <Option value="pingfang">PingFang SC</Option>
            <Option value="microsoft">微软雅黑</Option>
          </Select>
        </Form.Item>

        <Divider />

        <Form.Item>
          <Space>
            <Button
              type="primary"
              icon={<SaveOutlined />}
              onClick={saveThemeSettings}
              loading={loading}
            >
              保存设置
            </Button>
            <Button
              icon={<ReloadOutlined />}
              onClick={resetSettings}
            >
              重置
            </Button>
          </Space>
        </Form.Item>
      </Form>
    );
  };

  // 通知设置表单
  const renderNotificationSettings = () => {
    return (
      <Form
        form={notificationForm}
        layout="vertical"
        className="settings-form"
        initialValues={{
          emailNotification: true,
          smsNotification: false,
          pushNotification: true,
          normalFrequency: 'realtime',
          highPriorityFrequency: 'realtime',
          dailyDigest: true,
          soundEnabled: true
        }}
      >
        <Alert
          message="通知设置已增强"
          description="您可以在个人中心的'通知设置'页面中进行更详细的通知配置，包括工作流通知设置。"
          type="info"
          showIcon
          style={{ marginBottom: 24 }}
        />

        <Divider orientation="left">通知方式</Divider>
        <Row gutter={24}>
          <Col span={8}>
            <Form.Item
              name="emailNotification"
              label="邮件通知"
              valuePropName="checked"
            >
              <Switch />
            </Form.Item>
          </Col>

          <Col span={8}>
            <Form.Item
              name="smsNotification"
              label="短信通知"
              valuePropName="checked"
            >
              <Switch />
            </Form.Item>
          </Col>

          <Col span={8}>
            <Form.Item
              name="pushNotification"
              label="推送通知"
              valuePropName="checked"
            >
              <Switch />
            </Form.Item>
          </Col>
        </Row>

        <Divider orientation="left">通知频率</Divider>
        <Row gutter={24}>
          <Col span={12}>
            <Form.Item
              name="normalFrequency"
              label="普通通知频率"
            >
              <Radio.Group>
                <Radio value="realtime">实时</Radio>
                <Radio value="hourly">每小时</Radio>
                <Radio value="daily">每天</Radio>
              </Radio.Group>
            </Form.Item>
          </Col>

          <Col span={12}>
            <Form.Item
              name="highPriorityFrequency"
              label="高优先级通知频率"
            >
              <Radio.Group>
                <Radio value="realtime">实时</Radio>
                <Radio value="hourly">每小时</Radio>
                <Radio value="daily">每天</Radio>
              </Radio.Group>
            </Form.Item>
          </Col>
        </Row>

        <Divider orientation="left">其他设置</Divider>
        <Row gutter={24}>
          <Col span={12}>
            <Form.Item
              name="dailyDigest"
              label="每日摘要"
              valuePropName="checked"
            >
              <Switch />
            </Form.Item>
          </Col>

          <Col span={12}>
            <Form.Item
              name="soundEnabled"
              label="通知声音"
              valuePropName="checked"
            >
              <Switch />
            </Form.Item>
          </Col>
        </Row>

        <Divider />

        <Form.Item>
          <Space>
            <Button
              type="primary"
              icon={<SaveOutlined />}
              onClick={saveNotificationSettings}
              loading={loading}
            >
              保存设置
            </Button>
            <Button
              icon={<ReloadOutlined />}
              onClick={resetSettings}
            >
              重置
            </Button>
          </Space>
        </Form.Item>
      </Form>
    );
  };

  // 安全设置表单
  const renderSecuritySettings = () => {
    return (
      <Form
        form={securityForm}
        layout="vertical"
        className="settings-form"
        initialValues={{
          passwordExpiration: 90,
          twoFactorAuth: false,
          loginAttempts: 5,
          sessionTimeout: 30,
          ipRestriction: false
        }}
      >
        <Row gutter={24}>
          <Col span={12}>
            <Form.Item
              name="passwordExpiration"
              label="密码过期天数"
            >
              <Select>
                <Option value={30}>30天</Option>
                <Option value={60}>60天</Option>
                <Option value={90}>90天</Option>
                <Option value={180}>180天</Option>
                <Option value={365}>365天</Option>
                <Option value={0}>永不过期</Option>
              </Select>
            </Form.Item>

            <Form.Item
              name="twoFactorAuth"
              label="两步验证"
              valuePropName="checked"
            >
              <Switch />
            </Form.Item>
          </Col>

          <Col span={12}>
            <Form.Item
              name="loginAttempts"
              label="最大登录尝试次数"
            >
              <Select>
                <Option value={3}>3次</Option>
                <Option value={5}>5次</Option>
                <Option value={10}>10次</Option>
              </Select>
            </Form.Item>

            <Form.Item
              name="sessionTimeout"
              label="会话超时时间(分钟)"
            >
              <Select>
                <Option value={15}>15分钟</Option>
                <Option value={30}>30分钟</Option>
                <Option value={60}>60分钟</Option>
                <Option value={120}>120分钟</Option>
                <Option value={0}>永不超时</Option>
              </Select>
            </Form.Item>

            <Form.Item
              name="ipRestriction"
              label="IP限制"
              valuePropName="checked"
            >
              <Switch />
            </Form.Item>

            <Form.Item
              noStyle
              shouldUpdate={(prev, curr) => prev.ipRestriction !== curr.ipRestriction}
            >
              {({ getFieldValue }) =>
                getFieldValue('ipRestriction') ? (
                  <Form.Item
                    name="ipWhitelist"
                    label="IP白名单（仅允许下列IP访问，支持多个，回车或逗号分隔）"
                    rules={[
                      {
                        validator: (_, value) => {
                          if (!value || value.length === 0) {
                            return Promise.reject('请至少填写一个IP地址');
                          }
                          // 校验每个IP格式
                          const ips = value.filter(ip => ip.trim() !== '');
                          const ipRegex = /^(\d{1,3}\.){3}\d{1,3}$/;
                          for (let ip of ips) {
                            if (!ipRegex.test(ip.trim())) {
                              return Promise.reject(`IP地址格式不正确: ${ip}`);
                            }
                          }
                          return Promise.resolve();
                        },
                      },
                    ]}
                  >
                    <Select
                      mode="tags"
                      style={{ width: '100%' }}
                      tokenSeparators={[',', ' ']}
                      placeholder="如 ***********，支持多个"
                    />
                  </Form.Item>
                ) : null
              }
            </Form.Item>
          </Col>
        </Row>

        <Divider />

        <Form.Item>
          <Space>
            <Button
              type="primary"
              icon={<SaveOutlined />}
              onClick={saveSecuritySettings}
              loading={loading}
            >
              保存设置
            </Button>
            <Button
              icon={<ReloadOutlined />}
              onClick={resetSettings}
            >
              重置
            </Button>
          </Space>
        </Form.Item>
      </Form>
    );
  };

  // 常规设置表单
  const renderGeneralSettings = () => {
    return (
      <Form
        form={generalForm}
        layout="vertical"
        className="settings-form"
        initialValues={{
          language: 'zh_CN',
          timezone: 'Asia/Shanghai',
          dateFormat: 'YYYY-MM-DD',
          timeFormat: '24hour'
        }}
      >
        <Row gutter={24}>
          <Col span={12}>
            <Form.Item
              name="language"
              label="语言"
            >
              <Select>
                <Option value="zh_CN">简体中文</Option>
                <Option value="en_US">English</Option>
              </Select>
            </Form.Item>

            <Form.Item
              name="timezone"
              label="时区"
            >
              <Select>
                <Option value="Asia/Shanghai">中国标准时间 (GMT+8)</Option>
                <Option value="America/New_York">美国东部时间 (GMT-5)</Option>
                <Option value="Europe/London">英国标准时间 (GMT+0)</Option>
                <Option value="Asia/Tokyo">日本标准时间 (GMT+9)</Option>
              </Select>
            </Form.Item>
          </Col>

          <Col span={12}>
            <Form.Item
              name="dateFormat"
              label="日期格式"
            >
              <Radio.Group>
                <Radio value="YYYY-MM-DD">YYYY-MM-DD</Radio>
                <Radio value="MM/DD/YYYY">MM/DD/YYYY</Radio>
                <Radio value="DD/MM/YYYY">DD/MM/YYYY</Radio>
              </Radio.Group>
            </Form.Item>

            <Form.Item
              name="timeFormat"
              label="时间格式"
            >
              <Radio.Group>
                <Radio value="24hour">24小时制</Radio>
                <Radio value="12hour">12小时制</Radio>
              </Radio.Group>
            </Form.Item>
          </Col>
        </Row>

        <Divider />

        <Form.Item>
          <Space>
            <Button
              type="primary"
              icon={<SaveOutlined />}
              onClick={saveGeneralSettings}
              loading={loading}
            >
              保存设置
            </Button>
            <Button
              icon={<ReloadOutlined />}
              onClick={resetSettings}
            >
              重置
            </Button>
          </Space>
        </Form.Item>
      </Form>
    );
  };

  // AI设置表单
  const renderAISettings = () => {
    return (
      <Form
        form={aiForm}
        layout="vertical"
        className="settings-form"
        initialValues={{
          enabled: true,
          cache_duration: '1hour',
          insights_enabled: true,
          suggestions_enabled: true,
          auto_analysis: false,
          analysis_frequency: 'daily',
          max_requests_per_hour: 100,
          enable_data_learning: false,
          privacy_mode: true
        }}
      >
        <Alert
          message="AI功能配置"
          description="配置AI助手的功能开关和行为参数，这些设置将影响整个项目的AI功能表现。"
          type="info"
          showIcon
          style={{ marginBottom: 24 }}
        />

        <Row gutter={24}>
          <Col span={12}>
            <Form.Item
              name="enabled"
              label="AI功能总开关"
              valuePropName="checked"
            >
              <Switch checkedChildren="开启" unCheckedChildren="关闭" />
            </Form.Item>

            <Form.Item
              name="cache_duration"
              label="AI缓存时长"
              tooltip="AI响应结果的缓存时间，可以提高响应速度"
            >
              <Select>
                <Option value="10min">10分钟</Option>
                <Option value="1hour">1小时</Option>
                <Option value="6hour">6小时</Option>
              </Select>
            </Form.Item>

            <Form.Item
              name="insights_enabled"
              label="AI洞察功能"
              valuePropName="checked"
              tooltip="开启后AI会自动分析数据并提供业务洞察"
            >
              <Switch checkedChildren="开启" unCheckedChildren="关闭" />
            </Form.Item>

            <Form.Item
              name="suggestions_enabled"
              label="AI建议功能"
              valuePropName="checked"
              tooltip="开启后AI会根据数据提供优化建议"
            >
              <Switch checkedChildren="开启" unCheckedChildren="关闭" />
            </Form.Item>

            <Form.Item
              name="auto_analysis"
              label="自动分析"
              valuePropName="checked"
              tooltip="开启后AI会定期自动分析数据"
            >
              <Switch checkedChildren="开启" unCheckedChildren="关闭" />
            </Form.Item>
          </Col>

          <Col span={12}>
            <Form.Item
              name="analysis_frequency"
              label="分析频率"
              tooltip="AI自动分析的频率设置"
            >
              <Select>
                <Option value="realtime">实时</Option>
                <Option value="hourly">每小时</Option>
                <Option value="daily">每日</Option>
                <Option value="weekly">每周</Option>
              </Select>
            </Form.Item>

            <Form.Item
              name="max_requests_per_hour"
              label="每小时最大请求数"
              tooltip="限制AI功能的使用频率，防止过度消耗资源"
            >
              <InputNumber
                min={10}
                max={1000}
                step={10}
                style={{ width: '100%' }}
              />
            </Form.Item>

            <Form.Item
              name="enable_data_learning"
              label="数据学习"
              valuePropName="checked"
              tooltip="开启后AI会从历史数据中学习，提高准确性"
            >
              <Switch checkedChildren="开启" unCheckedChildren="关闭" />
            </Form.Item>

            <Form.Item
              name="privacy_mode"
              label="隐私模式"
              valuePropName="checked"
              tooltip="开启后AI不会保存敏感数据，保护隐私安全"
            >
              <Switch checkedChildren="开启" unCheckedChildren="关闭" />
            </Form.Item>
          </Col>
        </Row>

        <Divider />

        <Alert
          message="注意事项"
          description={
            <ul style={{ marginBottom: 0, paddingLeft: 20 }}>
              <li>AI功能需要消耗一定的计算资源，请根据实际需求合理配置</li>
              <li>开启数据学习功能可能会影响系统性能，建议在业务低峰期使用</li>
              <li>隐私模式下AI功能可能会受到一定限制，但能更好地保护数据安全</li>
            </ul>
          }
          type="warning"
          showIcon
          style={{ marginBottom: 24 }}
        />

        <Form.Item>
          <Space>
            <Button
              type="primary"
              icon={<SaveOutlined />}
              onClick={saveAISettings}
              loading={loading}
            >
              保存设置
            </Button>
            <Button
              icon={<ReloadOutlined />}
              onClick={resetSettings}
            >
              重置
            </Button>
          </Space>
        </Form.Item>
      </Form>
    );
  };

  // 标签页配置
  const tabItems = useMemo(() => [
    {
      key: 'theme',
      label: (
        <span>
          <BgColorsOutlined />
          主题设置
        </span>
      ),
      children: activeTab === 'theme' ? renderThemeSettings() : null
    },
    {
      key: 'notification',
      label: (
        <span>
          <BellOutlined />
          通知设置
        </span>
      ),
      children: activeTab === 'notification' ? renderNotificationSettings() : null
    },
    {
      key: 'security',
      label: (
        <span>
          <SecurityScanOutlined />
          安全设置
        </span>
      ),
      children: activeTab === 'security' ? renderSecuritySettings() : null
    },
    {
      key: 'general',
      label: (
        <span>
          <GlobalOutlined />
          常规设置
        </span>
      ),
      children: activeTab === 'general' ? renderGeneralSettings() : null
    },
    {
      key: 'ai',
      label: (
        <span>
          <RobotOutlined />
          AI设置
        </span>
      ),
      children: activeTab === 'ai' ? renderAISettings() : null
    },
    {
      key: 'third_party_login',
      label: (
        <span>
          <LoginOutlined />
          三方登录
        </span>
      ),
      children: activeTab === 'third_party_login' ? <ThirdPartyLogin /> : null
    }
  ], [activeTab, themeForm, notificationForm, securityForm, generalForm, aiForm]);

  console.log('SystemSettings 组件渲染', { activeTab, tabItems });

  return (
    <div className="system-settings">
      <Card
        title={
          <Space>
            <SettingOutlined />
            <span>系统设置</span>
          </Space>
        }
        className="settings-card"
      >
        <Tabs
          activeKey={activeTab}
          onChange={setActiveTab}
          items={tabItems}
        />
      </Card>
    </div>
  );
};

export default SystemSettings;
