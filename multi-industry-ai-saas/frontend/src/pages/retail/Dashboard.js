import React, { useState, useEffect } from 'react';
import {
  Card,
  Row,
  Col,
  Statistic,
  Typography,
  Table,
  Button,
  Space,
  Tooltip,
  Tag,
  Progress,
  Spin,
  Alert,
  Divider,
  List,
  Avatar
} from 'antd';
import {
  DashboardOutlined,
  ShoppingOutlined,
  ShopOutlined,
  TeamOutlined,
  Bar<PERSON>hartOutlined,
  ReloadOutlined,
  EyeOutlined,
  SettingOutlined,
  RiseOutlined,
  FallOutlined,
  ExclamationCircleOutlined,
  DollarOutlined,
  HddOutlined,
  SolutionOutlined,
  InfoCircleOutlined
} from '@ant-design/icons';
import { useNavigate } from 'react-router-dom';
import { useTenant } from '../../contexts/TenantContext';
import apiService from '../../services/api';
import { Line, Column, Pie } from '@ant-design/plots';

const { Title, Paragraph, Text } = Typography;

const RetailDashboard = () => {
  const navigate = useNavigate();
  const { currentTenant, currentProject } = useTenant();

  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [dashboardData, setDashboardData] = useState({
    summary: {
      totalSales: 0,
      totalOrders: 0,
      totalCustomers: 0,
      totalProducts: 0,
      averageOrderValue: 0,
      inventoryValue: 0,
    },
    salesTrend: [],
    topProducts: [],
    topStores: [],
    inventoryAlerts: [],
    categoryPerformance: [],
    recentOrders: [],
    aiRecommendations: []
  });

  // 加载仪表盘数据
  useEffect(() => {
    fetchDashboardData();
  }, [currentTenant, currentProject]);

  // 获取仪表盘数据
  const fetchDashboardData = async () => {
    try {
      setLoading(true);
      setError(null);

      // 这里应该调用API获取实际数据
      // 为了演示，使用模拟数据
      const mockData = {
        summary: {
          totalSales: 128560.75,
          totalOrders: 1256,
          totalCustomers: 856,
          totalProducts: 1024,
          averageOrderValue: 102.36,
          inventoryValue: 356890.50,
        },
        salesTrend: [
          { date: '2023-06-01', sales: 8560.25 },
          { date: '2023-06-02', sales: 7890.50 },
          { date: '2023-06-03', sales: 9120.75 },
          { date: '2023-06-04', sales: 8450.30 },
          { date: '2023-06-05', sales: 9560.80 },
          { date: '2023-06-06', sales: 10250.40 },
          { date: '2023-06-07', sales: 9870.60 },
          { date: '2023-06-08', sales: 10560.90 },
          { date: '2023-06-09', sales: 11250.30 },
          { date: '2023-06-10', sales: 10890.75 },
          { date: '2023-06-11', sales: 9780.45 },
          { date: '2023-06-12', sales: 10450.80 },
          { date: '2023-06-13', sales: 11780.25 },
          { date: '2023-06-14', sales: 12560.90 },
          { date: '2023-06-15', sales: 13450.60 },
        ],
        topProducts: [
          { id: '1', name: '苹果iPhone 14', category: '电子产品', sales: 25600, profit: 8960, stock: 45 },
          { id: '2', name: '三星Galaxy S23', category: '电子产品', sales: 18900, profit: 6615, stock: 32 },
          { id: '3', name: '耐克运动鞋', category: '服装鞋帽', sales: 15600, profit: 7800, stock: 78 },
          { id: '4', name: '华为MateBook', category: '电子产品', sales: 12800, profit: 3840, stock: 23 },
          { id: '5', name: '阿迪达斯T恤', category: '服装鞋帽', sales: 9800, profit: 4900, stock: 56 },
        ],
        topStores: [
          { id: '1', name: '北京中关村店', region: '华北', sales: 45600, orders: 450, growth: 15.2 },
          { id: '2', name: '上海南京路店', region: '华东', sales: 38900, orders: 380, growth: 12.5 },
          { id: '3', name: '广州天河店', region: '华南', sales: 32600, orders: 320, growth: 8.7 },
          { id: '4', name: '深圳福田店', region: '华南', sales: 28900, orders: 285, growth: 10.2 },
          { id: '5', name: '成都春熙路店', region: '西南', sales: 25600, orders: 250, growth: 9.5 },
        ],
        inventoryAlerts: [
          { id: '1', product: '苹果iPhone 14', category: '电子产品', stock: 5, threshold: 10, status: 'low' },
          { id: '2', name: '华为MateBook', category: '电子产品', stock: 3, threshold: 8, status: 'low' },
          { id: '3', name: '小米手环', category: '电子产品', stock: 0, threshold: 5, status: 'out' },
          { id: '4', name: '联想ThinkPad', category: '电子产品', stock: 2, threshold: 5, status: 'low' },
          { id: '5', name: '耐克跑步鞋', category: '服装鞋帽', stock: 4, threshold: 10, status: 'low' },
        ],
        categoryPerformance: [
          { category: '电子产品', sales: 68500, percentage: 53.3 },
          { category: '服装鞋帽', sales: 32600, percentage: 25.4 },
          { category: '家居用品', sales: 15800, percentage: 12.3 },
          { category: '食品饮料', sales: 8900, percentage: 6.9 },
          { category: '其他', sales: 2760, percentage: 2.1 },
        ],
        recentOrders: [
          { id: '1', customer: '张三', store: '北京中关村店', amount: 5600.50, items: 5, status: 'completed', created_at: '2023-06-15T10:30:00Z' },
          { id: '2', customer: '李四', store: '上海南京路店', amount: 3200.75, items: 3, status: 'processing', created_at: '2023-06-15T09:45:00Z' },
          { id: '3', customer: '王五', store: '广州天河店', amount: 1800.25, items: 2, status: 'completed', created_at: '2023-06-15T08:20:00Z' },
          { id: '4', customer: '赵六', store: '深圳福田店', amount: 4500.80, items: 4, status: 'completed', created_at: '2023-06-14T16:50:00Z' },
          { id: '5', customer: '钱七', store: '成都春熙路店', amount: 2900.60, items: 3, status: 'processing', created_at: '2023-06-14T15:30:00Z' },
        ],
        aiRecommendations: [
          { id: '1', title: '库存优化建议', content: '电子产品类库存过低，建议增加采购量，特别是苹果iPhone 14和华为MateBook。', type: 'warning' },
          { id: '2', title: '销售策略建议', content: '服装鞋帽类销售增长放缓，建议推出促销活动提升销量。', type: 'info' },
          { id: '3', title: '门店表现分析', content: '北京中关村店和上海南京路店表现优异，可复制其成功经验到其他门店。', type: 'success' },
          { id: '4', title: '客户行为洞察', content: '数据显示周末客流量增加30%，建议增加周末促销力度。', type: 'info' },
          { id: '5', title: '产品组合建议', content: '电子产品和服装鞋帽搭配销售效果好，建议优化产品陈列。', type: 'success' },
        ],
      };

      setDashboardData(mockData);
    } catch (error) {
      console.error('获取仪表盘数据失败:', error);
      setError('获取仪表盘数据失败，请稍后重试');
    } finally {
      setLoading(false);
    }
  };

  // 获取状态标签
  const getStatusTag = (status) => {
    switch (status) {
      case 'completed':
        return <Tag color="success">已完成</Tag>;
      case 'processing':
        return <Tag color="processing">处理中</Tag>;
      case 'pending':
        return <Tag color="warning">待处理</Tag>;
      case 'cancelled':
        return <Tag color="error">已取消</Tag>;
      case 'low':
        return <Tag color="warning">库存不足</Tag>;
      case 'out':
        return <Tag color="error">缺货</Tag>;
      default:
        return <Tag>{status}</Tag>;
    }
  };

  // 获取AI建议类型图标
  const getRecommendationIcon = (type) => {
    switch (type) {
      case 'success':
        return <RiseOutlined style={{ color: '#52c41a' }} />;
      case 'warning':
        return <ExclamationCircleOutlined style={{ color: '#faad14' }} />;
      case 'info':
        return <InfoCircleOutlined style={{ color: '#1890ff' }} />;
      default:
        return <InfoCircleOutlined style={{ color: '#1890ff' }} />;
    }
  };

  // 商品表格列
  const productColumns = [
    {
      title: '商品名称',
      dataIndex: 'name',
      key: 'name',
    },
    {
      title: '分类',
      dataIndex: 'category',
      key: 'category',
    },
    {
      title: '销售额',
      dataIndex: 'sales',
      key: 'sales',
      render: (text) => `¥${text.toLocaleString()}`,
      sorter: (a, b) => a.sales - b.sales,
    },
    {
      title: '利润',
      dataIndex: 'profit',
      key: 'profit',
      render: (text) => `¥${text.toLocaleString()}`,
    },
    {
      title: '库存',
      dataIndex: 'stock',
      key: 'stock',
    },
    {
      title: '操作',
      key: 'action',
      render: (_, record) => (
        <Space size="small">
          <Tooltip title="查看详情">
            <Button
              type="text"
              icon={<EyeOutlined />}
              onClick={() => navigate(`/retail/products/${record.id}`)}
            />
          </Tooltip>
        </Space>
      ),
    },
  ];

  // 门店表格列
  const storeColumns = [
    {
      title: '门店名称',
      dataIndex: 'name',
      key: 'name',
    },
    {
      title: '区域',
      dataIndex: 'region',
      key: 'region',
    },
    {
      title: '销售额',
      dataIndex: 'sales',
      key: 'sales',
      render: (text) => `¥${text.toLocaleString()}`,
      sorter: (a, b) => a.sales - b.sales,
    },
    {
      title: '订单数',
      dataIndex: 'orders',
      key: 'orders',
    },
    {
      title: '增长率',
      dataIndex: 'growth',
      key: 'growth',
      render: (text) => (
        <span>
          {text >= 0 ? (
            <RiseOutlined style={{ color: '#52c41a' }} />
          ) : (
            <FallOutlined style={{ color: '#f5222d' }} />
          )}
          {' '}
          {Math.abs(text)}%
        </span>
      ),
    },
    {
      title: '操作',
      key: 'action',
      render: (_, record) => (
        <Space size="small">
          <Tooltip title="查看详情">
            <Button
              type="text"
              icon={<EyeOutlined />}
              onClick={() => navigate(`/retail/stores/${record.id}`)}
            />
          </Tooltip>
        </Space>
      ),
    },
  ];

  // 订单表格列
  const orderColumns = [
    {
      title: '订单号',
      dataIndex: 'id',
      key: 'id',
    },
    {
      title: '客户',
      dataIndex: 'customer',
      key: 'customer',
    },
    {
      title: '门店',
      dataIndex: 'store',
      key: 'store',
    },
    {
      title: '金额',
      dataIndex: 'amount',
      key: 'amount',
      render: (text) => `¥${text.toLocaleString()}`,
    },
    {
      title: '商品数',
      dataIndex: 'items',
      key: 'items',
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (text) => getStatusTag(text),
    },
    {
      title: '创建时间',
      dataIndex: 'created_at',
      key: 'created_at',
      render: (text) => new Date(text).toLocaleString(),
    },
    {
      title: '操作',
      key: 'action',
      render: (_, record) => (
        <Space size="small">
          <Tooltip title="查看详情">
            <Button
              type="text"
              icon={<EyeOutlined />}
              onClick={() => navigate(`/retail/orders/${record.id}`)}
            />
          </Tooltip>
        </Space>
      ),
    },
  ];

  // 销售趋势图配置
  const salesTrendConfig = {
    data: dashboardData.salesTrend,
    xField: 'date',
    yField: 'sales',
    smooth: true,
    point: {
      size: 5,
      shape: 'diamond',
    },
    tooltip: {
      formatter: (datum) => {
        return { name: '销售额', value: `¥${datum.sales.toLocaleString()}` };
      },
    },
    xAxis: {
      title: {
        text: '日期',
      },
    },
    yAxis: {
      title: {
        text: '销售额 (元)',
      },
    },
    meta: {
      sales: {
        alias: '销售额',
      },
      date: {
        alias: '日期',
      },
    },
  };

  // 分类销售图配置
  const categoryPieConfig = {
    data: dashboardData.categoryPerformance.map(item => ({
      type: item.category,
      value: item.sales,
    })),
    angleField: 'value',
    colorField: 'type',
    radius: 0.8,
    label: {
      type: 'outer',
      formatter: (datum, item) => `${datum.type}: ${(item.percent * 100).toFixed(1)}%`,
    },
    tooltip: {
      formatter: (datum) => {
        return { name: datum.type, value: `¥${datum.value.toLocaleString()}` };
      },
    },
    legend: {
      layout: 'vertical',
      position: 'right',
    },
    interactions: [{ type: 'element-active' }],
  };

  // 错误提示
  const errorAlert = error ? (
    <Alert
      message="错误"
      description={error}
      type="error"
      showIcon
      style={{ marginBottom: 16 }}
    />
  ) : null;

  return (
    <div className="retail-dashboard">
      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: 24 }}>
        <div>
          <Title level={2}><DashboardOutlined /> 零售仪表盘</Title>
          <Paragraph>
            {currentProject ? `${currentProject.name} - ` : ''}
            查看销售数据、库存状态和AI分析建议。
          </Paragraph>
        </div>
        <Button
          type="primary"
          icon={<ReloadOutlined />}
          onClick={fetchDashboardData}
          loading={loading}
        >
          刷新数据
        </Button>
      </div>

      {errorAlert}

      <Spin spinning={loading}>
        {/* 统计卡片 */}
        <Row gutter={16}>
          <Col span={4}>
            <Card>
              <Statistic
                title="总销售额"
                value={dashboardData.summary.totalSales}
                precision={2}
                valueStyle={{ color: '#3f8600' }}
                prefix={<DollarOutlined />}
                suffix="元"
              />
            </Card>
          </Col>
          <Col span={4}>
            <Card>
              <Statistic
                title="总订单数"
                value={dashboardData.summary.totalOrders}
                valueStyle={{ color: '#1890ff' }}
                prefix={<ShoppingOutlined />}
              />
            </Card>
          </Col>
          <Col span={4}>
            <Card>
              <Statistic
                title="客户数"
                value={dashboardData.summary.totalCustomers}
                valueStyle={{ color: '#722ed1' }}
                prefix={<TeamOutlined />}
              />
            </Card>
          </Col>
          <Col span={4}>
            <Card>
              <Statistic
                title="商品数"
                value={dashboardData.summary.totalProducts}
                valueStyle={{ color: '#fa8c16' }}
                prefix={<ShoppingOutlined />}
              />
            </Card>
          </Col>
          <Col span={4}>
            <Card>
              <Statistic
                title="平均订单金额"
                value={dashboardData.summary.averageOrderValue}
                precision={2}
                valueStyle={{ color: '#cf1322' }}
                prefix={<BarChartOutlined />}
                suffix="元"
              />
            </Card>
          </Col>
          <Col span={4}>
            <Card>
              <Statistic
                title="库存总值"
                value={dashboardData.summary.inventoryValue}
                precision={2}
                valueStyle={{ color: '#13c2c2' }}
                prefix={<HddOutlined />}
                suffix="元"
              />
            </Card>
          </Col>
        </Row>

        <Divider />

        {/* 图表 */}
        <Row gutter={16} style={{ marginTop: 16 }}>
          <Col span={16}>
            <Card title="销售趋势" className="dashboard-card">
              <div style={{ height: 300 }}>
                <Line {...salesTrendConfig} />
              </div>
            </Card>
          </Col>
          <Col span={8}>
            <Card title="分类销售占比" className="dashboard-card">
              <div style={{ height: 300 }}>
                <Pie {...categoryPieConfig} />
              </div>
            </Card>
          </Col>
        </Row>

        <Divider />

        {/* AI建议 */}
        <Card title="AI分析建议" style={{ marginTop: 16 }}>
          <List
            itemLayout="horizontal"
            dataSource={dashboardData.aiRecommendations}
            renderItem={item => (
              <List.Item>
                <List.Item.Meta
                  avatar={
                    <Avatar icon={getRecommendationIcon(item.type)} />
                  }
                  title={item.title}
                  description={item.content}
                />
              </List.Item>
            )}
          />
        </Card>

        {/* 热销商品 */}
        <Card
          title="热销商品"
          extra={<Button type="link" onClick={() => navigate('/retail/products')}>查看全部</Button>}
          style={{ marginTop: 16 }}
        >
          <Table
            columns={productColumns}
            dataSource={dashboardData.topProducts}
            rowKey="id"
            pagination={false}
          />
        </Card>

        {/* 门店表现 */}
        <Card
          title="门店表现"
          extra={<Button type="link" onClick={() => navigate('/retail/stores')}>查看全部</Button>}
          style={{ marginTop: 16 }}
        >
          <Table
            columns={storeColumns}
            dataSource={dashboardData.topStores}
            rowKey="id"
            pagination={false}
          />
        </Card>

        {/* 库存预警 */}
        <Card
          title={
            <span>
              <ExclamationCircleOutlined style={{ color: '#faad14', marginRight: 8 }} />
              库存预警
            </span>
          }
          extra={<Button type="link" onClick={() => navigate('/retail/inventory')}>查看全部</Button>}
          style={{ marginTop: 16 }}
        >
          <Table
            columns={[
              { title: '商品', dataIndex: 'product', key: 'product' },
              { title: '分类', dataIndex: 'category', key: 'category' },
              { title: '当前库存', dataIndex: 'stock', key: 'stock' },
              { title: '预警阈值', dataIndex: 'threshold', key: 'threshold' },
              { title: '状态', dataIndex: 'status', key: 'status', render: (text) => getStatusTag(text) },
              {
                title: '操作',
                key: 'action',
                render: (_, record) => (
                  <Button type="primary" size="small" onClick={() => navigate('/retail/inventory/replenish')}>
                    补货
                  </Button>
                ),
              },
            ]}
            dataSource={dashboardData.inventoryAlerts}
            rowKey="id"
            pagination={false}
          />
        </Card>

        {/* 最近订单 */}
        <Card
          title="最近订单"
          extra={<Button type="link" onClick={() => navigate('/retail/orders')}>查看全部</Button>}
          style={{ marginTop: 16 }}
        >
          <Table
            columns={orderColumns}
            dataSource={dashboardData.recentOrders}
            rowKey="id"
            pagination={false}
          />
        </Card>
      </Spin>
    </div>
  );
};

export default RetailDashboard;
