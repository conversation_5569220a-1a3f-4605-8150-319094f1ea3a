import request from './request';
import { getProjectId, getTenantId } from './api/httpClient';

/**
 * 获取插件市场列表
 *
 * @param {Object} params 查询参数
 * @returns {Promise<Object>} 插件市场列表
 */
const getPluginMarket = async (params = {}) => {
  try {
    // 使用租户级别的插件市场 API
    const response = await request.get(`/v1/tenant/plugins/market`, { params });

    // 添加SAPI插件
    if (response.success && response.data && response.data.plugins) {
      // 检查是否已经存在SAPI插件
      const sapiExists = response.data.plugins.some(plugin => plugin.code === 'sapi');

      if (!sapiExists) {
        // 添加SAPI插件
        response.data.plugins.push({
          id: "sapi",
          code: "sapi",
          name: "智能助手集成平台",
          description: "智能助手集成平台(SAPI)是一个强大的API集成工具，支持Model Context Protocol(MCP)协议，允许您将系统功能与各类应用程序和服务无缝连接。",
          version: "1.0.0",
          type: "marketplace",
          category: "integration",
          icon_url: null,
          is_installed: true,
          price: 0,
          author: "系统",
          homepage: null,
          features: [
            "API端点管理",
            "MCP服务器管理",
            "API到MCP映射",
            "令牌管理",
            "使用统计"
          ]
        });
      }
    }

    return response;
  } catch (error) {
    console.error('获取插件市场列表失败:', error);
    throw error;
  }
};

/**
 * 获取插件详情
 *
 * @param {string} pluginId 插件ID
 * @returns {Promise<Object>} 插件详情
 */
const getPluginDetail = async (pluginId) => {
  try {
    // 特殊处理SAPI插件
    if (pluginId === 'sapi') {
      return {
        success: true,
        data: {
          id: "sapi",
          code: "sapi",
          name: "智能助手集成平台",
          description: "智能助手集成平台(SAPI)是一个强大的API集成工具，支持Model Context Protocol(MCP)协议，允许您将系统功能与各类应用程序和服务无缝连接。",
          version: "1.0.0",
          type: "marketplace",
          category: "integration",
          icon_url: null,
          is_installed: true,
          price: 0,
          author: "系统",
          homepage: null,
          features: [
            "API端点管理",
            "MCP服务器管理",
            "API到MCP映射",
            "令牌管理",
            "使用统计"
          ]
        }
      };
    }

    // 使用租户级别的API
    const response = await request.get(`/v1/tenant/plugins/market/${pluginId}`);
    return response;
  } catch (error) {
    console.error('获取插件详情失败:', error);
    throw error;
  }
};

/**
 * 购买插件
 *
 * @param {string} pluginId 插件ID
 * @param {Object} purchaseData 购买数据
 * @returns {Promise<Object>} 购买结果
 */
const purchasePlugin = async (pluginId, purchaseData) => {
  try {
    const response = await request.post(`/v1/tenant/plugins/${pluginId}/purchase`, purchaseData);
    return response;
  } catch (error) {
    console.error('购买插件失败:', error);
    throw error;
  }
};

/**
 * 安装插件（租户级别）
 *
 * @param {string} pluginId 插件ID
 * @returns {Promise<Object>} 安装结果
 */
const installPlugin = async (pluginId) => {
  try {
    // 特殊处理SAPI插件
    if (pluginId === 'sapi') {
      return {
        success: true,
        message: 'SAPI插件已默认安装',
        data: {
          plugin_id: 'sapi',
          status: 'active'
        }
      };
    }

    // 使用租户级别的安装API
    const response = await request.post(`/v1/tenant/plugins/market/${pluginId}/install`);
    return response;
  } catch (error) {
    console.error('安装插件失败:', error);
    throw error;
  }
};

/**
 * 卸载插件（租户级别）
 *
 * @param {string} pluginId 插件ID
 * @returns {Promise<Object>} 卸载结果
 */
const uninstallPlugin = async (pluginId) => {
  try {
    // 特殊处理SAPI插件
    if (pluginId === 'sapi') {
      return {
        success: false,
        message: 'SAPI插件是系统核心插件，无法卸载'
      };
    }

    // 使用租户级别的卸载API
    const response = await request.post(`/v1/tenant/plugins/market/${pluginId}/uninstall`);
    return response;
  } catch (error) {
    console.error('卸载插件失败:', error);
    throw error;
  }
};

/**
 * 获取已安装的插件列表（租户级别）
 *
 * @returns {Promise<Object>} 已安装的插件列表
 */
const getInstalledPlugins = async () => {
  try {
    const response = await request.get(`/v1/tenant/plugins/installed`);
    
    // 添加SAPI插件到已安装列表
    if (response.success && response.data) {
      const sapiExists = response.data.some(plugin => plugin.plugin?.code === 'sapi');
      
      if (!sapiExists) {
        response.data.push({
          id: "sapi",
          plugin_id: "sapi",
          status: "active",
          installed_at: new Date().toISOString(),
          settings: {},
          plugin: {
            id: "sapi",
            code: "sapi",
            name: "智能助手集成平台",
            description: "智能助手集成平台(SAPI)",
            icon_url: null,
            category: "integration",
            version: "1.0.0"
          }
        });
      }
    }
    
    return response;
  } catch (error) {
    console.error('获取已安装的插件列表失败:', error);
    throw error;
  }
};

/**
 * 获取插件配置
 *
 * @param {string} pluginId 插件ID
 * @returns {Promise<Object>} 插件配置
 */
const getPluginConfig = async (pluginId) => {
  try {
    const response = await request.get(`/v1/tenant/plugins/${pluginId}/config`);
    return response;
  } catch (error) {
    console.error('获取插件配置失败:', error);
    throw error;
  }
};

/**
 * 更新插件配置
 *
 * @param {string} pluginId 插件ID
 * @param {Object} config 插件配置
 * @returns {Promise<Object>} 更新结果
 */
const updatePluginConfig = async (pluginId, config) => {
  try {
    const response = await request.put(`/v1/tenant/plugins/${pluginId}/config`, config);
    return response;
  } catch (error) {
    console.error('更新插件配置失败:', error);
    throw error;
  }
};

/**
 * 获取插件使用统计
 *
 * @param {string} pluginId 插件ID
 * @param {Object} params 查询参数
 * @returns {Promise<Object>} 使用统计
 */
const getPluginUsageStats = async (pluginId, params = {}) => {
  try {
    const response = await request.get(`/v1/tenant/plugins/${pluginId}/usage`, { params });
    return response;
  } catch (error) {
    console.error('获取插件使用统计失败:', error);
    throw error;
  }
};

export {
  getPluginMarket,
  getPluginDetail,
  purchasePlugin,
  installPlugin,
  uninstallPlugin,
  getInstalledPlugins,
  getPluginConfig,
  updatePluginConfig,
  getPluginUsageStats
};
