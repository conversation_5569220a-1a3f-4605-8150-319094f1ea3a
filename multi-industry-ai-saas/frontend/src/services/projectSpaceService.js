import axios from 'axios';

// API基础URL
const API_URL = process.env.REACT_APP_API_URL || '/api';

// 创建axios实例
const spaceAxios = axios.create({
  baseURL: API_URL,
  timeout: 30000,
});

// 打印baseURL，用于调试
console.log('projectSpaceService baseURL:', spaceAxios.defaults.baseURL);

// 请求拦截器
spaceAxios.interceptors.request.use(
  (config) => {
    // 从localStorage获取token
    const token = localStorage.getItem('token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }

    // 从localStorage获取项目ID
    const projectId = localStorage.getItem('project_id');
    if (projectId) {
      config.headers['X-Project-ID'] = projectId;
    }

    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

/**
 * 获取存储空间使用情况
 * @returns {Promise<Object>} 存储空间使用情况
 */
const getStorageUsage = async () => {
  try {
    // 从localStorage获取项目ID
    const projectId = localStorage.getItem('project_id');
    if (!projectId) {
      throw new Error('未指定项目ID');
    }

    const response = await spaceAxios.get(`/v1/project/${projectId}/space/usage`);
    return response.data;
  } catch (error) {
    console.error('获取存储空间使用情况失败:', error);
    throw error;
  }
};

/**
 * 获取文件列表
 * @param {Object} params 查询参数
 * @returns {Promise<Object>} 文件列表
 */
const getFiles = async (params = {}) => {
  try {
    const response = await spaceAxios.get(`/v1/project/space/files`, { params });
    return response.data;
  } catch (error) {
    console.error('获取文件列表失败:', error);
    throw error;
  }
};

/**
 * 上传文件
 * @param {File} file 文件对象
 * @param {string} folderPath 文件夹路径
 * @param {string} description 文件描述
 * @param {boolean} isPublic 是否公开
 * @param {Function} onProgress 上传进度回调
 * @returns {Promise<Object>} 上传结果
 */
const uploadFile = async (file, folderPath = '/', description = '', isPublic = false, onProgress) => {
  try {
    const formData = new FormData();
    formData.append('file', file);
    formData.append('folder_path', folderPath);
    formData.append('description', description);
    formData.append('is_public', isPublic);

    const response = await spaceAxios.post(`/v1/project/space/upload`, formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      },
      onUploadProgress: onProgress ? (progressEvent) => {
        const percentCompleted = Math.round((progressEvent.loaded * 100) / progressEvent.total);
        onProgress(percentCompleted);
      } : undefined
    });
    return response.data;
  } catch (error) {
    console.error('上传文件失败:', error);
    throw error;
  }
};

/**
 * 创建文件夹
 * @param {string} folderName 文件夹名称
 * @param {string} parentId 父文件夹ID
 * @returns {Promise<Object>} 创建结果
 */
const createFolder = async (folderName, parentId = null) => {
  try {
    const formData = new FormData();
    formData.append('folder_name', folderName);
    if (parentId) {
      formData.append('parent_id', parentId);
    }

    const response = await spaceAxios.post(`/v1/project/space/folders`, formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    });
    return response.data;
  } catch (error) {
    console.error('创建文件夹失败:', error);
    throw error;
  }
};

/**
 * 删除文件
 * @param {string} fileId 文件ID
 * @param {boolean} permanent 是否永久删除
 * @returns {Promise<Object>} 删除结果
 */
const deleteFile = async (fileId, permanent = false) => {
  try {
    const response = await spaceAxios.delete(`/v1/project/space/files/${fileId}`, {
      params: { permanent }
    });
    return response.data;
  } catch (error) {
    console.error('删除文件失败:', error);
    throw error;
  }
};

/**
 * 创建文件分享
 * @param {string} fileId 文件ID
 * @param {number} expiresInDays 过期天数
 * @param {string} password 密码
 * @returns {Promise<Object>} 分享结果
 */
const createFileShare = async (fileId, expiresInDays = null, password = null) => {
  try {
    const formData = new FormData();
    if (expiresInDays !== null) {
      formData.append('expires_in_days', expiresInDays);
    }
    if (password) {
      formData.append('password', password);
    }

    const response = await spaceAxios.post(`/v1/project/space/files/${fileId}/share`, formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    });
    return response.data;
  } catch (error) {
    console.error('创建文件分享失败:', error);
    throw error;
  }
};

/**
 * 获取分享文件信息
 * @param {string} shareToken 分享令牌
 * @param {string} password 密码
 * @returns {Promise<Object>} 分享文件信息
 */
const getSharedFile = async (shareToken, password = null) => {
  try {
    const response = await spaceAxios.get(`/v1/space/share/${shareToken}`, {
      params: password ? { password } : {}
    });
    return response.data;
  } catch (error) {
    console.error('获取分享文件失败:', error);
    throw error;
  }
};

/**
 * 验证分享密码
 * @param {string} shareToken 分享令牌
 * @param {string} password 密码
 * @returns {Promise<Object>} 验证结果
 */
const verifySharePassword = async (shareToken, password) => {
  try {
    const formData = new FormData();
    formData.append('password', password);

    const response = await spaceAxios.post(`/v1/space/share/${shareToken}/verify`, formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    });
    return response.data;
  } catch (error) {
    console.error('验证分享密码失败:', error);
    throw error;
  }
};

/**
 * 获取文件下载URL
 * @param {string} fileId 文件ID
 * @returns {string} 下载URL
 */
const getFileDownloadUrl = (fileId) => {
  return `${spaceAxios.defaults.baseURL}/v1/project/space/files/${fileId}/download`;
};

/**
 * 获取文件缩略图URL
 * @param {string} fileId 文件ID
 * @returns {string} 缩略图URL
 */
const getFileThumbnailUrl = (fileId) => {
  return `${spaceAxios.defaults.baseURL}/v1/project/space/files/${fileId}/thumbnail`;
};

/**
 * 获取分享文件下载URL
 * @param {string} shareToken 分享令牌
 * @param {string} password 密码
 * @returns {string} 下载URL
 */
const getSharedFileDownloadUrl = (shareToken, password = null) => {
  // 使用正确的API路径，确保与后端路由匹配
  const baseUrl = `${spaceAxios.defaults.baseURL}/v1/space/share/${shareToken}/download`;
  return password ? `${baseUrl}?password=${encodeURIComponent(password)}` : baseUrl;
};

/**
 * 获取分享文件缩略图URL
 * @param {string} shareToken 分享令牌
 * @param {string} password 密码
 * @returns {string} 缩略图URL
 */
const getSharedFileThumbnailUrl = (shareToken, password = null) => {
  // 使用正确的API路径，确保与后端路由匹配
  const baseUrl = `${spaceAxios.defaults.baseURL}/v1/space/share/${shareToken}/thumbnail`;
  return password ? `${baseUrl}?password=${encodeURIComponent(password)}` : baseUrl;
};

/**
 * AI识图功能
 * @param {string} fileId 文件ID
 * @param {string} prompt 自定义提示词
 * @returns {Promise<Object>} AI识图结果
 */
const aiRecognizeFile = async (fileId, prompt = null) => {
  try {
    // 从localStorage获取项目ID
    const projectId = localStorage.getItem('project_id');
    if (!projectId) {
      throw new Error('未指定项目ID');
    }

    const requestBody = prompt ? { prompt } : {};
    
    const response = await spaceAxios.post(
      `/v1/project/${projectId}/space/files/${fileId}/ai-recognize`, 
      requestBody
    );
    return response.data;
  } catch (error) {
    console.error('AI识图失败:', error);
    throw error;
  }
};

/**
 * 获取回收站文件列表
 * @param {Object} params - 查询参数
 * @returns {Promise<Object>} 回收站文件列表
 */
const getDeletedFiles = async (params = {}) => {
  try {
    // 从localStorage获取项目ID
    const projectId = localStorage.getItem('project_id');
    if (!projectId) {
      throw new Error('未指定项目ID');
    }

    const response = await spaceAxios.get(`/v1/project/${projectId}/space/recycle-bin`, {
      params: params
    });
    return response.data;
  } catch (error) {
    console.error('获取回收站文件列表失败:', error);
    throw error;
  }
};

/**
 * 从回收站还原文件
 * @param {string} fileId - 文件ID
 * @returns {Promise<Object>} 还原结果
 */
const restoreFile = async (fileId) => {
  try {
    // 从localStorage获取项目ID
    const projectId = localStorage.getItem('project_id');
    if (!projectId) {
      throw new Error('未指定项目ID');
    }

    const response = await spaceAxios.post(`/v1/project/${projectId}/space/recycle-bin/${fileId}/restore`);
    return response.data;
  } catch (error) {
    console.error('还原文件失败:', error);
    throw error;
  }
};

/**
 * 彻底删除文件
 * @param {string} fileId - 文件ID
 * @returns {Promise<Object>} 删除结果
 */
const permanentDeleteFile = async (fileId) => {
  try {
    // 从localStorage获取项目ID
    const projectId = localStorage.getItem('project_id');
    if (!projectId) {
      throw new Error('未指定项目ID');
    }

    const response = await spaceAxios.delete(`/v1/project/${projectId}/space/recycle-bin/${fileId}/permanent`);
    return response.data;
  } catch (error) {
    console.error('彻底删除文件失败:', error);
    throw error;
  }
};

/**
 * 清空回收站
 * @returns {Promise<Object>} 清空结果
 */
const clearRecycleBin = async () => {
  try {
    // 从localStorage获取项目ID
    const projectId = localStorage.getItem('project_id');
    if (!projectId) {
      throw new Error('未指定项目ID');
    }

    const response = await spaceAxios.delete(`/v1/project/${projectId}/space/recycle-bin/clear`);
    return response.data;
  } catch (error) {
    console.error('清空回收站失败:', error);
    throw error;
  }
};

export default {
  getStorageUsage,
  getFiles,
  uploadFile,
  createFolder,
  deleteFile,
  createFileShare,
  getSharedFile,
  verifySharePassword,
  getFileDownloadUrl,
  getFileThumbnailUrl,
  getSharedFileDownloadUrl,
  getSharedFileThumbnailUrl,
  aiRecognizeFile,
  getDeletedFiles,
  restoreFile,
  permanentDeleteFile,
  clearRecycleBin
};
