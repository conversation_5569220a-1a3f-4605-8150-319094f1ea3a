import apiService from './api';

/**
 * AI推荐服务
 * 提供基于AI的业务推荐功能
 */

/**
 * 获取AI推荐设置
 * @returns {Promise<Object>} - AI推荐设置
 */
export const getAISettings = async () => {
  try {
    const response = await apiService.project.settings.getAISettings();
    return response;
  } catch (error) {
    console.error('获取AI推荐设置失败:', error);
    return {
      enabled: true,
      models: ['gpt-4'],
      frequency: 'daily',
      categories: ['business', 'finance', 'inventory', 'purchase', 'operation']
    };
  }
};

/**
 * 获取AI推荐
 * @param {string} category - 推荐类别
 * @param {Object} data - 业务数据
 * @returns {Promise<Array>} - 推荐列表
 */
export const getRecommendations = async (category, data) => {
  try {
    // 获取AI设置
    const settings = await getAISettings();
    
    // 如果AI推荐功能已禁用，则返回空数组
    if (!settings.enabled) {
      return [];
    }
    
    // 如果当前类别不在启用的类别列表中，则返回空数组
    if (!settings.categories.includes(category)) {
      return [];
    }
    
    // 调用API获取推荐
    const response = await apiService.project.dashboard.getAIRecommendations(category, {
      model: settings.models[0],
      data
    });
    
    return response.recommendations || [];
  } catch (error) {
    console.error('获取AI推荐失败:', error);
    return [];
  }
};

/**
 * 获取财务推荐
 * @param {Object} data - 财务数据
 * @returns {Promise<Array>} - 推荐列表
 */
export const getFinanceRecommendations = async (data) => {
  return getRecommendations('finance', data);
};

/**
 * 获取库存推荐
 * @param {Object} data - 库存数据
 * @returns {Promise<Array>} - 推荐列表
 */
export const getInventoryRecommendations = async (data) => {
  return getRecommendations('inventory', data);
};

/**
 * 获取采购推荐
 * @param {Object} data - 采购数据
 * @returns {Promise<Array>} - 推荐列表
 */
export const getPurchaseRecommendations = async (data) => {
  return getRecommendations('purchase', data);
};

/**
 * 获取运营推荐
 * @param {Object} data - 运营数据
 * @returns {Promise<Array>} - 推荐列表
 */
export const getOperationRecommendations = async (data) => {
  return getRecommendations('operation', data);
};

/**
 * 获取业务推荐
 * @param {Object} data - 业务数据
 * @returns {Promise<Array>} - 推荐列表
 */
export const getBusinessRecommendations = async (data) => {
  return getRecommendations('business', data);
};

/**
 * 反馈推荐
 * @param {string} recommendationId - 推荐ID
 * @param {boolean} isHelpful - 是否有帮助
 * @param {string} feedback - 反馈内容
 * @returns {Promise<Object>} - 反馈结果
 */
export const feedbackRecommendation = async (recommendationId, isHelpful, feedback) => {
  try {
    const response = await apiService.project.dashboard.feedbackAIRecommendation({
      recommendation_id: recommendationId,
      is_helpful: isHelpful,
      feedback
    });
    
    return response;
  } catch (error) {
    console.error('反馈AI推荐失败:', error);
    return { success: false };
  }
};

export default {
  getAISettings,
  getRecommendations,
  getFinanceRecommendations,
  getInventoryRecommendations,
  getPurchaseRecommendations,
  getOperationRecommendations,
  getBusinessRecommendations,
  feedbackRecommendation
};
