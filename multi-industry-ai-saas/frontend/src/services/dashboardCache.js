/**
 * 仪表盘数据缓存服务
 * 用于缓存仪表盘数据，减少API请求次数
 */

// 缓存存储
const cache = {
  data: {},
  timestamp: {},
  ttl: 5 * 60 * 1000 // 缓存有效期：5分钟
};

/**
 * 获取缓存数据
 * @param {string} key - 缓存键
 * @returns {Object|null} - 缓存数据或null
 */
export const getCachedData = (key) => {
  if (!key) return null;

  const data = cache.data[key];
  const timestamp = cache.timestamp[key];

  // 检查缓存是否存在且未过期
  if (data && timestamp && Date.now() - timestamp < cache.ttl) {
    return data;
  }

  return null;
};

/**
 * 设置缓存数据
 * @param {string} key - 缓存键
 * @param {Object} data - 缓存数据
 */
export const setCachedData = (key, data) => {
  if (!key || !data) return;

  cache.data[key] = data;
  cache.timestamp[key] = Date.now();
};

/**
 * 清除缓存数据
 * @param {string} key - 缓存键，如果不提供则清除所有缓存
 */
export const clearCachedData = (key) => {
  if (key) {
    delete cache.data[key];
    delete cache.timestamp[key];
  } else {
    cache.data = {};
    cache.timestamp = {};
  }
};

/**
 * 生成缓存键
 * @param {string} dashboardType - 仪表盘类型
 * @param {Object} params - 请求参数
 * @returns {string} - 缓存键
 */
export const generateCacheKey = (dashboardType, params) => {
  if (!dashboardType) return null;

  // 将参数转换为排序后的字符串
  const paramsStr = params
    ? Object.entries(params)
        .filter(([_, value]) => value !== null && value !== undefined)
        .sort(([keyA], [keyB]) => keyA.localeCompare(keyB))
        .map(([key, value]) => `${key}=${value}`)
        .join('&')
    : '';

  return `${dashboardType}${paramsStr ? `?${paramsStr}` : ''}`;
};

/**
 * 带缓存的数据获取函数
 * @param {string} dashboardType - 仪表盘类型
 * @param {Object} params - 请求参数
 * @param {Function} fetchFunction - 获取数据的函数
 * @returns {Promise<Object>} - 数据
 */
export const fetchWithCache = async (dashboardType, params, fetchFunction) => {
  const cacheKey = generateCacheKey(dashboardType, params);
  
  // 尝试从缓存获取数据
  const cachedData = getCachedData(cacheKey);
  if (cachedData) {
    return cachedData;
  }

  // 如果缓存中没有数据，则从API获取
  const data = await fetchFunction(params);
  
  // 缓存数据
  setCachedData(cacheKey, data);
  
  return data;
};

export default {
  getCachedData,
  setCachedData,
  clearCachedData,
  generateCacheKey,
  fetchWithCache
};
