import api from '../httpClient';
import { getProjectId } from '../httpClient';

/**
 * 获取到货确认列表
 * @param {Object} params - 查询参数
 * @returns {Promise<Object>} 到货确认列表
 */
export const getArrivalList = (params = {}) => {
  const projectId = getProjectId();
  return api.request({
    url: `/project/${projectId}/store-operations/arrivals`,
    method: 'GET',
    params
  });
};

/**
 * 获取供应商列表，用于到货确认
 * @param {Object} params - 查询参数
 * @returns {Promise<Object>} 供应商列表
 */
export const getSuppliers = (params = {}) => {
  const projectId = getProjectId();
  return api.request({
    url: `/project/${projectId}/store-operations/suppliers`,
    method: 'GET',
    params
  });
};

/**
 * 获取产品列表，用于到货确认
 * @param {Object} params - 查询参数
 * @returns {Promise<Object>} 产品列表
 */
export const getProducts = (params = {}) => {
  const projectId = getProjectId();
  return api.request({
    url: `/project/${projectId}/store-operations/products`,
    method: 'GET',
    params
  });
};

/**
 * 获取到货确认详情
 * @param {string} arrivalId - 到货确认ID
 * @returns {Promise<Object>} 到货确认详情
 */
export const getArrivalDetail = (arrivalId) => {
  const projectId = getProjectId();
  return api.request({
    url: `/project/${projectId}/store-operations/arrivals/${arrivalId}`,
    method: 'GET'
  });
};

/**
 * 创建到货确认
 * @param {Object} data - 到货确认数据
 * @returns {Promise<Object>} 创建结果
 */
export const createArrival = (data) => {
  const projectId = getProjectId();
  return api.request({
    url: `/project/${projectId}/store-operations/arrivals`,
    method: 'POST',
    data
  });
};

/**
 * 确认到货
 * @param {string} arrivalId - 到货确认ID
 * @param {Object} data - 确认数据
 * @returns {Promise<Object>} 确认结果
 */
export const confirmArrival = (arrivalId, data) => {
  const projectId = getProjectId();
  return api.request({
    url: `/project/${projectId}/store-operations/arrivals/${arrivalId}/confirm`,
    method: 'POST',
    data
  });
};

/**
 * 拒绝到货
 * @param {string} arrivalId - 到货确认ID
 * @param {string} notes - 拒绝原因
 * @returns {Promise<Object>} 拒绝结果
 */
export const rejectArrival = (arrivalId, notes) => {
  const projectId = getProjectId();
  return api.request({
    url: `/project/${projectId}/store-operations/arrivals/${arrivalId}/reject`,
    method: 'POST',
    data: { notes }
  });
};

/**
 * 获取到货成本汇总
 * @param {Object} params - 查询参数
 * @returns {Promise<Object>} 成本汇总
 */
export const getArrivalCostSummary = (params = {}) => {
  const projectId = getProjectId();
  return api.request({
    url: `/project/${projectId}/store-operations/arrivals-cost-summary`,
    method: 'GET',
    params
  });
};

/**
 * 获取日常费用列表
 * @param {Object} params - 查询参数
 * @returns {Promise<Object>} 日常费用列表
 */
export const getExpenseList = (params = {}) => {
  const projectId = getProjectId();
  return api.request({
    url: `/project/${projectId}/store-operations/expenses`,
    method: 'GET',
    params
  });
};

/**
 * 获取日常费用详情
 * @param {string} expenseId - 日常费用ID
 * @returns {Promise<Object>} 日常费用详情
 */
export const getExpenseDetail = (expenseId) => {
  const projectId = getProjectId();
  return api.request({
    url: `/project/${projectId}/store-operations/expenses/${expenseId}`,
    method: 'GET'
  });
};

/**
 * 创建日常费用
 * @param {Object} data - 日常费用数据
 * @returns {Promise<Object>} 创建结果
 */
export const createExpense = (data) => {
  const projectId = getProjectId();
  return api.request({
    url: `/project/${projectId}/store-operations/expenses`,
    method: 'POST',
    data
  });
};

/**
 * 更新日常费用
 * @param {string} expenseId - 日常费用ID
 * @param {Object} data - 更新数据
 * @returns {Promise<Object>} 更新结果
 */
export const updateExpense = (expenseId, data) => {
  const projectId = getProjectId();
  return api.request({
    url: `/project/${projectId}/store-operations/expenses/${expenseId}`,
    method: 'PUT',
    data
  });
};

/**
 * 删除日常费用
 * @param {string} expenseId - 日常费用ID
 * @returns {Promise<Object>} 删除结果
 */
export const deleteExpense = (expenseId) => {
  const projectId = getProjectId();
  return api.request({
    url: `/project/${projectId}/store-operations/expenses/${expenseId}`,
    method: 'DELETE'
  });
};

/**
 * 获取费用统计
 * @param {Object} params - 查询参数
 * @returns {Promise<Object>} 费用统计
 */
export const getExpenseStatistics = (params = {}) => {
  const projectId = getProjectId();
  return api.request({
    url: `/project/${projectId}/store-operations/expenses-statistics`,
    method: 'GET',
    params
  });
};

/**
 * 上传费用凭据
 * @param {FormData} formData - 表单数据
 * @returns {Promise<Object>} 上传结果
 */
export const uploadExpenseReceipt = (formData) => {
  const projectId = getProjectId();
  return api.request({
    url: `/project/${projectId}/store-operations/expenses-upload-receipt`,
    method: 'POST',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  });
};

export default {
  getArrivalList,
  getArrivalDetail,
  createArrival,
  confirmArrival,
  rejectArrival,
  getArrivalCostSummary,
  getSuppliers,
  getProducts,
  getExpenseList,
  getExpenseDetail,
  createExpense,
  updateExpense,
  deleteExpense,
  getExpenseStatistics,
  uploadExpenseReceipt
};
