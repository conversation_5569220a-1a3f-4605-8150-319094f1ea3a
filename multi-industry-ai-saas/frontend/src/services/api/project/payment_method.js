import api from '../httpClient';
import { getProjectId } from '../httpClient';

/**
 * 项目-支付方式API
 */
const paymentMethod = {
  /**
   * 获取支付方式列表
   * @param {Object} params - 查询参数
   * @returns {Promise} - 支付方式列表
   */
  getList: async (params = {}) => {
    const projectId = getProjectId();
    if (!projectId) {
      throw new Error('未指定项目ID');
    }
    return api.get(`/project/${projectId}/basic/sales/payment-methods`, { params });
  },

  /**
   * 获取支付方式详情
   * @param {string} id - 支付方式ID
   * @returns {Promise} - 支付方式详情
   */
  getDetail: async (id) => {
    const projectId = getProjectId();
    if (!projectId) {
      throw new Error('未指定项目ID');
    }
    return api.get(`/project/${projectId}/basic/sales/payment-methods/${id}`);
  },

  /**
   * 创建支付方式
   * @param {Object} data - 支付方式数据
   * @returns {Promise} - 创建结果
   */
  create: async (data) => {
    const projectId = getProjectId();
    if (!projectId) {
      throw new Error('未指定项目ID');
    }
    return api.post(`/project/${projectId}/basic/sales/payment-methods`, data);
  },

  /**
   * 更新支付方式
   * @param {string} id - 支付方式ID
   * @param {Object} data - 支付方式数据
   * @returns {Promise} - 更新结果
   */
  update: async (id, data) => {
    const projectId = getProjectId();
    if (!projectId) {
      throw new Error('未指定项目ID');
    }
    return api.put(`/project/${projectId}/basic/sales/payment-methods/${id}`, data);
  },

  /**
   * 删除支付方式
   * @param {string} id - 支付方式ID
   * @returns {Promise} - 删除结果
   */
  delete: async (id) => {
    const projectId = getProjectId();
    if (!projectId) {
      throw new Error('未指定项目ID');
    }
    return api.delete(`/project/${projectId}/basic/sales/payment-methods/${id}`);
  }
};

export default paymentMethod;
