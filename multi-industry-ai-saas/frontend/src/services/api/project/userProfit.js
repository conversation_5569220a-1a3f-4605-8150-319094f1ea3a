import httpClient from '../httpClient';
import request from '../../request';
import { getProjectId } from '../httpClient';

/**
 * 项目-用户API（设置层，仅设置相关接口）
 */
const user = {
  // 获取当前用户个人资料
  getProfile: async () => {
    const projectId = getProjectId();
    if (!projectId) throw new Error('未指定项目ID');
    return request.get(`/project/${projectId}/users/profile`);
  },
  // 更新当前用户个人资料
  updateProfile: async (data) => {
    const projectId = getProjectId();
    if (!projectId) throw new Error('未指定项目ID');
    return request.put(`/project/${projectId}/users/profile`, data);
  },
  // 修改当前用户密码
  changePassword: async (data) => {
    const projectId = getProjectId();
    if (!projectId) throw new Error('未指定项目ID');
    return request.put(`/project/${projectId}/users/password`, data);
  },
  // 上传用户头像
  uploadAvatar: async (formData) => {
    const projectId = getProjectId();
    if (!projectId) throw new Error('未指定项目ID');
    return request.post(`/project/${projectId}/users/avatar`, formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    });
  },
  // 获取用户活动记录
  getActivities: async (params = {}) => {
    const projectId = getProjectId();
    if (!projectId) throw new Error('未指定项目ID');
    return request.get(`/project/${projectId}/users/activities`, { params });
  },
  // 获取第三方登录授权URL (现在使用用户资料接口)
  getThirdPartyAuthUrl: async (platform) => {
    const projectId = getProjectId();
    if (!projectId) throw new Error('未指定项目ID');
    return request.get(`/project/${projectId}/users/bind-url?platform=${platform}`);
  },
  // 绑定第三方账号
  bindThirdParty: async (platform, code) => {
    const projectId = getProjectId();
    if (!projectId) throw new Error('未指定项目ID');
    return request.post(`/project/${projectId}/users/bind/${platform}`, { code });
  },
  // 解绑第三方账号
  unbindThirdParty: async (accountId) => {
    const projectId = getProjectId();
    if (!projectId) throw new Error('未指定项目ID');
    return request.delete(`/project/${projectId}/users/bind/${accountId}`);
  },
  // 获取用户第三方账号列表
  getThirdPartyAccounts: async () => {
    const projectId = getProjectId();
    if (!projectId) throw new Error('未指定项目ID');
    return request.get(`/project/${projectId}/users/bindings`);
  }
};

export default user;