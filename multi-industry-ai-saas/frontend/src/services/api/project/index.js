import api from '../httpClient';
import { getProjectId } from '../httpClient';
import user from './user';
import role from './role';
import store from './store';
import warehouse from './warehouse';
import settings from './settings';
import notification from './notification';
import notifications from './notifications';
import product from './product';
import supplier from './supplier';
import loss from './loss';
import purchaseOrder from './purchase_order';
import salesReport from './sales_report';
import knowledgeBase from './knowledge_base';
import marketingActivity from './marketing_activity';
import routineTask from './routine_task';
import inventory from './inventory';
import businessSettings from './businessSettings';
import operationLogs from './operationLogs';
import channel from './channel';
import paymentMethod from './payment_method';
import finance from './finance';
import ai from './ai';
import dashboard from './dashboard';
import store_operations from './store_operations';
import store_inventory_transfer from './store_inventory_transfer';
import basic from './basic';
import * as spaceApi from './space';

/**
 * 项目API
 */
const project = {
  /**
   * 获取项目信息
   * @returns {Promise} - 项目信息
   */
  getInfo: async () => {
    const projectId = getProjectId();
    if (!projectId) {
      throw new Error('未指定项目ID');
    }
    try {
      return await api.get(`/project/${projectId}`);
    } catch (error) {
      console.error('获取项目信息失败:', error);
      // 返回默认项目信息
      return {
        id: projectId,
        name: localStorage.getItem('project_name') || '默认项目',
        description: '',
        industry_type: 'retail',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      };
    }
  },

  /**
   * 获取项目统计信息
   * @returns {Promise} - 统计信息
   */
  getStats: async () => {
    const projectId = getProjectId();
    if (!projectId) {
      throw new Error('未指定项目ID');
    }
    return api.get(`/project/${projectId}/stats`);
  },

  /**
   * 初始化项目
   * @returns {Promise} - 初始化结果
   */
  initialize: async () => {
    const projectId = getProjectId();
    if (!projectId) {
      throw new Error('未指定项目ID');
    }
    return api.post(`/project/${projectId}/init`);
  },

  user,
  role,
  store,
  warehouse,
  settings,
  notification,
  notifications,
  product,
  supplier,
  loss,
  purchaseOrder,
  salesReport,
  knowledgeBase,
  marketingActivity,
  routineTask,
  inventory,
  businessSettings,
  operationLogs,
  channel,
  paymentMethod,
  finance,
  ai,
  dashboard,
  store_operations,
  store_inventory_transfer,
  basic,
  space: {
    getStorageUsage: spaceApi.getStorageUsage,
    getFiles: spaceApi.getFiles,
    getFileInfo: spaceApi.getFileInfo,
    uploadFile: spaceApi.uploadFile,
    createFolder: spaceApi.createFolder,
    deleteFile: spaceApi.deleteFile,
    createFileShare: spaceApi.createFileShare,
    getSharedFile: spaceApi.getSharedFile,
    verifySharePassword: spaceApi.verifySharePassword,
    getFileDownloadUrl: spaceApi.getFileDownloadUrl,
    getFileThumbnailUrl: spaceApi.getFileThumbnailUrl,
    getSharedFileDownloadUrl: spaceApi.getSharedFileDownloadUrl,
    getSharedFileThumbnailUrl: spaceApi.getSharedFileThumbnailUrl
  },
};

export {
  user,
  role,
  store,
  warehouse,
  settings,
  notification,
  notifications,
  product,
  supplier,
  loss,
  purchaseOrder,
  salesReport,
  knowledgeBase,
  marketingActivity,
  routineTask,
  inventory,
  businessSettings,
  operationLogs,
  channel,
  paymentMethod,
  finance,
  ai,
  dashboard,
  store_operations,
  store_inventory_transfer,
  basic,
  spaceApi as space,
};

export default project;
