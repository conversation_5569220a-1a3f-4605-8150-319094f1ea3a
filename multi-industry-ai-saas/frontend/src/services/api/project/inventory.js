import request from '../request';

// 获取当前项目ID
const getProjectId = () => {
  const projectId = localStorage.getItem('project_id');
  if (projectId) {
    return projectId;
  }

  const userData = JSON.parse(localStorage.getItem('user_data') || '{}');
  if (userData && userData.project_id) {
    localStorage.setItem('project_id', userData.project_id);
    return userData.project_id;
  }

  return null;
};

/**
 * 获取库存项列表
 * @param {Object} params - 查询参数
 * @returns {Promise<Object>} 库存项列表
 */
export const getItems = (params = {}) => {
  const projectId = getProjectId();
  return request({
    url: `/v1/project/${projectId}/inventories/items`,
    method: 'GET',
    params
  });
};

/**
 * 获取单个库存项详情
 * @param {string} id - 库存项ID
 * @returns {Promise<Object>} 库存项详情
 */
export const getItem = (id) => {
  const projectId = getProjectId();
  return request({
    url: `/v1/project/${projectId}/inventories/items/${id}`,
    method: 'GET'
  });
};

/**
 * 添加库存项
 * @param {Object} data - 库存项数据
 * @returns {Promise<Object>} 添加结果
 */
export const addItem = (data) => {
  const projectId = getProjectId();
  return request({
    url: `/v1/project/${projectId}/inventories/items`,
    method: 'POST',
    data
  });
};

/**
 * 更新库存项
 * @param {string} id - 库存项ID
 * @param {Object} data - 更新数据
 * @returns {Promise<Object>} 更新结果
 */
export const updateItem = (id, data) => {
  const projectId = getProjectId();
  return request({
    url: `/v1/project/${projectId}/inventories/items/${id}`,
    method: 'PUT',
    data
  });
};

/**
 * 删除库存项
 * @param {string} id - 库存项ID
 * @returns {Promise<Object>} 删除结果
 */
export const deleteItem = (id) => {
  const projectId = getProjectId();
  return request({
    url: `/v1/project/${projectId}/inventories/items/${id}`,
    method: 'DELETE'
  });
};

/**
 * 获取库存项历史记录
 * @param {string} id - 库存项ID
 * @returns {Promise<Object>} 历史记录
 */
export const getItemHistory = (id) => {
  const projectId = getProjectId();
  return request({
    url: `/v1/project/${projectId}/inventories/items/${id}/history`,
    method: 'GET'
  });
};

/**
 * 上传库存表
 * @param {FormData} formData - 表单数据
 * @param {Object} options - 请求选项
 * @returns {Promise<Object>} 上传结果
 */
export const uploadSheet = (formData, options = {}) => {
  const projectId = getProjectId();
  return request({
    url: `/v1/project/${projectId}/inventories/upload`,
    method: 'POST',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data'
    },
    ...options
  });
};

/**
 * 使用AI智能处理预览库存表上传数据
 * @param {Object} params - 预览参数，包含AI处理选项
 * @returns {Promise} - 预览结果
 */
export const previewUploadWithAi = async (params) => {
  const projectId = getProjectId();
  if (!projectId) {
    throw new Error('未指定项目ID');
  }
  
  console.log('库存表AI预览原始参数:', params);
  
  // 过滤掉undefined和null的参数，但保留空字符串和空数组
  const queryParams = {};
  Object.keys(params).forEach(key => {
    const value = params[key];
    if (value !== undefined && value !== null) {
      // 特殊处理数组参数
      if (Array.isArray(value)) {
        queryParams[key] = value.length > 0 ? JSON.stringify(value) : '[]';
      } else {
        queryParams[key] = value;
      }
    }
  });
  
  console.log('库存表AI预览处理后参数:', queryParams);
  
  // 使用POST方式传递参数
  return request({
    url: `/v1/project/${projectId}/inventories/preview-upload-ai`,
    method: 'POST',
    params: queryParams
  });
};

/**
 * 确认导入库存表
 * @param {Object} data - 确认数据
 * @returns {Promise<Object>} 确认结果
 */
export const confirmImport = (data) => {
  const projectId = getProjectId();
  return request({
    url: `/v1/project/${projectId}/inventories/confirm-import`,
    method: 'POST',
    data
  });
};

/**
 * 取消导入库存表
 * @returns {Promise<Object>} 取消结果
 */
export const cancelImport = () => {
  const projectId = getProjectId();
  return request({
    url: `/v1/project/${projectId}/inventories/cancel-import`,
    method: 'POST'
  });
};

/**
 * 获取库存表模板URL
 * @returns {string} 模板URL
 */
export const getTemplateUrl = () => {
  const projectId = getProjectId();
  return `/api/v1/project/${projectId}/inventories/template`;
};

/**
 * 库存转移
 * @param {Object} data - 转移数据
 * @returns {Promise<Object>} 转移结果
 */
export const transferItems = (data) => {
  const projectId = getProjectId();
  return request({
    url: `/v1/project/${projectId}/inventories/transfer`,
    method: 'POST',
    data
  });
};

/**
 * 获取库存调拨列表
 * @param {Object} params - 查询参数
 * @returns {Promise<Object>} 库存调拨列表
 */
export const getTransferList = (params = {}) => {
  const projectId = getProjectId();
  return request({
    url: `/v1/project/${projectId}/inventory-transfers`,
    method: 'GET',
    params
  });
};

/**
 * 创建库存调拨
 * @param {Object} data - 调拨数据
 * @returns {Promise<Object>} 创建结果
 */
export const createTransfer = (data) => {
  const projectId = getProjectId();
  return request({
    url: `/v1/project/${projectId}/inventory-transfers`,
    method: 'POST',
    data
  });
};

/**
 * 获取调拨详情
 * @param {string} id - 调拨ID
 * @returns {Promise<Object>} 调拨详情
 */
export const getTransferDetail = (id) => {
  const projectId = getProjectId();
  return request({
    url: `/v1/project/${projectId}/inventory-transfers/${id}`,
    method: 'GET'
  });
};

/**
 * 上传调拨表
 * @param {FormData} formData - 表单数据
 * @returns {Promise<Object>} 上传结果
 */
export const uploadTransferSheet = (formData) => {
  const projectId = getProjectId();
  return request({
    url: `/v1/project/${projectId}/inventory-transfers/upload`,
    method: 'POST',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  });
};

/**
 * 获取库存预警列表
 * @param {string} projectId - 项目ID
 * @param {Object} params - 查询参数
 * @returns {Promise<Object>} 库存预警列表
 */
export const getAlerts = (projectId, params = {}) => {
  // 如果没有提供项目ID，则使用localStorage中的项目ID
  if (!projectId) {
    projectId = getProjectId();
  }

  return request({
    url: `/v1/project/${projectId}/inventory/alerts`,
    method: 'GET',
    params
  });
};

/**
 * 获取库存预警摘要
 * @param {string} projectId - 项目ID
 * @returns {Promise<Object>} 库存预警摘要
 */
export const getAlertSummary = (projectId) => {
  // 如果没有提供项目ID，则使用localStorage中的项目ID
  if (!projectId) {
    projectId = getProjectId();
  }

  return request({
    url: `/v1/project/${projectId}/inventory/alerts/summary`,
    method: 'GET'
  });
};

// ========== 月度盘点相关API ==========

/**
 * 获取月度盘点列表
 * @param {Object} params - 查询参数
 * @returns {Promise} - 盘点列表
 */
export const getMonthlyInventoryList = (params = {}) => {
  const projectId = getProjectId();
  return request({
    url: `/project/${projectId}/inventory/monthly`,
    method: 'GET',
    params
  });
};

/**
 * 获取月度盘点详情
 * @param {string} id - 盘点ID
 * @returns {Promise} - 盘点详情
 */
export const getMonthlyInventoryDetail = (id) => {
  const projectId = getProjectId();
  return request({
    url: `/project/${projectId}/inventory/monthly/${id}`,
    method: 'GET'
  });
};

/**
 * 创建月度盘点记录
 * @param {Object} data - 盘点数据
 * @returns {Promise} - 创建结果
 */
export const createMonthlyInventory = (data) => {
  const projectId = getProjectId();
  return request({
    url: `/project/${projectId}/inventory/monthly`,
    method: 'POST',
    data
  });
};

/**
 * 批量创建月度盘点记录
 * @param {Array} dataList - 盘点数据数组
 * @returns {Promise} - 批量创建结果
 */
export const batchCreateMonthlyInventory = (dataList) => {
  const projectId = getProjectId();
  return request({
    url: `/project/${projectId}/inventory/monthly/batch`,
    method: 'POST',
    data: { items: dataList }
  });
};

/**
 * 更新月度盘点记录
 * @param {string} id - 盘点ID
 * @param {Object} data - 盘点数据
 * @returns {Promise} - 更新结果
 */
export const updateMonthlyInventory = (id, data) => {
  const projectId = getProjectId();
  return request({
    url: `/project/${projectId}/inventory/monthly/${id}`,
    method: 'PUT',
    data
  });
};

/**
 * 删除月度盘点记录
 * @param {string} id - 盘点ID
 * @returns {Promise} - 删除结果
 */
export const deleteMonthlyInventory = (id) => {
  const projectId = getProjectId();
  return request({
    url: `/project/${projectId}/inventory/monthly/${id}`,
    method: 'DELETE'
  });
};

/**
 * 批量上传月度盘点表
 * @param {FormData} formData - 包含盘点表文件的表单数据
 * @returns {Promise} - 上传结果
 */
export const uploadMonthlyInventory = (formData) => {
  const projectId = getProjectId();
  return request({
    url: `/project/${projectId}/inventory/monthly/upload`,
    method: 'POST',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  });
};

/**
 * 提交月度盘点到财务审核
 * @param {string} id - 盘点ID
 * @returns {Promise} - 提交结果
 */
export const submitMonthlyInventoryToFinance = (id) => {
  const projectId = getProjectId();
  return request({
    url: `/project/${projectId}/inventory/monthly/${id}/submit`,
    method: 'POST'
  });
};

/**
 * 撤回月度盘点提交
 * @param {string} id - 盘点ID
 * @returns {Promise} - 撤回结果
 */
export const withdrawMonthlyInventory = (id) => {
  const projectId = getProjectId();
  return request({
    url: `/project/${projectId}/inventory/monthly/${id}/withdraw`,
    method: 'POST'
  });
};

/**
 * 下载月度盘点模板
 * @returns {Promise<Blob>} 模板文件
 */
export const downloadMonthlyInventoryTemplate = () => {
  const projectId = getProjectId();
  return request({
    url: `/project/${projectId}/inventory/monthly/download-template`,
    method: 'GET',
    responseType: 'blob'
  });
};

/**
 * 导出月度盘点数据
 * @param {Object} params - 查询参数
 * @returns {Promise} - 导出结果
 */
export const exportMonthlyInventory = (params = {}) => {
  const projectId = getProjectId();
  return request({
    url: `/project/${projectId}/inventory/monthly/export`,
    method: 'GET',
    params,
    responseType: 'blob'
  });
};

/**
 * 使用AI智能处理预览月度盘点上传数据
 * @param {Object} params - 预览参数，包含AI处理选项
 * @returns {Promise} - 预览结果
 */
export const previewMonthlyInventoryWithAi = async (params) => {
  const projectId = getProjectId();
  if (!projectId) {
    throw new Error('未指定项目ID');
  }
  
  console.log('月度盘点AI预览原始参数:', params);
  
  // 过滤掉undefined和null的参数
  const queryParams = {};
  Object.keys(params).forEach(key => {
    const value = params[key];
    if (value !== undefined && value !== null) {
      queryParams[key] = value;
    }
  });
  
  console.log('月度盘点AI预览处理后参数:', queryParams);
  
  return request({
    url: `/project/${projectId}/inventory/monthly/preview-upload-ai`,
    method: 'POST',
    params: queryParams
  });
};

/**
 * 确认导入月度盘点AI处理结果
 * @param {Object} data - 确认数据
 * @returns {Promise<Object>} 确认结果
 */
export const confirmMonthlyInventoryImport = (data) => {
  const projectId = getProjectId();
  return request({
    url: `/project/${projectId}/inventory/monthly/confirm-import`,
    method: 'POST',
    data
  });
};

// ========== 库存盘点相关API ==========

/**
 * 获取库存盘点记录列表
 * @param {Object} params - 查询参数
 * @returns {Promise} - 盘点记录列表
 */
export const getCheckRecords = (params = {}) => {
  const projectId = getProjectId();
  return request({
    url: `/v1/project/${projectId}/inventory-checks/checks`,
    method: 'GET',
    params
  });
};

/**
 * 获取库存盘点记录详情
 * @param {string} id - 盘点记录ID
 * @returns {Promise} - 盘点记录详情
 */
export const getCheckRecord = (id) => {
  const projectId = getProjectId();
  return request({
    url: `/v1/project/${projectId}/inventory-checks/checks/${id}`,
    method: 'GET'
  });
};

/**
 * 创建库存盘点记录
 * @param {Object} data - 盘点记录数据
 * @returns {Promise} - 创建结果
 */
export const createCheckRecord = (data) => {
  const projectId = getProjectId();
  return request({
    url: `/v1/project/${projectId}/inventory-checks/checks`,
    method: 'POST',
    data
  });
};

/**
 * 更新库存盘点记录
 * @param {string} id - 盘点记录ID
 * @param {Object} data - 更新数据
 * @returns {Promise} - 更新结果
 */
export const updateCheckRecord = (id, data) => {
  const projectId = getProjectId();
  return request({
    url: `/v1/project/${projectId}/inventory-checks/checks/${id}`,
    method: 'PUT',
    data
  });
};

/**
 * 删除库存盘点记录
 * @param {string} id - 盘点记录ID
 * @returns {Promise} - 删除结果
 */
export const deleteCheckRecord = (id) => {
  const projectId = getProjectId();
  return request({
    url: `/v1/project/${projectId}/inventory-checks/checks/${id}`,
    method: 'DELETE'
  });
};

/**
 * 确认库存盘点记录
 * @param {string} id - 盘点记录ID
 * @returns {Promise} - 确认结果
 */
export const confirmCheckRecord = (id) => {
  const projectId = getProjectId();
  return request({
    url: `/v1/project/${projectId}/inventory-checks/checks/${id}/confirm`,
    method: 'POST'
  });
};

// ========== 导出所有API ==========
export default {
  getItems,
  getItem,
  addItem,
  updateItem,
  deleteItem,
  getItemHistory,
  uploadSheet,
  confirmImport,
  cancelImport,
  getTemplateUrl,
  transferItems,
  getAlerts,
  getAlertSummary,
  getTransferList,
  createTransfer,
  getTransferDetail,
  uploadTransferSheet,
  // 月度盘点相关
  getMonthlyInventoryList,
  getMonthlyInventoryDetail,
  createMonthlyInventory,
  batchCreateMonthlyInventory,
  updateMonthlyInventory,
  deleteMonthlyInventory,
  uploadMonthlyInventory,
  submitMonthlyInventoryToFinance,
  withdrawMonthlyInventory,
  downloadMonthlyInventoryTemplate,
  exportMonthlyInventory,
  // 库存盘点相关
  getCheckRecords,
  getCheckRecord,
  createCheckRecord,
  updateCheckRecord,
  deleteCheckRecord,
  confirmCheckRecord,
  previewMonthlyInventoryWithAi,
  confirmMonthlyInventoryImport
};
