/**
 * 确认预览数据并创建订单
 * @param {string} taskId - 任务ID
 * @param {Object} previewData - 预览数据
 * @returns {Promise<Object>} 确认结果
 */
export const confirmPreview = (taskId, previewData) => {
  const projectId = getProjectId();
  
  console.log('confirmPreview调用参数:', { taskId, previewData });  // 调试日志
  
  return api.request({
    url: `/project/${projectId}/purchase-orders/confirm-preview/${taskId}`,
    method: 'POST',
    data: previewData  // 直接传递预览数据对象
  });
}; 