import api from '../httpClient';
import { getProjectId } from '../httpClient';
import dayjs from 'dayjs';

/**
 * 项目-销售渠道API
 */
const channel = {
  /**
   * 获取销售渠道列表
   * @param {Object} params - 查询参数
   * @returns {Promise} - 销售渠道列表
   */
  getList: async (params = {}) => {
    const projectId = getProjectId();
    if (!projectId) {
      throw new Error('未指定项目ID');
    }
    return api.get(`/project/${projectId}/basic/sales/channels`, { params });
  },

  /**
   * 获取销售渠道详情
   * @param {string} id - 销售渠道ID
   * @returns {Promise} - 销售渠道详情
   */
  getDetail: async (id) => {
    const projectId = getProjectId();
    if (!projectId) {
      throw new Error('未指定项目ID');
    }
    return api.get(`/project/${projectId}/basic/sales/channels/${id}`);
  },

  /**
   * 创建销售渠道
   * @param {Object} data - 销售渠道数据
   * @returns {Promise} - 创建结果
   */
  create: async (data) => {
    const projectId = getProjectId();
    if (!projectId) {
      throw new Error('未指定项目ID');
    }
    return api.post(`/project/${projectId}/basic/sales/channels`, data);
  },

  /**
   * 更新销售渠道
   * @param {string} id - 销售渠道ID
   * @param {Object} data - 销售渠道数据
   * @returns {Promise} - 更新结果
   */
  update: async (id, data) => {
    const projectId = getProjectId();
    if (!projectId) {
      throw new Error('未指定项目ID');
    }
    return api.put(`/project/${projectId}/basic/sales/channels/${id}`, data);
  },

  /**
   * 删除销售渠道
   * @param {string} id - 销售渠道ID
   * @returns {Promise} - 删除结果
   */
  delete: async (id) => {
    const projectId = getProjectId();
    if (!projectId) {
      throw new Error('未指定项目ID');
    }
    return api.delete(`/project/${projectId}/basic/sales/channels/${id}`);
  },

  /**
   * 获取渠道订单列表
   * @param {Object} params - 查询参数
   * @returns {Promise} - 渠道订单列表
   */
  getOrders: async (params = {}) => {
    const projectId = getProjectId();
    if (!projectId) {
      throw new Error('未指定项目ID');
    }

    // 由于后端暂未实现渠道订单API，返回空数组
    return {
      items: [],
      total: 0,
      success: true
    };
  }
};

export default channel;
