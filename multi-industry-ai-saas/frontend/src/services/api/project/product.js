import api from '../httpClient';
import { getProjectId } from '../httpClient';

/**
 * 项目-产品管理API
 */
const productAPI = {
  /**
   * 获取产品列表
   * @param {string} projectId - 项目ID
   * @param {Object} params - 查询参数
   * @returns {Promise} - 产品列表
   */
  getProducts: (projectId, params = {}) => {
    return api.get(`/project/${projectId}/products`, { params });
  },

  /**
   * 获取产品列表（兼容旧方法名）
   * @param {Object} params - 查询参数
   * @returns {Promise} - 产品列表
   */
  getList: async (params = {}) => {
    const projectId = getProjectId();
    if (!projectId) {
      throw new Error('未指定项目ID');
    }
    return api.get(`/project/${projectId}/products`, { params });
  },

  /**
   * 获取分类列表
   * @param {Object} params - 查询参数
   * @returns {Promise} - 分类列表
   */
  getCategories: async (params = {}) => {
    const projectId = getProjectId();
    if (!projectId) {
      throw new Error('未指定项目ID');
    }
    return api.get(`/project/${projectId}/products/categories`, { params });
  },

  /**
   * 获取品牌列表
   * @param {Object} params - 查询参数
   * @returns {Promise} - 品牌列表
   */
  getBrands: async (params = {}) => {
    const projectId = getProjectId();
    if (!projectId) {
      throw new Error('未指定项目ID');
    }
    return api.get(`/project/${projectId}/products/brands`, { params });
  },

  /**
   * 获取产品详情
   * @param {string} projectId - 项目ID
   * @param {string} productId - 产品ID
   * @returns {Promise} - 产品详情
   */
  getProduct: (projectId, productId) => {
    return api.get(`/project/${projectId}/products/${productId}`);
  },

  /**
   * 创建产品（兼容旧方法名）
   * @param {Object} data - 产品数据
   * @returns {Promise} - 创建结果
   */
  create: async (data) => {
    const projectId = getProjectId();
    if (!projectId) {
      throw new Error('未指定项目ID');
    }
    return api.post(`/project/${projectId}/products`, data);
  },

  /**
   * 创建产品
   * @param {string} projectId - 项目ID
   * @param {Object} data - 产品数据
   * @returns {Promise} - 创建结果
   */
  createProduct: (projectId, data) => {
    return api.post(`/project/${projectId}/products`, data);
  },

  /**
   * 更新产品
   * @param {string} projectId - 项目ID
   * @param {string} productId - 产品ID
   * @param {Object} data - 产品数据
   * @returns {Promise} - 更新结果
   */
  updateProduct: (projectId, productId, data) => {
    return api.put(`/project/${projectId}/products/${productId}`, data);
  },

  /**
   * 删除产品
   * @param {string} projectId - 项目ID
   * @param {string} productId - 产品ID
   * @returns {Promise} - 删除结果
   */
  deleteProduct: (projectId, productId) => {
    return api.delete(`/project/${projectId}/products/${productId}`);
  },

  /**
   * 批量导入产品
   * @param {string} projectId - 项目ID
   * @param {File} file - 产品文件
   * @returns {Promise} - 导入结果
   */
  batchImport: (projectId, file) => {
    const formData = new FormData();
    formData.append('file', file);
    return api.post(`/project/${projectId}/products/batch-import`, formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
  },

  /**
   * 导出产品
   * @param {string} projectId - 项目ID
   * @param {Object} params - 查询参数
   * @returns {Promise} - 导出结果
   */
  exportProducts: (projectId, params = {}) => {
    return api.get(`/project/${projectId}/products/export`, { params });
  },

  /**
   * 获取商品合并建议
   * @param {string} projectId - 项目ID
   * @param {Object} params - 查询参数
   * @returns {Promise} - 合并建议列表
   */
  getMergeSuggestions: (projectId, params = {}) => {
    return api.get(`/project/${projectId}/products/merge-suggestions`, { params });
  },

  /**
   * 合并商品
   * @param {string} projectId - 项目ID
   * @param {Object} data - 合并数据
   * @returns {Promise} - 合并结果
   */
  mergeProducts: (projectId, data) => {
    return api.post(`/project/${projectId}/products/merge`, data);
  },

  /**
   * 获取商品变体
   * @param {string} projectId - 项目ID
   * @param {string} productGroupId - 商品分组ID
   * @returns {Promise} - 变体列表
   */
  getVariants: (projectId, productGroupId) => {
    return api.get(`/project/${projectId}/products/variants/${productGroupId}`);
  },

  /**
   * 创建商品变体
   * @param {string} projectId - 项目ID
   * @param {Object} data - 变体数据
   * @returns {Promise} - 创建结果
   */
  createVariant: (projectId, data) => {
    return api.post(`/project/${projectId}/products/create-variant`, data);
  },

  /**
   * 品牌管理
   */
  brands: {
    /**
     * 获取品牌列表
     * @param {string} projectId - 项目ID
     * @param {Object} params - 查询参数
     * @returns {Promise} - 品牌列表
     */
    list: (projectId, params = {}) => {
      return api.get(`/project/${projectId}/products/brands`, { params });
    },

    /**
     * 创建品牌
     * @param {string} projectId - 项目ID
     * @param {Object} data - 品牌数据
     * @returns {Promise} - 创建结果
     */
    create: (projectId, data) => {
      return api.post(`/project/${projectId}/products/brands`, data);
    },

    /**
     * 更新品牌
     * @param {string} projectId - 项目ID
     * @param {string} brandId - 品牌ID
     * @param {Object} data - 品牌数据
     * @returns {Promise} - 更新结果
     */
    update: (projectId, brandId, data) => {
      return api.put(`/project/${projectId}/products/brands/${brandId}`, data);
    },

    /**
     * 删除品牌
     * @param {string} projectId - 项目ID
     * @param {string} brandId - 品牌ID
     * @returns {Promise} - 删除结果
     */
    delete: (projectId, brandId) => {
      return api.delete(`/project/${projectId}/products/brands/${brandId}`);
    },
  },

  /**
   * 分类管理
   */
  categories: {
    /**
     * 获取分类列表
     * @param {string} projectId - 项目ID
     * @param {Object} params - 查询参数
     * @returns {Promise} - 分类列表
     */
    list: (projectId, params = {}) => {
      return api.get(`/project/${projectId}/products/categories`, { params });
    },

    /**
     * 创建分类
     * @param {string} projectId - 项目ID
     * @param {Object} data - 分类数据
     * @returns {Promise} - 创建结果
     */
    create: (projectId, data) => {
      return api.post(`/project/${projectId}/products/categories`, data);
    },

    /**
     * 更新分类
     * @param {string} projectId - 项目ID
     * @param {string} categoryId - 分类ID
     * @param {Object} data - 分类数据
     * @returns {Promise} - 更新结果
     */
    update: (projectId, categoryId, data) => {
      return api.put(`/project/${projectId}/products/categories/${categoryId}`, data);
    },

    /**
     * 删除分类
     * @param {string} projectId - 项目ID
     * @param {string} categoryId - 分类ID
     * @returns {Promise} - 删除结果
     */
    delete: (projectId, categoryId) => {
      return api.delete(`/project/${projectId}/products/categories/${categoryId}`);
    },
  },
};

export default productAPI;
