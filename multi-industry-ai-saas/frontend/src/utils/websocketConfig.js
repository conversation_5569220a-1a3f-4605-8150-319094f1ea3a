/**
 * WebSocket配置工具
 * 处理不同环境下的WebSocket连接配置
 */

/**
 * 获取WebSocket主机地址
 * @returns {string} WebSocket主机地址
 */
export const getWebSocketHost = () => {
  // 检查是否在开发环境
  const isDevelopment = process.env.NODE_ENV === 'development';
  
  if (isDevelopment) {
    // 开发环境配置
    const currentHost = window.location.hostname;
    
    // 如果是localhost或127.0.0.1，使用后端服务端口
    if (currentHost === 'localhost' || currentHost === '127.0.0.1') {
      return `${currentHost}:8001`; // 开发环境后端端口
    }
    
    // 如果是外部IP（如**************），使用当前host但改为后端端口
    if (currentHost === '**************') {
      return `${currentHost}:8001`;
    }
    
    // 对于Docker环境，使用当前host
    return window.location.host;
  }
  
  // 生产环境使用当前host
  return window.location.host;
};

/**
 * 获取WebSocket协议
 * @returns {string} ws或wss
 */
export const getWebSocketProtocol = () => {
  return window.location.protocol === 'https:' ? 'wss:' : 'ws:';
};

/**
 * 构建WebSocket URL
 * @param {string} path - WebSocket路径
 * @param {Object} params - URL参数
 * @returns {string} 完整的WebSocket URL
 */
export const buildWebSocketUrl = (path, params = {}) => {
  const protocol = getWebSocketProtocol();
  const host = getWebSocketHost();
  
  // 构建查询字符串
  const queryParams = new URLSearchParams(params);
  const queryString = queryParams.toString();
  
  return `${protocol}//${host}${path}${queryString ? '?' + queryString : ''}`;
};

/**
 * 检查WebSocket连接是否可用
 * @param {string} url - WebSocket URL
 * @returns {Promise<boolean>} 连接是否可用
 */
export const checkWebSocketAvailability = (url) => {
  return new Promise((resolve) => {
    const ws = new WebSocket(url);
    
    const timeout = setTimeout(() => {
      ws.close();
      resolve(false);
    }, 5000); // 5秒超时
    
    ws.onopen = () => {
      clearTimeout(timeout);
      ws.close();
      resolve(true);
    };
    
    ws.onerror = () => {
      clearTimeout(timeout);
      resolve(false);
    };
  });
};

/**
 * 获取WebSocket重连配置
 * @returns {Object} 重连配置
 */
export const getReconnectConfig = () => {
  return {
    maxAttempts: parseInt(process.env.REACT_APP_WS_MAX_RECONNECT_ATTEMPTS || '5'),
    interval: parseInt(process.env.REACT_APP_WS_RECONNECT_INTERVAL || '3000'),
    maxDelay: 30000, // 最大延迟30秒
  };
};

/**
 * WebSocket连接状态
 */
export const WS_STATES = {
  CONNECTING: 0,
  OPEN: 1,
  CLOSING: 2,
  CLOSED: 3
};

/**
 * 获取连接状态描述
 * @param {number} state - WebSocket状态
 * @returns {string} 状态描述
 */
export const getStateDescription = (state) => {
  switch (state) {
    case WS_STATES.CONNECTING:
      return '连接中';
    case WS_STATES.OPEN:
      return '已连接';
    case WS_STATES.CLOSING:
      return '关闭中';
    case WS_STATES.CLOSED:
      return '已关闭';
    default:
      return '未知状态';
  }
};

/**
 * 网络状态检查
 * @returns {boolean} 是否在线
 */
export const isOnline = () => {
  return navigator.onLine;
};

/**
 * 检查是否为开发环境的热重载WebSocket错误
 * @param {string} url - WebSocket URL
 * @returns {boolean} 是否为热重载相关错误
 */
export const isHMRWebSocketError = (url) => {
  const hmrPatterns = [
    '/sockjs-node',
    '/__webpack_hmr',
    '/ws',
    ':3001/auto', // Vite HMR
    'localhost:3001',
    '127.0.0.1:3001'
  ];
  
  return hmrPatterns.some(pattern => url.includes(pattern));
};

/**
 * 获取WebSocket错误的用户友好消息
 * @param {Error} error - 错误对象
 * @param {string} url - WebSocket URL
 * @returns {string} 用户友好的错误消息
 */
export const getWebSocketErrorMessage = (error, url) => {
  // 如果是热重载相关的错误，返回友好提示
  if (isHMRWebSocketError(url)) {
    return '开发环境热重载连接失败，不影响应用功能使用';
  }
  
  // 检查网络状态
  if (!isOnline()) {
    return '网络连接不可用，请检查网络设置';
  }
  
  // 检查是否为CORS错误
  if (error.message && error.message.includes('CORS')) {
    return '跨域请求被阻止，请联系管理员检查服务器配置';
  }
  
  // 检查是否为连接超时
  if (error.message && (error.message.includes('timeout') || error.message.includes('超时'))) {
    return '连接超时，请检查网络状况或稍后重试';
  }
  
  // 通用错误消息
  return '连接失败，请稍后重试或联系管理员';
};

export default {
  getWebSocketHost,
  getWebSocketProtocol,
  buildWebSocketUrl,
  checkWebSocketAvailability,
  getReconnectConfig,
  WS_STATES,
  getStateDescription,
  isOnline,
  isHMRWebSocketError,
  getWebSocketErrorMessage
}; 