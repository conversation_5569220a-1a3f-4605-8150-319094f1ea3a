import React, { useState, useEffect } from 'react';
import {
  Modal, Table, Button, Input, Space, message, Empty, 
  Tag, Tooltip, Breadcrumb, Row, Col
} from 'antd';
import {
  FolderOutlined, FileOutlined, SearchOutlined, 
  FileImageOutlined, FileTextOutlined, FilePdfOutlined, 
  FileUnknownOutlined, EyeOutlined
} from '@ant-design/icons';
import * as projectSpaceService from '../services/api/project/space';

const { Search } = Input;

/**
 * 文件空间选择器组件
 * @param {Function} onFileSelect - 文件选择回调
 * @param {Array} accept - 接受的文件扩展名列表
 * @param {String} description - 描述文本
 * @param {Boolean} visible - 模态框是否可见
 * @param {Function} onCancel - 取消回调
 */
const FileSpaceSelector = ({ 
  onFileSelect, 
  accept = [], 
  description = '从项目空间选择文件',
  visible = false,
  onCancel
}) => {
  const [modalVisible, setModalVisible] = useState(false);
  const [loading, setLoading] = useState(false);
  const [files, setFiles] = useState([]);
  const [folders, setFolders] = useState([]);
  const [currentFolder, setCurrentFolder] = useState(null);
  const [searchText, setSearchText] = useState('');
  const [selectedFile, setSelectedFile] = useState(null);
  const [breadcrumbs, setBreadcrumbs] = useState([{ name: '根目录', id: null }]);

  // 获取文件图标
  const getFileIcon = (file) => {
    if (file.mime_type.startsWith('image/')) {
      return <FileImageOutlined style={{ color: '#1890ff' }} />;
    } else if (file.mime_type.startsWith('text/') || file.mime_type.includes('document')) {
      return <FileTextOutlined style={{ color: '#52c41a' }} />;
    } else if (file.mime_type === 'application/pdf') {
      return <FilePdfOutlined style={{ color: '#f5222d' }} />;
    } else {
      return <FileUnknownOutlined style={{ color: '#999' }} />;
    }
  };

  // 检查文件是否被接受
  const isFileAccepted = (fileName) => {
    if (accept.length === 0) return true;
    const extension = fileName.toLowerCase().substring(fileName.lastIndexOf('.'));
    return accept.includes(extension);
  };

  // 格式化文件大小
  const formatFileSize = (size) => {
    if (size < 1024) {
      return size + ' B';
    } else if (size < 1024 * 1024) {
      return (size / 1024).toFixed(2) + ' KB';
    } else if (size < 1024 * 1024 * 1024) {
      return (size / (1024 * 1024)).toFixed(2) + ' MB';
    } else {
      return (size / (1024 * 1024 * 1024)).toFixed(2) + ' GB';
    }
  };

  // 获取文件列表
  const fetchFiles = async (folderId = null, search = '') => {
    setLoading(true);
    try {
      const params = {
        folder_id: folderId,
        search: search || null,
        page_size: 50
      };

      const response = await projectSpaceService.getFiles(params);

      if (response.success) {
        setFiles(response.data.files || []);
        setFolders(response.data.folders || []);
        setCurrentFolder(response.data.current_folder);
        
        // 更新面包屑
        if (response.data.current_folder) {
          updateBreadcrumbs(response.data.current_folder);
        } else {
          setBreadcrumbs([{ name: '根目录', id: null }]);
        }
      } else {
        message.error('获取文件列表失败');
      }
    } catch (error) {
      console.error('获取文件列表失败:', error);
      message.error('获取文件列表失败');
    } finally {
      setLoading(false);
    }
  };

  // 更新面包屑
  const updateBreadcrumbs = (folder) => {
    if (!folder || !folder.path) {
      setBreadcrumbs([{ name: '根目录', id: null }]);
      return;
    }

    const pathParts = folder.path.split('/').filter(Boolean);
    const newBreadcrumbs = [{ name: '根目录', id: null }];
    newBreadcrumbs.push({ name: folder.name, id: folder.id });
    setBreadcrumbs(newBreadcrumbs);
  };

  // 处理文件夹点击
  const handleFolderClick = (folderId) => {
    setCurrentFolder({ id: folderId });
    fetchFiles(folderId, searchText);
  };

  // 处理面包屑点击
  const handleBreadcrumbClick = (folderId) => {
    setCurrentFolder(folderId ? { id: folderId } : null);
    fetchFiles(folderId, searchText);
  };

  // 处理搜索
  const handleSearch = (value) => {
    setSearchText(value);
    fetchFiles(currentFolder?.id, value);
  };

  // 处理文件选择
  const handleFileSelect = (file) => {
    if (!isFileAccepted(file.name)) {
      message.warning(`不支持的文件类型，请选择：${accept.join(', ')}`);
      return;
    }
    setSelectedFile(file);
  };

  // 确认选择
  const handleConfirmSelect = () => {
    if (selectedFile) {
      onFileSelect(selectedFile);
      setModalVisible(false);
      setSelectedFile(null);
    } else {
      message.warning('请选择一个文件');
    }
  };

  // 取消选择
  const handleCancelSelect = () => {
    setModalVisible(false);
    setSelectedFile(null);
    if (onCancel) {
      onCancel();
    }
  };

  // 初始化
  useEffect(() => {
    if (modalVisible) {
      fetchFiles();
    }
  }, [modalVisible]);

  // 表格列定义
  const columns = [
    {
      title: '名称',
      dataIndex: 'name',
      key: 'name',
      render: (text, record) => (
        <Space>
          {record.type === 'folder' ? (
            <>
              <FolderOutlined style={{ color: '#1890ff' }} />
              <a onClick={() => handleFolderClick(record.id)}>{text}</a>
            </>
          ) : (
            <>
              {getFileIcon(record)}
              <span 
                style={{ 
                  cursor: isFileAccepted(record.name) ? 'pointer' : 'not-allowed',
                  color: isFileAccepted(record.name) ? '#1890ff' : '#999'
                }}
                onClick={() => handleFileSelect(record)}
              >
                {text}
              </span>
              {!isFileAccepted(record.name) && (
                <Tag color="red" size="small">不支持</Tag>
              )}
            </>
          )}
        </Space>
      )
    },
    {
      title: '大小',
      dataIndex: 'size',
      key: 'size',
      width: 100,
      render: (size, record) => (
        record.type === 'folder' ? '-' : formatFileSize(size)
      )
    },
    {
      title: '类型',
      dataIndex: 'mime_type',
      key: 'mime_type',
      width: 80,
      render: (mimeType, record) => (
        record.type === 'folder' ? '文件夹' : (
          mimeType ? mimeType.split('/')[1].toUpperCase() : 'UNKNOWN'
        )
      )
    },
    {
      title: '修改时间',
      dataIndex: 'updated_at',
      key: 'updated_at',
      width: 150,
      render: (text) => text ? new Date(text).toLocaleString() : '-'
    }
  ];

  // 合并文件夹和文件数据
  const tableData = [
    ...folders.map(folder => ({ ...folder, type: 'folder' })),
    ...files.filter(file => accept.length === 0 || isFileAccepted(file.name))
  ];

  return (
    <div>
      {/* 简化的选择器界面 */}
      <div style={{ 
        border: '1px dashed #d9d9d9', 
        borderRadius: 6, 
        padding: 24, 
        textAlign: 'center',
        cursor: 'pointer',
        transition: 'border-color 0.3s'
      }}
      onMouseEnter={(e) => e.target.style.borderColor = '#1890ff'}
      onMouseLeave={(e) => e.target.style.borderColor = '#d9d9d9'}
      onClick={() => setModalVisible(true)}
      >
        <FolderOutlined style={{ fontSize: 48, color: '#1890ff', marginBottom: 16 }} />
        <div style={{ fontSize: 16, marginBottom: 8 }}>点击选择项目空间文件</div>
        <div style={{ color: '#999', fontSize: 14 }}>{description}</div>
        {accept.length > 0 && (
          <div style={{ color: '#666', fontSize: 12, marginTop: 8 }}>
            支持文件类型：{accept.join(', ')}
          </div>
        )}
      </div>

      {/* 文件选择模态框 */}
      <Modal
        title="选择项目空间文件"
        open={modalVisible}
        onCancel={handleCancelSelect}
        footer={[
          <Button key="cancel" onClick={handleCancelSelect}>
            取消
          </Button>,
          <Button 
            key="confirm" 
            type="primary" 
            onClick={handleConfirmSelect}
            disabled={!selectedFile}
          >
            确认选择
          </Button>
        ]}
        width={900}
      >
        <div>
          {/* 面包屑导航 */}
          <div style={{ marginBottom: 16 }}>
            <Breadcrumb separator=">">
              {breadcrumbs.map((item, index) => (
                <Breadcrumb.Item key={index}>
                  <a onClick={() => handleBreadcrumbClick(item.id)}>{item.name}</a>
                </Breadcrumb.Item>
              ))}
            </Breadcrumb>
          </div>

          {/* 搜索栏 */}
          <Row gutter={16} style={{ marginBottom: 16 }}>
            <Col span={12}>
              <Search
                placeholder="搜索文件"
                onSearch={handleSearch}
                style={{ width: '100%' }}
              />
            </Col>
            <Col span={12}>
              {selectedFile && (
                <div style={{ color: '#1890ff' }}>
                  已选择：{selectedFile.name}
                </div>
              )}
            </Col>
          </Row>

          {/* 文件列表 */}
          <Table
            columns={columns}
            dataSource={tableData}
            rowKey="id"
            loading={loading}
            pagination={false}
            scroll={{ y: 400 }}
            size="small"
            rowSelection={{
              type: 'radio',
              selectedRowKeys: selectedFile ? [selectedFile.id] : [],
              onSelect: (record) => {
                if (record.type !== 'folder') {
                  handleFileSelect(record);
                }
              },
              getCheckboxProps: (record) => ({
                disabled: record.type === 'folder' || !isFileAccepted(record.name)
              })
            }}
            locale={{
              emptyText: <Empty description="暂无文件" />
            }}
          />
        </div>
      </Modal>
    </div>
  );
};

export default FileSpaceSelector; 